# 打印模版管理

## 工作概览
打印模版管理 (入库+出库)
 1. 将后端的html模版移动到前端
  - package_print.html
    - wpoc/front/pre/package/print_package_label
        pc-精准出库扫描、精准预包复核、精准合包复核、面单前置出库扫描功能，打印地址标签（包裹标签） 
        pc-/outbound/package-record-出库管理/打包复核/打包记录管理(打印包裹标签)
  - package_combine_print.html 
        pc - 出库管理-异常管理-补打标签[打印包裹标签]
        pc - 后台管理-基础功能-[包裹打印]
        pc - 出库管理-合包业务-精准预包复核 - [扫描箱号] - 打印包裹标签的样式修改
        pc - 出库管理-合包业务-精准预包复核 - [扫描校验嘛] - 打印包裹标签的样式修改

 2. 整理前端现有的模版,进行管理 (tpl 下面的各种公共的模版, 没有对应的展示, 各种文件夹下面私有的模版)

 3. 对公共打印方法进行改造 (是否需要proxy = 1的长缓存)
      - src/lib/print.js (大量重复, 拓展性不高的方法)

 4. 模版可视化打印

 页面demo参考:http://wms-test01.dotfashion.cn/#/examples/something

 文档参考: 
 1. https://wiki.dotfashion.cn/pages/viewpage.action?pageId=846345530
 2. https://wiki.dotfashion.cn/pages/viewpage.action?pageId=846345530
 3. https://wiki.dotfashion.cn/pages/viewpage.action?pageId=882030078
 4. https://wiki.dotfashion.cn/pages/viewpage.action?pageId=975870437
 5. PDF打印服务 - 调用方式api: https://wiki.dotfashion.cn/pages/viewpage.action?pageId=543722357


## 模版可视化打印
现有的模板都是ejs文件

EJS是嵌入式JavaScript 模板引擎，是node.js 用的

ejs 是模板引擎，React 是用来构建UI的。在React 中要用ejs，嗯，有点怪怪的，但能做到么？

现有的


## 痕迹

怎么预览ejs？
- [如何在react中嵌入后端返回的html文本](https://juejin.cn/post/7029193833510502407)
react中嵌入的是ejs.render

ejs
- [EJS doc](https://ejs.co/)
- [EJS github](https://github.com/mde/ejs)
ejs嵌入式 JavaScript 模板引擎，是node.js 环境下的，基于commonjs

commonjs
- [js模块七日谈](http://huangxuan.me/js-module-7day)
- [how to use react require syntax?](https://stackoverflow.com/questions/33248012/how-to-use-react-require-syntax)

react跟ejs
- [Using React with ejs?](https://stackoverflow.com/questions/39336538/using-react-with-ejs)

ejs转换成jsx
- [How do you Convert from .ejs template to React?](https://forum.freecodecamp.org/t/how-do-you-convert-from-ejs-template-to-react/187881)
- [Moving from EJS to JSX](https://blog.jim-nielsen.com/2019/moving-from-ejs-to-jsx)

lego与ejs
- [PLM系统接入经验](http://lego-master-dev-5828.dev.paas-dev.sheincorp.cn/lego/community/plm)

把ejs当成jsx来用？

可以的，直接把ejs拿来用就行。虽然没找到，但lego系统肯定是做了一层类似于ejs->jsx的转换

```javascript
import tpl from 'xx.ejs'
const htmlText = tpl({})

function List(props) {
  return (
    ...
    <div dangerouslySetInnerHTML={{ __html: htmlText }}></div>
  )
}
```

preview 技术层面就是这样，重点工作还是对当前wms系统中的模板文件统合归纳。

## PPT
准备一些PPT的词儿

管理混乱，打印模板不可见

难以调试

涉及到55个文件 88个引用

据统计，WMS系统中共有54个文件引用到了82个ejs模板文件，其中45个引用的公共ejs，37个是私有ejs。




对资源的管理

现有的问题：
1. 管理混乱
  1. 部分模板存在于后端
  2. wms 前端则涉及到55个文件 88个引用
2. 难以调试

痛点：
1. 太多太乱
2. 看不见
3. 出了问题不好定位

因为太多，所以难找，因为看不见，所以开发时往往新建一个本地的，而不是公共的
上手门槛

项目的目标：
降低开发门槛
整合现有资源，优化管理



对资源的

项目该怎么搞

我应该整理出一个表格了，用来划分范围

- 一阶段干什么
- 二阶段干什么
- 保底方案，就是怎么回退
- 有什么前置工作
  - demo 页面，用来预览

怎么一个预览法？（demo与项目的整体规划）

怎么一个管理法？

管理页面叫 公共打印管理

表格初版弄出来了，接下来弄下初版demo

json 编辑器有点麻烦（现在调研

现在先搭建个预览页面

`htmlText=tpl(fn(state))`

htmlText，用于可视化的html
tpl，对应的ejs
state，用于预览的state
fn，用于对barcode或者img的转换处理

后面转了想法，tpl生成的是string，那么可以转成处理字符串，`htmlText=fn(tpl(state))`

fn string -> string

ejs 不能改，所以再另想发法子做到可视化

统计下
我们系统里用到打印的页面，
打印标签的基本样式
出标签的场景代码，标签的尺寸
