# 项目立项.md

## 1. 项目背景
现状和痛点


解决方案

## 2. 项目目标与收益
- 总体目标
1. 将后端打印模版移动到前端
2. 管理前端现有模版
3. 对公共打印方法进行改造
4. 模版可视化打印

- 项目收益

- 简化模板开发流程，前后端联调步骤减少，预计提效25%
- 简化打印公共方法，降低开发人力成本：新打印功能开发减少0.25人/天
- 降低打印模板的维护门槛，提升模板复用率，降低开发成本：粗估减少0.5人/天*模板数量

- 提升开发效率，提升时效
- 简化

- 简化开发流程，提升开发效率
- 
- 降低



- 验收标准
  - todo

## 3. 项目范围
涉及范围：wms 系统
改造点：

打印

1. 

## 4. 项目计划&里程碑
这个简单，后往前推即可

## 项目成员与职责

todo
- 怎么确定项目成员和估算人日

## 项目风险
项目风险和应对措施

- 技术风险
- 质量风险
- 资源风险

开发的第一阶段

改造目标：
1. 将后端的html模版移动到前端
2. 整理前端现有的模版,进行管理 (tpl 下面的各种公共的模版, 没有对应的展示, 各种文件夹下面私有的模版)
3. 对公共打印方法进行改造 (是否需要proxy = 1的长缓存)
4. 模版可视化打印

开发的第一阶段
- 将后端的html模版移动到前端（共涉及9个页面，29个页面打印功能点）(前端9人日，后端7人日，测试4人日)
- 对公共打印方案进行改造(前端2人日)
- wms 打印功能前端本地降级方案(前端2人日)

目前：24

开发的第二阶段
- 打印模板管理页和脚本开发（前端5人日）
- 模板可视化打印(前端2人日)
- 整理前端现有的模版,进行管理（共涉及43个页面）



目前：51

还有没照顾到的点：

一些怪东西：
fealFunc 中的printed, printToDataLandscape, printToData, printToDataTransship

zpl: 
src/component/print-template

产品对模板管理页的要求：
增加模块维度的划分


```javascript
  // 操作栏打印按钮
  * doPrint(action) {
    const copyInfo = { ...action.info };
    copyInfo.workLocationCodeImg = textToBase64Barcode(copyInfo.workLocationCode);
    const printUrl = yield transformToPdf(tpl({ info: copyInfo }), 297, 210, true, '');
    yield printAddr(...invoicePrintAddress(printUrl));
    Message.success(t('打印成功'));
  },
```

- `textToBase64Barcode` print.js
- `transformToPdf` print.js
- `tpl` EJS模板
- `printAddr` eph.js
- `invoicePrintAddress` print-url.js

据统计，WMS系统中共有54个文件引用到了82个ejs模板文件，其中45个引用的公共ejs，37个是私有ejs。

人活着就是在对抗熵增定律，生命以负熵为生。

熵增定律. 定义：在一个孤立系统里，如果没有外力做功，其总混乱度（即熵）会不断增大。

这期不做的todo
https://ax.sheincorp.cn/unpkg/ax/W5DDYY/newest/start.html#id=nkuxzo&g=1
XX公司出库单，这个存在于后端，但不涉及打印功能，所以这期先不做，后面项目上线后再考虑优化

一阶段改动范围：
出库 10
支撑 01

共11个页面

二阶段改动范围：
支撑 09
入库 18
出库 10
库内 02

共39个页面

1. 后端打印模板开发流程
现状：
放在后端的打印模板在开发中无法直接预览到效果，需求开发过程：
后端将html打印模板发给前端->前端调整后发给后端->后端构建后自测->如果自测不过，前端再重新调整后发给后端->后端重新构建后再自测->如此反复直到自测通过

改良后：
打印模板迁移到前端后，前端在接口联调后能直接验证效果，需求开发过程：
前端开发->前后端接口数据联调->调整->功能自测完成

2. 当前打印功能相关功能
现状：
打印相关功能(printed) 公共方法中存在4个类似方法
条形码相关功能(JsBarcode)，33个页面中私有方法，公共方法中存在4个类似方法
pdf服务相关功能(toPdf)，35个页面中有私有方法，公共方法中存在3个类似方法
场景代码相关功能，公共方法中存在14个类似方法

改良后：
打印相关功能(printed) 公共方法统一为1个
条形码相关功能(JsBarcode)，公共方法统一为1个，页面统一调用公共方法
pdf服务相关功能(toPdf)，公共方法统一为1个，页面统一调用公共方法
场景代码相关功能，公共方法统一为1个

3. 打印模板现状：
现状：
WMS系统中共有19个公共ejs，和37个私有ejs。由于目前ejs不可见，私有ejs中应该存在大量与公共雷同的ejs.

改良后：
打印模板可视化后，预计能减少一半的私有ejs。

