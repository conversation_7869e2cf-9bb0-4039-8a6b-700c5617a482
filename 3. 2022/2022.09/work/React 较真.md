# React 状态管理与害虫防治：React setState 较真

废话文学：

在写东西，莫名其妙的灵感迸发

近日公司对研发中心的害虫防治方面颇为重视

早发现早预防早治疗

防治跟提皮球相比更有乐子！

## 前因
在wcs中StretchMenu的搜索筛选功能与wms的表现不一样，有bug(害虫，赛博时代电子行业的农业危害)。

## 解决方法
改为setState回调方法

搜索触发的方式中setState为异步，导致数

React不同版本的setState 为什么会有不一样的表现？

- wms的版本是v16.8.6
- wcs的版本是v17.0.2
- StretchMenu的版本是>=16，实际上是16.14.0(也就是v16的最终版本)

todo: react 的changelog https://github.com/facebook/react/blob/main/CHANGELOG.md
todo: shine 的react 版本

🧐 研究，究竟是哪里出了问题

试了React 的 sandbox，v16跟v18 的表现是一样的，异步。

接下来跑下组件看看

组件 npm run doc:dev 跑起来了，react 版本为18.2.0，一样没问题。。。why呢？

这是架构组搞了什么飞机（先立个靶子）

@babel runtime createClass

todo: react setState 异步还是同步，由什么决定？

- [React doc: when is setstate asynchronous](https://reactjs.org/docs/faq-state.html#when-is-setstate-asynchronous)
- [第 18 题：React 中 setState 什么时候是同步的，什么时候是异步的？](https://github.com/Advanced-Frontend/Daily-Interview-Question/issues/17)

从js执行来说, setState肯定是同步执行。但这里讨论的同步和异步并不是指setState是否异步执行, 而是指调用setState之后this.state能否立即更新。

为什么业务组件的环境中setState是同步的？wms跟doc:dev是同样表现的

现在直接看编译后的源码，没看出特别的地方，特别点的应该就是@babel/runtime 的createClass ，

类组件跟函数组件在babel 阶段的重要改变

babel 编译 -> this.state 是动态改变的 -> 所以是同步的？

跑的是编译出来的文件，不应该有不一样的表现呀

保持理智！keep san!dont panic（9月1号，san值归零，阵亡）

keep going!（9月2号，san值重置，继续行动）

今天有点小活儿，下周可以来点新乐子了。整wms-week 在线版本

