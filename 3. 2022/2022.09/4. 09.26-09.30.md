# 9 月份 第 4 周

## 本周 todo
下周开始国庆了！激动，但是先干活。

## 2023.09.26 周一
### life

### work
后台管理/基础功能/包裹打印 /basic-functions/package-print

todo:
- 封装zpl 相关方法
- 封装zpl
- 输入操作文档

切换用开关：
- 开关一：前后端模板开关
- 开关二：新旧代码开关
- 开关三：降级开关

本地降级方案的设计

闭门造车第三天，该出文档了，指导操作

自我要求：
- 尽可能的封装功能（像本地降级）
- 简化使用者的心智负担

- 本地降级功能封装起来
- 减少冲突的可能





## 2023.09.27 周二
### life
入职一周年！

### work


## 2023.09.28 周三
### life
todo:
今天争取早上班早下班（毕竟是真的早，早了2小时）

### work
todo:
立项：
- 输出文档
- 制定jira
  -（毕竟前端明天开始开发）
  - 后端节后回来开发
- ppt发给对应干系人
  - 蔡碧姗
- 跟后端约定开发文档，定时间
- 写概要设计，让人知道怎么开发（这个最舒服，但优先级最低）

先确定明天开发人员，balabala


## 2023.09.29 周四
### life

### work
今天也是超级忙活的一天，但感觉就像在空转。

todo
- 沟通后端，安排节后人日和文档时间(已完成)
  - 跟田陆野那边再确认下具体的开发时间和文档提供的时间
- 创建项目jira(进行中，快好了)
- 创建自提需求并(已完成)
  - 打包是哪个页面？
  - 跟兰维姐拉通对齐
- 今天工作（自己的活）
  - 输出开发文档
  - 排期划分


## 2023.09.30 周五
### life

### work
做需求的一天

todo
- OFC-60430 【产品需求】wms 库内/库存部分- 英文名切换【开发】
- 


## 周六周日 国庆祭典！