# 2 月份 第 3 周

## 本周 todo

- mot 盘点的出新调研

## 2023.02.14 周一

### life

### work

todo:

- 循环盘点 接口请求(已完成)
- 故障 5why(已完成,找了)
- shineout 升级，给兰轩轩提 issue(已完成)
- 定上半年绩效考核
- OFC-31515 lego 库内管理 propTypes 修复(已完成)
- OFC-32195 渠道粘贴框优化重复提示(已完成)

故障 5why 制造,检验,体系,流程

## 2023.02.15 周二

### life

### work

todo

- 定上半年绩效考核
- OFC-32195 渠道粘贴框优化重复提示(自测完之后给自测)

## 2023.02.16 周三

### life

### work

开发日常需求，争取早点晚上，挤出时间摸鱼干点别的（草，忙死了，需求有变更）

## 2023.02.17 周四

名言！

使用底层技术手段，解决上层业务问题，并且在中间层做到符合业界主流。这样一方面不打扰上层应用，另一方面也有助于技术资产被其他项目利用起来。

### life

### work

todo:
- OFC-32408 商品采集
- OFC-32372 补货下架支持操作换箱

15:32 完成 OFC-32408 商品采集，开始去做下一个需求(已提交到test分支)

## 2023.02.18 周五

### life

### work

13:35 今天才开始做新需求，难顶

## 2023.02.19 周六

### life

## 2021.02.20 周日

### life
