# 2022 年方向

无头苍蝇转转转

## 业内方向

### 模块化 ESM

- ESM 是什么？全称是 es modules
- vite
- pnpm

### JavaScript 基建

- esbuild 和 swc 的战争，将牵动着前端的未来

### deno

- deno 是什么？应用场景在哪里？

## React

- React 18？没什么方向，但我可以先学学
- redux

## 公司的前端方向

Lego 的未来方向 hook ts / 微前端 / bundle + complie

突然觉得架构组还是以吹逼为主，行为偏保守。

## 我的方向

2022 的抓手在哪里呢？还没想好！但是想学学 rust，感觉能整些新活。但现在先学学 redux，毕竟这个是项目中的状态管理核心。

所以接下来的学习方向是react的状态管理为主，rust为辅

### 项目中的状态管理

LCD 就是个巨大的怪胎，有这功夫还不如看看 lego-ak

LCD 的毒 架构组打算用 lego-ak 去解，我除了对他们技术能力没有信心之外都很有信心。
