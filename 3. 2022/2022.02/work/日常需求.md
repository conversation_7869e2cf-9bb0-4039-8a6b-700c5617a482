# 日常需求

## 整理

1. 【OFC-22918】WMS 盘点功能优化 (已完成)
   a: 弹窗之后自动清空输入框
   工作量：少
2. 【OFC-32242】商品采集  
   a: mot 去掉重量回填，改成手动输入
   b: wms 二次弹窗增加输入框，判定两次数量是否一致
   工作量：少
3. 【OFC-26328】补货下架支持操作换箱
   a: 增加数量操作 部分下架
   b：二次提示和关箱样式修改
   工作量：中

## 进度

1. OFC-22918 100%
2. OFC-32408 100%
3. OFC-32372

### OFC-22918 WMS 盘点功能优化

2/15 已完成

### OFC-32408 商品采集

- mot
  - 去掉重量回填，改成手动输入(已完成)
- wms
  - 去掉重量回填，改成手动输入(已完成)
  - 二次弹窗增加输入框，判定两次数量是否一致(已完成)

1000166351
1000166362
1000146638
1000146640

### OFC-32372
1. 补货下架
   1. 新增文本框：数量，默认填充当前待下数量
   2. 提交数据后，根据实际下架数量写入移位单、任务管理
2. 回货下架
   1. 功能 copy
   2. 原换箱功能移除

#### 联调内容

- 两接口
   - 在原有接收对象里面加了个参数 offNum
   - wws/front/replenish_shelves/close_pick_container  
   - wws/front/pda/return_down/scan_goods
- 两开关
   - 后端配置了2个开关,用于兼容旧逻辑
   - OFF_SHELF_SUPPORT_CHANGE_CONTAINER("OFF_SHELF_SUPPORT_CHANGE_CONTAINER", "补货下架支持换箱")
   - RETURN_OFF_SHELF_SUPPORT_CHANGE_CONTAINER("RETURN_OFF_SHELF_SUPPORT_CHANGE_CONTAINER", "回货下架支持换箱")

#### 造数据

BH22021800042 补货单号

BHSS012243161 补货周转箱
BH-HH014 库位号
1000166194 商品条码


sM22021517033051 

造数据的过程
1. 库内管理 任务查询 任务单号 查数据
2. 选中数据，点击任务指派按钮。点击下拉框，可以查看到自己是否可被指派。
	*若用户未与库区绑定，则进入用户库区管理，将用户与库区进行绑定
3. 进入pda-拣货-补货下架页面。扫描一个空闲+启用的补货周转箱，此时会进入补货下架的业务流程页面，库位为右上方提供的库位，商品条码为skc+size对应的商品条码

todo:
- scanGoods 接口新增参数offNum(已完成)
- clear waitOffNum(已完成)
- orderTypecode值
- 校验(基本可以，等下午联调)
- 回货下架 (重点)
- 功能copy
- 移除换箱
- 联调

回货下架

周转箱 BHSS012240983
库位号 BH-HH004
数量 
skc sM21122701631587
条码 1000159129

回货下架的任务单号 HH220222001



BH22010600092

回货下架 任务单号 BH22022200001

S372975-5G	FS601-1-21052805
S372975-2D	FS601-1-21052804
S372975-3E	FS601-1-21052803
S372975-4F	FS601-1-21052802

日常补货：BH22022300016(进行中)
紧急补货：BH22022300017
回货：BH22022300015


### OFC-33154 波次创建相关优化

inputMore 

- 不用传值进来（省去

- 接受多种分隔符
- 只有上限提示


