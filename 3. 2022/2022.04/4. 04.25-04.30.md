# 4 月份 第 4 周

## 本周 todo

本周工作应该就是按箱存储项目的联调了，还有就是技术分享：ast 节点操作

## 2023.04.24 周日

### life

### work

五一调休，今日补班！！¥%¥！@¥……¥！@%！……@

10:29 开始按箱存储项目的盘点相关工作，先研究改动点都有哪些。还有就是代码优化。
14:45 继续花时间去了解盘点的逻辑，找到前端改动点

## 2023.04.25 周一

### life

### work

10:18 改动得差不多了，继续吧。争取早上搞完，下午忙活 demo

GW12241YYYYY

## 2023.04.26 周二

### life

### work

上午忙活开发，下午忙活 demo

## 2023.04.27 周三

### life

### work

今天是纯纯的无事可干，继续忙活自己的项目。思索下，怎么挖掘出项目中有乐子的事情。规范落地？来，继续充满激情的开发 demo。决定把项目叫做乐子工程，第一版是问答机，用于生成页面。

上午还是优化完成工作，今天的工作是联调补货下架。

todo: 联调 补货下架。今天还有个 OFC-39010 容器号管理 - 增加“补货周转箱”对应的优先级

估计有点工作量

todo:

- OFC-36400 联调补货下架(todo 都补全了)
- OFC-39010 容器号管理 - 增加“补货周转箱”对应的优先级(已完成)
- OFC-36400 按箱存储项目的用例评审(听完了，很懵，明天针对性的改动下)

前端自提需求：
产能环节看板 修改点：
1、数值颜色色值
2、浮窗显示

验收时间，五一回来之后

## 2023.04.28 周四

### life

### work

工作，但是获得了节前综合症（工作效率-25%）

## 2023.04.29 周五

### life

### work

工作，五一前最后一天！怎样都要坚持下去。

todo:

- OFC-39487 前端 pc 端优化(已完成)
- 产能环节看板优化(已完成)

## 2023.04.30 周六

### life

五一假期 第1天！不过值班，希望没事
