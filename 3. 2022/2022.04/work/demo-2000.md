# demo-2000

## 搭建基础框架

- vite@2(✖️)
- React@18(✔️)
- typescript@4(✔️)
- tailwind css(✔️)
- react-router-dom@6(✔️)
- redux
- antd
- shineout

4/21 17:25 妈的，麻了，react 搭个小项目框架都特别麻烦

4/22 16:40 牧码人的工作暂时完成，继续来干这个。

4/22 16:42 redux 可以暂时放一边，等需要在安上。项目架构可以边做边搭建，轻量化，保持前进速度。

4/22 17:55 一切的痛苦来自于vite过于先进。vite仅能尝鲜，真要干活还是老一套，主要还是React那一套没跟进得那么快。

- React@18(✔️)
- typescript@4(✔️)
- tailwind css(✔️)
- react-router-dom@6(✔️)
- antd(✔️)
- shineout(✔️)

4/26 15:53 重新搭建框架。就基于react+ts，不用vite了
4/26 17:03 基本框架搭起来了，可以干活了

## 正式投入

4/27 昨天终于把框架搭起来了！接下来就该自己安排工作了，找乐子

乐子工程：demo-2000 第一版，问答机! Q&A

该考虑问答机的设计了
