# OFC-36400 按仓存储

## 文档

按箱存储-库内、库存部分

- [PRD 链接](https://ax.sheincorp.cn/unpkg/ax/W5DDYY/newest/start.html#id=46l6kq&g=1)
- [jira 链接](https://jira.dotfashion.cn/browse/GEARS-69880)

开发时间：4.13~4.29（进行中）
测试时间：4.30~5.11（期间跨过五一假期）
验收时间：5.12~5.14
灰度时间：5.16~5.18
上线时间：5.19

支撑组对接：靳明杰（负责库位周转箱关系管理、用户组管理）

- [后端 按箱存储](https://wiki.dotfashion.cn/pages/viewpage.action?pageId=876184496)

## 开发点

- PC
  - 库位周转箱关系管理
    - 开发：4.15
    - 联调：4.22
    - 进度：100%，已完成
  - 任务管理-任务查询新增字段
    - 开发：4.19
    - 联调：4.20
    - 进度：100%，已完成
    - 妈的，竟然没了，得重新补上（究竟去哪了）
- PDA

  - 移位上架
    - 开发：4.20
    - 联调：4.20
    - 进度：100%，前端无需开发
  - 更新库位可用状态
    - 开发：4.21-4.22
    - 联调：4.22
    - 进度：100%，已完成
    - 备注：回货下架，移位下架，冻结移位下架
  - 补货下架
    - 开发：4.19-4.20
    - 联调：4.27
    - 进度：50%，新页面 按箱下架 container-page 已经写完了，页面逻辑也走了一遍，就差联调了
  - 盘点修改
    - 开发：4.25-4.26
    - 联调：4.27
    - 进度：95%, 基本完成，如果有未改动到的点，测试的时候改
    - 细节
      - 新增 信息 周转箱(已完成)
      - 修改 扫描库位(前端不改动，已完成)
      - 修改 扫描推荐库位(前端不改动，已完成)
      - 修改 扫描周转箱(已完成)
      - 修改 扫描条码(已完成)
      - 修改 确认提交数据(已完成)
      - 修改 差异数据展示(已完成)
      - 改动点 1: 周转箱输入框
      - 改动点 2: 信息 周转箱 文本
      - 改动点 3: 盘盈 校验：整箱上架库位不允许盘盈其他商品
      - 改动点 4: 盘点完成 提示框
