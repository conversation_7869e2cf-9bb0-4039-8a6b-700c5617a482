# 11 月份 第 4 周
## 本周 todo


## 2023.11.21 周一
### life

### work
todo
- 【库容可视化】
  - OFC-67902 【支撑】审批单管理
  - OFC-67626 【库内&库存】
    - 库容规划配置 ✅
    - 库存全流程看板
    - 库存看板
- 【wms打印优化】
  - 分支管理 独立开关
  - 写 二阶段上线计划 ✅
  - 写 验收文档
  - 写 复盘 
  - 令锦的小问题（交给根源处理了

## 2023.11.22 周二
### life

### work
继续昨天的工作

todo
- 【库容可视化】
  - OFC-67902 【支撑】审批单管理（70%, 基本完成前端开发部分，等联调并做完剩余部分）
  - OFC-67626 【库内&库存】
    - 库容规划配置 ✅
    - 库存全流程看板 ✅
    - 库存看板
- 【wms打印优化】
  - 分支管理 独立开关
  - 写 二阶段上线计划 ✅
  - 写 验收文档
  - 写 复盘 
- 得找个时间走一遍二阶段的验收

## 2023.11.23 周三
### life

### work
今天很忙，而且时间很少，所以，得弄清楚自己要干什么

todo
- 【库容可视化】
  - OFC-67902 【支撑】审批单管理（70%, 基本完成前端开发部分，等联调并做完剩余部分）
  - OFC-67626 【库内&库存】
    - 库容规划配置 ✅
    - 库存全流程看板 ✅
    - 库存看板（这个下周一才能联调，
- 【wms打印优化】
  - 分支管理 独立开关
  - 写 验收文档（这个的deadline是今天
  - 写 复盘 （这个的deadline是今天


今天先完成

1. OFC-67902 审批单管理 的联调 ✅
2. 去写 二阶段灰度验收文档（简易版也行） ✅
3. 完成 OFC-67626 的四级联动

总共39个页面
- 支撑 15个
- 入库 14个
- 库内 1个
- 出库 9个

## 2023.11.24 周四
### life


### work
todo
- OFC-68569【盘点结果】支持按仓配置 ✅
  - 开发
  - 整测试环境数据来测试(不搞了，太麻烦了)
- 【库容可视化】
  - OFC-67902 【支撑】审批单管理 ✅
  - OFC-67626 【库内&库存】
    - 库容规划配置 ✅
    - 库存全流程看板 ✅
    - 库存看板()
- 【wms打印优化】
  - 上线计划 评审 ✅


这个任务说白了就是 

海外仓（包括散货/整货）的二次盘点/短拣盘点 想用

```
isTwiceOrPick: false, // 是否二次盘点或短拣盘点；用于实现OFC-18735需求的盘点结果页面【且是佛山仓，扫库位时会判断重置】
```

涉及到的变量
- isTwiceOrPick 判断条件之一
- checkResultCode 核心：选择
- checkResultExceptionSelect 短拣盘点 的选择下拉
- checkResultTwiceSelect 二次盘点的 选择下拉
- const isGzBulkload = locationType === locationTypeMap.get(t('散货')) && gz;

直接在扫描库位 sacnGoodsLocation 就介入，因为本身有个（非佛山仓，则短拣和二次盘点）的逻辑。目前是这样

## 2023.11.25 周五
### life

### work

todo
- 【库容可视化】
  - OFC-67626 【库内&库存】
    - 库存看板(四级联动) ✅
- OFC-68667 盘点增加颜色+创建盘点单增加【园区】字段
  - mot ✅
  - wms ✅
- 【wms打印优化】
  - 灰度验收文档完善

## 2023.11.26 周六
### life


## 2023.11.27 周日
### life

## 2023.11.28 周一


### life

### work 

