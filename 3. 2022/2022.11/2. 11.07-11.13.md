# 11 月份 第 2 周
## 本周 todo


## 2023.11.07 周一
### life

### work
完成
- 改掉wms打印优化的开关参数 ✅ 
- 安排灰度验收 ✅

todo 
- 开发 模板管理页
  - 嗅探 ✅
    - 定位
    - 相对路径变为绝对路径
    - 排序，以ejs 为唯一标识
    - 输出
  - 页面开发 20%
    - 页面效果

## 2023.11.08 周二
### life

### work
今天抓紧时间联调吧，今天联调完的话，明天又能也写页面了。

完成
-【wms打印优化】【一阶段】在发生产前关闭开关，避免影响验收 ✅
-【wms打印优化】【一阶段】开关打开观察 ✅

todo
- 
- 【海外计费】页面联调
  - 审批流配置 ✅
  - 审单台页面 进行中
  - 审单台明细
- 【wms打印优化】【二阶段】模板管理页 开发 
  - 嗅探 ✅
    - 定位
    - 相对路径变为绝对路径
    - 排序，以ejs 为唯一标识
    - 输出
  - 页面开发 20%
    - 页面效果

## 2023.11.09 周三
### life

### work
完成
- 【海外计费】页面联调
  - 审批流配置 ✅
  - 审单台页面 ✅
  - 审单台明细 ✅

## 2023.11.10 周四
### life

### work

todo
- 【wms打印优化】【二阶段】模板管理页 开发 
  - 管理页 ✅
  - 预览

先处理json 编辑器和布局，明天再把剩下的搞完


## 2023.11.11 周五
### life

### work
todo

继续昨天的剩余的部分

- 【wms打印优化】【二阶段】模板管理页 开发 
  - 手打
  - 输入
  - 预览


## 2023.11.12 周六
### life


## 2023.11.13 周日
### life

回来做模板管理页了，得解决掉jso编辑器的问题，不然就周末就过不好了。

目标
- 搞定 json编辑器 ✅
- 预览 modal完事 ✅

就这两个，别的留到周一再说。

A cross-origin error was thrown. React doesn't have access to the actual error object in development. See https://fb.me/react-crossorigin-error for more information.

