# 11 月份 第 1 周
## 本周 todo
项目一阶段上线，二阶段进入测试环节

## 2023.10.31 周一
### life

### work
日后待办
- 【英文名优化】任务分配，前端改动，显示英文，传中文

完成
- 【WMS打印优化】补充剩余5个页面的验收文档 ✅
- 【WMS打印优化】通知业务验收 ✅
- 【WMS打印优化】灰度安排演练 ✅

今日todo

- 【海外计费】代码联调

## 2023.11.01 周二
### life

### work
完成


今日todo
- OFC-63073 【wms打印优化】【二阶段】【库内】修改【开发&自测】✅

项目的前端部分改成周四再上线了，多灰度2天，保证验收到所有场景

开关的问题也可以解决了

## 2023.11.02 周三
### life

### work
完成
- 【wms打印优化】【二阶段】开关优化 ✅
- 【wms打印优化】【一阶段】出库面单前置扫描 业务验收 ✅
- OFC-65302 try catch 接口报错提示优化 ✅
- 【wms打印优化】【二阶段】代码评审 ✅

待办
- 【wms打印优化】【二阶段】sotest
- OFC-65358 打印相关优化
  - 测试影响范围的更新 ✅
  - 打印相关的错误提示优化 
  - 新增切换稳定和测试的页面标志

## 2023.11.03 周四
### life
各路神仙保佑，今明两日无事发生！

wms打印优化项目要上生产了。测试测过了，业务验收了，开关优化了，代码也审了。该做的都做了，接下来就是。

14:00 妈蛋，业务拖后腿了，海外竟然没配置权限，一上去就报错。但我之前通知过了，所以不算我的锅，但终究不算顺利，但也还好，幸好是周四上，不然连切换开关都有问题。

别问，问就是如实汇报。

原定周二上线，但最后发现：业务验收通过的面单前置扫描，apm没有查到操作记录。为了稳妥起见，跟上级和业务沟通，继续验证。后端部分按期上线，前端部分周四再上线。

15:26 安逸，虽然海外开关切换成旧的，但国内的依旧在用着。特别是精准出库扫描，还是很稳的。现在只需要等海外配完权限节点（好吧，大概是明天了，业务估计一时半会弄不完）

### work
完成
- 【WMS打印优化项目】【一阶段】上线

todo 
- OFC-65358 打印相关优化 
  - 测试版本范围 修改 ✅
  - 未开打印插件 提示优化 ✅
    - 工位管理-新增 ✅ 好像没什么问题，不用改
    - 精准合包复核 单一发包确认
    - 面单前置扫描 扫最后一件商品条码，一直读进度条
  - 测试版本特殊标志 ✅

## 2023.11.04 周五
### life
13:30 各路神仙继续保佑，小的这里给你磕头了。
14:28 请各路神仙继续护法
16:45 脑袋好晕，不太灵光。

文辉不用看了，没必要汇报，中间问题太多，好感度刷为负了，问一下说一次。这个项目脏得一匹，做完完全没用。

摆了摆了，没必要做好，有什么做什么就行了。



### work
完成
- 【wms打印优化】【一阶段】开关打开 ✅
- 【wms打印优化】【一阶段】权限配置 ✅

todo
- 【wms打印优化】【一阶段】ejs 模板嗅探



## 2023.11.05 周六
### life


## 2023.11.06 周日
### life