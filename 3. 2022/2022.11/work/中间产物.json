{
  "@src/component/outbound/packChecking/rechecking/list/packageHtml.ejs": [
    "/outbound/packChecking/printParcels/list/reducers.js",
  ],
  "@src/component/print-template/packageHtml.ejs": [
    "/outbound/packChecking/rechecking-new/util.js",
    "/outbound/packChecking/rechecking-refactor/util.js",
  ],
  "@src/component/qms/defective/print-template/print-old.ejs": [
    "/qms/defective/de-product-scan/list/saga.js",
  ],
  "@src/component/qms/defective/print-template/print.ejs": [
    "/inbound/warehouse-packing/reducers.js",
    "/qms/defective/de-product-scan-new/reducers.js",
    "/qms/defective/return-handover-detail/list/reducers.js",
  ],
  "@src/component/qms/defective/print-template/scrap.ejs": [
    "/inbound/warehouse-packing/reducers.js",
    "/qms/defective/de-product-scan-new/reducers.js",
  ],
  "@src/tpl/apply-code.ejs": [
    "/qms/receipt-management/added-abnormal-application/reducers.js",
    "/qms/receipt-management/introduction-abnorma/reducers.js",
  ],
  "@src/tpl/barcode-old.ejs": [
    "/inbound/reject-check-manage/reject-check-scan/util.js",
    "/receive-goods-check/receive-qc-scan/list/saga.js",
  ],
  "@src/tpl/borrow-type-code.ejs": [
    "/special-out/box-scan/list/saga.js",
  ],
  "@src/tpl/box-label.ejs": [
    "/examples/something/reducers.js",
  ],
  "@src/tpl/container-code-list.ejs": [
    "/receive-goods-check/receive-qc-manage/list/reducers.js",
    "/special-out/overseas-out/list/reducers.js",
    "/special-out/overseas-out-oldPage/list/reducers.js",
  ],
  "@src/tpl/container-code.ejs": [
    "/basic/nsp-black-pack/reducers.js",
    "/special-out/recall-packing-query/reducers.js",
    "/inbound/reject-check-manage/reject-box-query/reducers.js",
    "/inbound/reject-check-manage/reject-check-scan/util.js",
    "/receive-goods-check/receive-qc-scan/list/saga.js",
    "/transferBill-manage/transferBill-detail/list/reducers.js",
  ],
  "@src/tpl/container.ejs": [
    "/inbound/warehouse-packing/reducers.js",
    "/in-warehouse/storage-query/list/reducers.js",
    "/qms/warehousing/into-scanning-upgrade/list/saga.js",
  ],
  "@src/tpl/fba-container-code.ejs": [
    "/special-out/box-data-query/list/reducers.js",
    "/special-out/box-scan/list/saga.js",
  ],
  "@src/tpl/fn-sku-code.ejs": [
    "/basic/product-info/list/reducers.js",
    "/special-out/box-detail-query/list/reducers.js",
    "/special-out/box-scan/list/saga.js",
  ],
  "@src/tpl/goods-barcode-size.ejs": [
    "/basic-functions/barcode-print/reducers.js",
    "/basic/product-info/list/reducers.js",
    "/in-warehouse/black-code-search/overdue/overdue.reducers.js",
    "/in-warehouse/black-code-search/printed/printed.reducers.js",
    "/in-warehouse/black-code-search/searchFail/searchFail.reducers.js",
    "/in-warehouse/black-code-search/searched/searched.reducers.js",
    "/in-warehouse/black-code-search/unsearch/unsearch.reducers.js",
  ],
  "@src/tpl/goods-barcode.ejs": [
    "/qms/receipt-management/detail/reducers.js",
    "/qms/receipt-management/receipt/list/reducers.js",
  ],
  "@src/tpl/heima-code.ejs": [
    "/inbound/reject-check-manage/reject-check-scan/util.js",
  ],
  "@src/tpl/india-label.ejs": [
    "/combination/pre-package-rechecking/list/saga.js",
    "/special-out/box-scan/list/saga.js",
    "/outbound/packChecking/distribution-order-scan/list/reducers.js",
    "/outbound/packChecking/rechecking/list/saga.js",
  ],
  "@src/tpl/mrp.ejs": [
    "/special-out/box-scan/list/saga.js",
    "/outbound/packChecking/rechecking/list/saga.js",
  ],
  "@src/tpl/package-combine-print.ejs": [
    "/basic-functions/package-print/reducers.js",
    "/combination/pre-rechecking-refactor/list/saga.js",
    "/outbound/packChecking/rechecking-new/util.js",
    "/outbound/package/list/reducers.js",
    "/outbound/patchLabel/list/reducers.js",
    "/outbound/patchLabel-oldPage/list/saga.js",
    "/outbound/packChecking/package-weight-difference/list/reducers.js",
  ],
  "@src/tpl/package-print.ejs": [
    "/basic-functions/package-print/reducers.js",
    "/outbound/package-record/reducers.js",
    "/combination/package-review-refactor/list/util.js",
    "/combination/parcel-package/list/reducers.js",
    "/combination/pre-rechecking-refactor/list/saga.js",
    "/outbound/packChecking/rechecking-new/util.js",
    "/outbound/packChecking/rechecking-refactor/util.js",
    "/outbound/package/list/reducers.js",
    "/outbound/packChecking/package-weight-difference/list/reducers.js",
  ],
  "@src/tpl/packing-list.ejs": [
    "/in-warehouse/breakage/distribution-replace-query/list/reducers.js",
  ],
  "@src/tpl/return-supply-box.ejs": [
    "/qms/defective/refund-box-scan-new/reducers.js",
    "/qms/defective/return-box-query/reducers.js",
  ],
  "@src/tpl/transshipment-box-print.ejs": [
    "/combination/sub-package-boxing/reducers.js",
    "/combination/pre-rechecking-refactor/list/saga.js",
    "/combination/transshipment/list/reducers.js",
  ],
  "@src/tpl/work-location.ejs": [
    "/basic/work-location-manage/reducers.js",
  ],
  "/basic/container/list/container.ejs": [
    "/basic/container/list/reducers.js",
  ],
  "/basic/goods-gather/list/ejs/print.ejs": [
    "/basic/goods-gather/list/reducers.js",
  ],
  "/basic/goods/list/ejs/location-big.ejs": [
    "/basic/goods/list/reducers.js",
  ],
  "/basic/goods/list/ejs/location.ejs": [
    "/basic/goods/list/reducers.js",
  ],
  "/basic/goods/list/ejs/sequence.ejs": [
    "/basic/goods/list/reducers.js",
  ],
  "/combination/package-review-refactor/list/ejs/packageHtml.ejs": [
    "/combination/package-review-refactor/list/util.js",
  ],
  "/combination/package-review/list/packageHtml.ejs": [
    "/combination/package-review/list/saga.js",
  ],
  "/combination/pre-package-rechecking/list/bigPackageTpl.ejs": [
    "/combination/pre-package-rechecking/list/saga.js",
  ],
  "/combination/transshipment/list/ejs/html.ejs": [
    "/combination/transshipment/list/reducers.js",
  ],
  "/examples/something/packageHtml.ejs": [
    "/examples/something/reducers.js",
  ],
  "/examples/something/shoeHtml.ejs": [
    "/examples/something/reducers.js",
  ],
  "/inbound/reject-order/list/print.ejs": [
    "/inbound/reject-order/list/reducers.js",
  ],
  "/inbound/reject-order/reback-receive-scan/print.ejs": [
    "/inbound/reject-order/reback-receive-scan/saga.js",
  ],
  "/multiple-goods-deal/multiple-goods-manage/printe.ejs": [
    "/multiple-goods-deal/multiple-goods-manage/reducers.js",
  ],
  "/outbound/abroad/bbc-box-scan/print.ejs": [
    "/outbound/abroad/bbc-box-scan/reducers.js",
  ],
  "/outbound/abroad/encasement-query/list/print.ejs": [
    "/outbound/abroad/encasement-query/list/reducers.js",
  ],
  "/outbound/packChecking/distribution-order-scan/list/packageHtml.ejs": [
    "/outbound/packChecking/distribution-order-scan/list/reducers.js",
  ],
  "/outbound/packChecking/rechecking/list/packageHtml.ejs": [
    "/outbound/packChecking/rechecking/list/saga.js",
  ],
  "/qms/defective-less/scrap-notice-detail/print.ejs": [
    "/qms/defective-less/scrap-notice-detail/reducers.js",
  ],
  "/qms/defective/de-product-scan/list/packageDetail.ejs": [
    "/qms/defective/de-product-scan/list/saga.js",
  ],
  "/qms/defective/de-product-scan/list/packageLabel.ejs": [
    "/qms/defective/de-product-scan/list/saga.js",
  ],
  "/qms/defective/return-box-query/inventory.ejs": [
    "/qms/defective/return-box-query/reducers.js",
  ],
  "/qms/management-sys/cargo-space/list/print.ejs": [
    "/qms/management-sys/cargo-space/list/saga.js",
  ],
  "/qms/receipt-management/detail/printFba.ejs": [
    "/qms/receipt-management/detail/reducers.js",
  ],
  "/qms/receipt-management/package-detail/ejs/print.ejs": [
    "/qms/receipt-management/package-detail/reducers.js",
  ],
  "/qms/receipt-management/receipt/list/printFba.ejs": [
    "/qms/receipt-management/receipt/list/reducers.js",
  ],
  "/special-out/box-data-query/list/html.ejs": [
    "/special-out/box-data-query/list/reducers.js",
  ],
  "/special-out/box-scan/list/html.ejs": [
    "/special-out/box-scan/list/saga.js",
  ],
  "/special-out/box-scan/list/indiaMrp.ejs": [
    "/special-out/box-scan/list/saga.js",
  ],
  "/special-out/overseas-out-oldPage/list/handoverCode.ejs": [
    "/special-out/overseas-out-oldPage/list/reducers.js",
  ],
  "/special-out/overseas-out/list/handoverCode.ejs": [
    "/special-out/overseas-out/list/reducers.js",
  ],
  "/special-out/tag-print/labelHtml.ejs": [
    "/special-out/tag-print/reducers.js",
  ],
  "/special-out/tag-print/shoeHtml.ejs": [
    "/special-out/tag-print/reducers.js",
  ]
}