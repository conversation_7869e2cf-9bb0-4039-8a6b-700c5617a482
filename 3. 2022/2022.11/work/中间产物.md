- /basic/container/list/reducers.js
  14,29: import tpl from '/basic/container/list/container.ejs';

- /basic/goods/list/reducers.js
  30,39: import tpl100 from '/basic/goods/list/ejs/location-big.ejs';
  31,32: import tpl from '/basic/goods/list/ejs/location.ejs';
  32,40: import sequenceTpl from '/basic/goods/list/ejs/sequence.ejs';

- /basic/goods-gather/list/reducers.js
  23,29: import tpl from '/basic/goods-gather/list/ejs/print.ejs';

- /basic/nsp-black-pack/reducers.js
  14,41: import tpl from '@src/tpl/container-code.ejs';

- /basic/product-info/list/reducers.js
  17,45: import tpl from '@src/tpl/goods-barcode-size.ejs';
  18,43: import FnSkutpl from '@src/tpl/fn-sku-code.ejs';

- /basic/work-location-manage/reducers.js
  11,40: import tpl from '@src/tpl/work-location.ejs';

- /basic-functions/barcode-print/reducers.js
  9,45: import tpl from '@src/tpl/goods-barcode-size.ejs';

- /basic-functions/package-print/reducers.js
  13,67: import packageCombinePrintTpl from '@src/tpl/package-combine-print.ejs';
  14,52: import packagePrintTpl from '@src/tpl/package-print.ejs';

- /combination/package-review/list/saga.js
  25,38: import packageTpl from '/combination/package-review/list/packageHtml.ejs';

- /combination/package-review-refactor/list/util.js
  12,52: import packagePrintTpl from '@src/tpl/package-print.ejs';
  13,42: import packageTpl from '/combination/package-review-refactor/list/ejs/packageHtml.ejs';

- /combination/parcel-package/list/reducers.js
  15,52: import packagePrintTpl from '@src/tpl/package-print.ejs';

- /combination/pre-package-rechecking/list/saga.js
  9,38: import tpl from '@src/tpl/india-label.ejs';
  28,43: import bigPackageTpl from '/combination/pre-package-rechecking/list/bigPackageTpl.ejs';

- /combination/pre-rechecking-refactor/list/saga.js
  15,52: import packagePrintTpl from '@src/tpl/package-print.ejs';
  16,67: import packageCombinePrintTpl from '@src/tpl/package-combine-print.ejs';
  17,63: import transshipmentTpl from '@src/tpl/transshipment-box-print.ejs';

- /combination/sub-package-boxing/reducers.js
  10,63: import transshipmentTpl from '@src/tpl/transshipment-box-print.ejs';

- /combination/transshipment/list/reducers.js
  13,63: import transshipmentTpl from '@src/tpl/transshipment-box-print.ejs';
  28,28: import tpl from './ejs/html.ejs';

- /examples/something/reducers.js
  3,41: import boxLabel from '@src/tpl/box-label.ejs';
  5,32: import shoeTpl from './shoeHtml.ejs';
  6,38: import packageTpl from './packageHtml.ejs';

- /in-warehouse/black-code-search/overdue/overdue.reducers.js
  6,45: import tpl from '@src/tpl/goods-barcode-size.ejs';

- /in-warehouse/black-code-search/printed/printed.reducers.js
  7,45: import tpl from '@src/tpl/goods-barcode-size.ejs';

- /in-warehouse/black-code-search/searched/searched.reducers.js
  7,45: import tpl from '@src/tpl/goods-barcode-size.ejs';

- /in-warehouse/black-code-search/searchFail/searchFail.reducers.js
  6,45: import tpl from '@src/tpl/goods-barcode-size.ejs';

- /in-warehouse/black-code-search/unsearch/unsearch.reducers.js
  6,45: import tpl from '@src/tpl/goods-barcode-size.ejs';

- /in-warehouse/breakage/distribution-replace-query/list/reducers.js
  10,50: import PackingListTpl from '@src/tpl/packing-list.ejs';

- /in-warehouse/storage-query/list/reducers.js
  13,45: import containerTpl from '@src/tpl/container.ejs';

- /inbound/reject-check-manage/reject-box-query/reducers.js
  8,41: import tpl from '@src/tpl/container-code.ejs';

- /inbound/reject-check-manage/reject-check-scan/util.js
  9,43: import goodstpl from '@src/tpl/barcode-old.ejs';
  12,41: import tpl from '@src/tpl/container-code.ejs';
  14,42: import HEIMAtpl from '@src/tpl/heima-code.ejs';

- /inbound/reject-order/list/reducers.js
  13,25: import tpl from '/inbound/reject-order/list/print.ejs';

- /inbound/reject-order/reback-receive-scan/saga.js
  7,25: import tpl from './print.ejs';

- /inbound/warehouse-packing/reducers.js
  8,45: import containerTpl from '@src/tpl/container.ejs';
  14,74: import boxListTpl from '@src/component/qms/defective/print-template/print.ejs'; // todo
  15,79: import boxListTplScrap from '@src/component/qms/defective/print-template/scrap.ejs'; // todo

- /multiple-goods-deal/multiple-goods-manage/reducers.js
  13,26: import tpl from './printe.ejs';

- /outbound/abroad/bbc-box-scan/reducers.js
  22,25: import tpl from './print.ejs';

- /outbound/abroad/encasement-query/list/reducers.js
  12,25: import tpl from './print.ejs';

- /outbound/package/list/reducers.js
  20,67: import packageCombinePrintTpl from '@src/tpl/package-combine-print.ejs';
  21,52: import packagePrintTpl from '@src/tpl/package-print.ejs';

- /outbound/package-record/reducers.js
  12,52: import packagePrintTpl from '@src/tpl/package-print.ejs';

- /outbound/packChecking/distribution-order-scan/list/reducers.js
  14,38: import tpl from '@src/tpl/india-label.ejs';
  15,38: import packageTpl from './packageHtml.ejs';

- /outbound/packChecking/package-weight-difference/list/reducers.js
  16,67: import packageCombinePrintTpl from '@src/tpl/package-combine-print.ejs';
  17,52: import packagePrintTpl from '@src/tpl/package-print.ejs';

- /outbound/packChecking/printParcels/list/reducers.js
  10,82: import tpl from '@src/component/outbound/packChecking/rechecking/list/packageHtml.ejs'; // todo

- /outbound/packChecking/rechecking/list/saga.js
  22,38: import tpl from '@src/tpl/india-label.ejs';
  23,33: import mrpTpl from '@src/tpl/mrp.ejs';
  46,38: import packageTpl from './packageHtml.ejs';

- /outbound/packChecking/rechecking-new/util.js
  16,66: import packageTpl from '@src/component/print-template/packageHtml.ejs'; // todo
  17,67: import packageCombinePrintTpl from '@src/tpl/package-combine-print.ejs';
  18,52: import packagePrintTpl from '@src/tpl/package-print.ejs';

- /outbound/packChecking/rechecking-refactor/util.js
  26,52: import packagePrintTpl from '@src/tpl/package-print.ejs';
  29,66: import packageTpl from '@src/component/print-template/packageHtml.ejs';

- /outbound/patchLabel/list/reducers.js
  8,67: import packageCombinePrintTpl from '@src/tpl/package-combine-print.ejs';

- /outbound/patchLabel-oldPage/list/saga.js
  12,67: import packageCombinePrintTpl from '@src/tpl/package-combine-print.ejs';

- /qms/defective/de-product-scan/list/saga.js
  10,78: import boxListTpl from '@src/component/qms/defective/print-template/print-old.ejs';
  18,37: import labelTpl from './packageLabel.ejs';
  19,39: import detailTpl from './packageDetail.ejs';

- /qms/defective/de-product-scan-new/reducers.js
  13,74: import boxListTpl from '@src/component/qms/defective/print-template/print.ejs';
  14,79: import boxListTplScrap from '@src/component/qms/defective/print-template/scrap.ejs';

- /qms/defective/refund-box-scan-new/reducers.js
  9,47: import boxTpl from '@src/tpl/return-supply-box.ejs';

- /qms/defective/return-box-query/reducers.js
  9,47: import boxTpl from '@src/tpl/return-supply-box.ejs';
  26,38: import inventoryTpl from './inventory.ejs';

- /qms/defective/return-handover-detail/list/reducers.js
  10,74: import boxListTpl from '@src/component/qms/defective/print-template/print.ejs';

- /qms/defective-less/scrap-notice-detail/reducers.js
  13,32: import boxListTpl from './print.ejs';

- /qms/management-sys/cargo-space/list/saga.js
  21,25: import tpl from './print.ejs';

- /qms/receipt-management/added-abnormal-application/reducers.js
  11,46: import applyCodeTpl from '@src/tpl/apply-code.ejs';

- /qms/receipt-management/detail/reducers.js
  13,40: import tpl from '@src/tpl/goods-barcode.ejs';
  22,31: import tplFba from './printFba.ejs';

- /qms/receipt-management/introduction-abnorma/reducers.js
  11,46: import applyCodeTpl from '@src/tpl/apply-code.ejs';

- /qms/receipt-management/package-detail/reducers.js
  17,29: import tpl from './ejs/print.ejs';

- /qms/receipt-management/receipt/list/reducers.js
  10,40: import tpl from '@src/tpl/goods-barcode.ejs';
  38,31: import tplFba from './printFba.ejs';

- /qms/warehousing/into-scanning-upgrade/list/saga.js
  20,45: import containerTpl from '@src/tpl/container.ejs';

- /receive-goods-check/receive-qc-manage/list/reducers.js
  11,53: import boxListTpl from '@src/tpl/container-code-list.ejs';

- /receive-goods-check/receive-qc-scan/list/saga.js
  14,27: // import tpl from './html.ejs';
  17,38: import tpl from '@src/tpl/barcode-old.ejs';
  18,44: import boxTpl from '@src/tpl/container-code.ejs';

- /special-out/box-data-query/list/reducers.js
  8,58: import FBAContainerCode from '@src/tpl/fba-container-code.ejs';
  9,24: import tpl from './html.ejs';

- /special-out/box-detail-query/list/reducers.js
  7,44: import FnSkuCode from '@src/tpl/fn-sku-code.ejs';

- /special-out/box-scan/list/saga.js
  7,48: import FBAtpl from '@src/tpl/fba-container-code.ejs';
  8,43: import FnSkutpl from '@src/tpl/fn-sku-code.ejs';
  9,53: import BorrowTypetpl from '@src/tpl/borrow-type-code.ejs';
  10,43: import indiaTpl from '@src/tpl/india-label.ejs';
  11,33: import mrpTpl from '@src/tpl/mrp.ejs';
  12,24: import tpl from './html.ejs';
  39,36: import indiaMrpTpl from './indiaMrp.ejs';

- /special-out/overseas-out/list/reducers.js
  13,53: import boxListTpl from '@src/tpl/container-code-list.ejs';
  24,44: import handoverCodeTpl from './handoverCode.ejs';

- /special-out/overseas-out-oldPage/list/reducers.js
  12,53: import boxListTpl from '@src/tpl/container-code-list.ejs';
  27,44: import handoverCodeTpl from './handoverCode.ejs';

- /special-out/recall-packing-query/reducers.js
  8,41: import tpl from '@src/tpl/container-code.ejs';

- /special-out/tag-print/reducers.js
  11,34: import labelTpl from './labelHtml.ejs';
  12,32: import shoeTpl from './shoeHtml.ejs';

- /transferBill-manage/transferBill-detail/list/reducers.js
  9,41: import tpl from '@src/tpl/container-code.ejs';