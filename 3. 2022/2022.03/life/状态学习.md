# 项目中的状态管理

## 抛出问题

现在工作中最大的疑惑是：项目中的状态管理究竟该怎么优化？

- 问题：reducers 存着页面的全局状态，但当项目越来越复杂，代码复杂度上升，可读性下降
- 思考：什么东西一定要放在 reducers 中？至少必须有 API
- 思考：复杂页面怎么进行重构？

React 的未来在于 hook，

## 预防针

先来一支预防针，要记住：LCD 存在大量怪癖，这些糟糕的设计都是深度侵入进我们业务代码方方面面的，几乎已经没有修正的可能性了。

在这种框架下你能做的事情有限，想大展拳脚只会让自己难受，还不如在其他方面提升自己。这些怪癖你可以不管，但你要懂，方便避雷。

## 概览

- LCD 怪癖避雷
- immer 核心概念
- redux
- redux-saga
- lego-ak

## LCD 怪癖避雷

见独立篇章

## 固有行为的讲解

- yield ''
- generator 函数的默认参数 (action, ctx, put)
- changeData(draft, action)
- $init

## immer 核心概念

- [immer](https://immerjs.github.io/immer/)
- [Immer 中文文档](https://github.com/ronffy/immer-tutorial)

## redux 核心概念

- [redux](http://cn.redux.js.org/understanding/thinking-in-redux/three-principles)
- [2021年的React状态管理](https://juejin.cn/post/7026232873233416223)

`redux`，React 的状态管理方案

redux 中间件三巨头, `redux-thunk`、`redux-saga`、`redux-observable`

辅助工具，`reselect`、`immer`

`redux-thunk`、`redux-saga`、`redux-observable`

### 三大原则

- 单一数据源：整个应用的 全局 state 被储存在一棵 object tree 中，并且这个 object tree 只存在于唯一一个 store 中。
- State 是只读的：唯一改变 state 的方法就是触发 action，action 是一个用于描述已发生事件的普通对象。
- 使用纯函数来执行修改：为了描述 action 如何改变 state tree，你需要编写纯的 reducers。

### 词汇表

**State** 表示了 Redux 应用的全部状态，通常为一个多层嵌套的对象。
**Action** 是一个普通对象，用来表示即将改变 state 的意图。
**Reducer** (也称为 reducing function) 函数接受两个参数：之前累积运算的结果和当前被累积的值，返回的是一个新的累积结果。该函数把一个集合归并成一个单值。

**Dispatch 函数** (或简言之 dispatching 函数) 是一个接收 action 或者异步 action 的函数，该函数要么往 store 分发一个或多个 action，要么不分发任何 action。

**Action Creator** 就是一个创建 action 的函数。不要混淆 action 和 action creator 这两个概念。Action 是一个信息的负载，而 action creator 是一个创建 action 的工厂。
**异步 Action** 是一个发给 dispatching 函数的值，但是这个值还不能被 reducer 消费。在发往 base dispatch() function 之前，middleware 会把异步 action 转换成一个或一组 action。
**Middleware** 是一个组合 dispatch 函数 的高阶函数，返回一个新的 dispatch 函数，通常将异步 action 转换成 action。

## redux-thunk

redux-thunk 允许应用在组件中 dispatch 一个 function（这个 function 就被称为 thunk），原本在组件中的异步代码被抽离到这个 thunk 中实现，从而在不同组件里复用。

有了 hook 之后，redux-thunk 可能已经完成了它的历史使命，毕竟它能做的 hook 都能做。

## redux-saga 核心概念

- [redux-saga](https://redux-saga-in-chinese.js.org/)
- [redux-saga API Reference](https://redux-saga.js.org/docs/api/)
- [Redux-Saga妈妈级教程（上）](https://juejin.cn/post/6975041237266989086)
- [Redux-Saga妈妈级教程（下）](https://juejin.cn/post/6979146131028574245)
- [redux-saga用法总结](https://juejin.cn/post/6915327386556825607)
- [轻松使用Redux-saga](https://zhuanlan.zhihu.com/p/114409848)

Redux-saga 旨在使应用中的副作用更便于管理，基于 ES generator 特性实现。可以想像为，一个 saga 就像是应用程序中一个单独的线程，它独自负责处理副作用。

Saga 高度封装了对 Side effects 的管理。我们在组件中 dispatch 的依然是 redux 的原生 action（plain object），saga 中监听 action，并执行与 action type 相应的 callback。这种写法相当于把组件内的异步代码都抽离到 saga 中管理。
Saga 的一大优势是内置了 race、takeLatest、takeEvery 等策略，可以很方便地实现 action 的竞态(Racing Effects)和并发(Concurrency)场景下的支持。

同时 saga 由于自身的高度封装，概念和接口比较多，包括：

和组件、redux 的交互（take、select、put）
阻塞调用和非阻塞调用（fork、call）
竞态、并发（race、takeLatest、takeEvery）
...

## 未来展望 lego-ak

[lego-ak](http://lego-ak-master-dev.dev.paas-dev.sheincorp.cn/guide/describe)

一个架构组的新玩具，本质上一个 redux Enhancer。面对现状，不得不寄了，但保留希望，另起炉灶。

lego-ak 抛弃了 redux-saga，改用 async/await。也好，跟技术主流。
