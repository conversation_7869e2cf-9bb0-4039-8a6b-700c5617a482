# LCD 怪癖避雷

- [lego-ak 与 LCD 的关系](http://lego-ak-master-dev.dev.paas-dev.sheincorp.cn/guide/less-coding)
- [LCD](https://ue.dev.sheincorp.cn/document/13)

## 概览
- yield ''
- 异步函数默认行为是 takeLatest 的行为
- 劫持 React.createElement
- mobx 的设计缺陷
- 盲目的修改著名第三方库的代码
- 编译期黑盒的替换行为
- 自动 connect

## yield ""
这个怪癖是因为LCD深度集成了redux-saga的缘故，generator + saga 执行器的设计是 redux-saga 的灵魂，不能用 async await 来实现。

但logo-ak 抛弃了redux-saga，异步管理不需要搞这么复杂

- [为什么 redux-saga 不能用 async await 实现](https://juejin.cn/post/7014484781429850148)

## reducers 函数的默认参数 (action, ctx, put)


## 异步函数默认行为是 takeLatest 的行为


