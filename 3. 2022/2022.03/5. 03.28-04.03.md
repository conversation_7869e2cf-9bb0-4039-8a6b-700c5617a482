# 3 月份 第 5 周

## 本周 todo

- 本周值班，天天快乐背书包
- 本周要上 6 天班！可恶我值班的时候竟然变长了

## 2023.03.28 周一

### life

### work

todo:

- OFC-36144 MOT 回货下架 关箱提示前端优化(已完成)
- OFC-34805 【站仓】前端-库内逻辑实现

## 2023.03.29 周二

### life

### work

本周上线需求:

- OFC-36144 MOT 回货下架 关箱提示前端优化
- OFC-35790 包裹管理-生成波次前端交互优化
- OFC-32633 积压数据监控项目二阶段

10:30 今天值班大作战！
15:43 暂停下，做到国家线库存 获取园区列表上(todo)

## 2023.03.30 周三

### life

### work

10:30 妈耶，今天才周三。

今天主要工作是站仓的联调，以及值班看问题。

todo

- OFC-34805 【站仓】前端-库内逻辑实现
  - 库内授权页面
    - 联调
  - 国家线与库存
    - 联调
  - 新增字段
    - 开发
    - 联调

## 2023.03.31 周四

### life

### work

疯狂值班中，疯狂联调中

## 2023.04.01 周五

### life

### work

10:58 今天联调，尼玛，好多

- WMS
  - 库内授权
  - 国家线与库存
  - 任务管理
    - 任务管理/任务查询 添加字段/搜索条件(已完成)
    - 任务管理/任务明细 添加字段/搜索条件(已完成)
  - 补货管理
    - 补货管理/补货分析(已完成)
    - 补货管理/补货查询 添加字段-国家线/快慢流/删掉生成补货任务按钮(已完成)
  - 理货管理
    - 增加搜索条件/添加字段(已完成)
    - 弹窗信息修改(难搞，需要数据联调)
  - 交接转运配置 去除必填
    - 起运子仓/收货子仓
- PDA
  - 补货下架/ 成功提示
  - 容器查询 /增加显示

交互细节

- 置灰
  - 当园区类型=快流，SKU 类型=慢流时，两个文本框都支持选择
  - 其他场景下，若其中一个下拉框有值时，另一个需要置灰
- 数据
  - 上架园区（拣货区）：下拉展示当前 SKU 快/慢流，园区对应的快/慢流“拣货园区”
  - 上架园区（备货区）：下拉展示当前园区对应的“备货园区”
  - 园区排序方式：查【库存环节全流程看板】的库容规划，按照子仓对应的“剩余库容”降序排序
- 交接转运配置
  - 去掉必填

## 2023.04.02 周六

### life

### work

- mot
  - 补货下架 增加提示(已完成)
  - 回货下架 增加提示(已完成)
  - 容器号查询 显示推荐库位(已完成)

1、回货下架-扫描条码 wws/front/pda/return_down/scan_goods type=1 recommendUpperParkName
2、回货下架-短拣 wws/front/pda/return_down/short_pick type=1 recommendUpperParkName
3、补货下架-扫描条码 wws/front/replenish_shelves/scan_goods status=1 recommendUpperParkName
4、补货下架-短拣 wws/front/replenish_shelves/short_pick type=2 recommendUpperParkName
5、容器号查询 wms/front/inventory/container/query recommendUpperParkName

15:19 开发工作已完成，现在进入转测阶段。

需要 codereview 的项目，

1. OFC-32633 库存看板二期

- 产能环节看板 数据对比日期
- 积压监控配置
- 积压监控看板
- 积压监控明细

2. OFC-34425 prov

- 溯源库存查询
- 包裹溯源信息查询

16:00 上测试了，狂修 bug。

## 2023.04.03 周日

### life

### work

10:30 值班最后一周，值班一周，上班 7 天。心累。

值班，疯狂值班。
