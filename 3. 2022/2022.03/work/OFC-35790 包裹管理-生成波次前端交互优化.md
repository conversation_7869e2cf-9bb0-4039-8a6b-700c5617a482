# OFC-35790 包裹管理-生成波次前端交互优化

## todo
- 生成合包波次
  - 优化：仅记忆本人本页面 
  - 显示选择的包裹的包裹标识去重显示，标签显示(已完成)
  - 若有包裹无标识，额外具有【无标识】标签(已完成)
  - 前端过滤“佛迪敏感品标识”(已完成)
- 手动生成波次
  - 优化：仅记忆本人本页面 

## 方案

业务有个需求：同一页面开多个标签页，每个标签页有自己的缓存值，刷新页面保留缓存，关闭浏览器清掉缓存。

核心问题：当前标签页怎么认出自己？

方案1：用路由hash值，存sessionStorage 。但解决不了复制路由地址会相同hash的问题，而且LCD下的路由也不敢加hash值

方案2: 用Web Worker，用专有 Worker。但Web Worker 天生不是干storage 的活儿的，整了之后页面还得监听postmessage 事件。整大活了属于是 

瑞源提供了方案3: 给sessionStorage 加字段: id，再监听下刷新事件和再加个字段来处理复制地址的问题

婷姐提供了半个方案4: MessageContainer 会在indexDB 中生成标签唯一值，唯一的问题是MessageContainer会在你一打开他就用uuid新生成一个唯一值，至于当前标签页是哪一个就不知道了

## key

// 手工生成波次

outbound_package_manualCreate_wellenNationalLine // 波次国家线
outbound_package_manualCreate_pickType // 拣货方式

// 生成合包波次

outbound_package_combineCreate_wellenNationalLine // 波次国家线
outbound_package_combineCreate_sensitive // 是否为敏感品波次
outbound_package_combineCreate_orderMark // 波次标识


sessionStorageTagId

选择模板 保存模板