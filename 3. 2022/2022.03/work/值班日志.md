
“潘增业：
【提报人】: 林梓畅
【所属部门】: SHEIN集团>供应链中心>全球仓储营运部>佛山仓储一部>安博五部>库存二部>拣货2-7组
【jira】: https://jira.dotfashion.cn/browse/SFD-3721
1、标题: 2023.03.27-WMS-大塘 安博-出库管理-0001
2、紧急程度: P1
3、问题描述: 储位号对应不上，却扫上了，但拿对应库位的商品却显示储位号不符，操作跳过该储位后系统直接显示已拣过该储位的商品
账号:刘灵16 任务号:SS2203271338048 箱号:JHSS032189924
[图片]”
------
@匡钱守(Qianshou Kuang)   这个也帮忙看看哈，是不是没有校验到库位号

哦 那就是 这个人 扫描的时候 没有触发回车。
1. 输入法是中文下扫描
2. 聚焦在库位号，然后 下拉状态栏扫描，这样也不会触发回车。
3. 手动输入

@潘增业(Zengye Pan) 老哥，这个是用户操作的问题哈。


2022-03-31值班问题反馈

2022-03-28 值班问题反馈
问题描述：PDA 正常拣货 显示储位号不符
应用环境：生产环境
问题原因：因为用户操作原因没有触发回车，导致没有校验
处理方式：不处理
责任人：用户

问题描述：打包复核扫描商品条码卡顿问题
应用环境：生产环境
问题原因：目前定位到为网络问题，运维在持续跟进中
处理方式：持续跟进
责任人：暂无

2022-03-31 值班问题反馈
问题描述：PDA入库上架，数量输入框值没根据上架配置规则自动填充
应用环境：生产环境S1
问题原因：获取上架配置规则从扫箱号换成扫库位成功后获取，导致扫库位成功后一直判断不用自动填充数量值
处理方式：将判断自动填充数量值逻辑改到获取上架配置规则成功后进行判断填充
修复时间：3.31，跟着日常版本一起发S2上线
责任人：陈文贵

问题描述：wms新检换入库装箱，扫条码报错弹窗关闭后，没自动聚焦条码输入框
应用环境：生产环境
问题原因：之前一个紧急优化需求，扫条码请求期间给输入框加置灰；影响到弹窗关闭后自动聚焦功能
处理方式：加上主动触发聚焦条码代码
修复时间：3.31
责任人：陈文贵