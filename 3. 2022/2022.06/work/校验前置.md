# 校验前置
## 更友好的提示


## 痛点
wms的页面搜索条件很多， 不点搜索都不知道哪些是必填项

## 难点
- 必填项可能是动态的
- 尽可能的少影响到当前业务

## 技术点
- Form

SearchAreaContainer -> SearchArea -> Form

searchArea-container.jsx

基于 Shineout Form 组件，支持控件展开、收缩，云端配置等功能。
- 组件依赖云配置组件configProvider和sortableoptions,发生云配置方面的问题时，可以单独升级configProvider或者sortableoptions即可
- 组件中的每一个控件支持通过 itemClass, labelClass, controlClass 控制各部分样式
- 组件中的每一个控件支持通过 required 属性控制是否强制展示（无法取消展示）
- 组件中的每一个控件支持通过 span=x 属性控制占据的列数
- 可以在控件上设置 selected={false} 属性，实现默认情况下不选中部分控件的功能

## 场景

## 要干的事儿

SearchArea 有一个参数needed,

- 研究一波 SearchArea

怎么设计？




