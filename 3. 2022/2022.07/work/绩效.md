# 绩效

## 自我开导

开幕暴击，上半年绩效拿C。文辉说的理由是S5级的时效问题，感觉这个理由有点无力。日常需求跟项目需求我都是准时完成按时跟进的，只是自提需求方面相对松懈。但既然都这么说了，只能补上这一块了。

怎么说呢，有种无力感。就是绩效这事儿不是我决定的，谁来背C都是上面决定的。努力基本无效，那我辛苦加班也没啥意思，既然如此，以后就不加了，反正都背C了，下半年再积极最多也就拿B，工作上做到尽责就好，别越界了，没啥用。

还有就是吸收教训，多跟文辉互动。混个脸熟，不然下半年再背个C就直接GG了。

所以花精力去提升自己吧，本职工作上尽责即可，多余的力气要攒起来。最见效的应该是提升财商。有钱了，心里不慌。本金不少，赚钱速度不应该这么弱，得提升自己的财商。

## 准备后事

todo：
- 理财学习！
- 知识积累！

理财原则：
- 仓位 60% 稳健型
- 仓位 40% 浮动型
- 单支产品不超过15%
- 基金只买C类
- 可追高可止损

目标：
- 攒够100w预防冲击
- 提升被动收入
- 前端知识前沿掌握

## 方向
- 提升财商
  - 好奇心 一般
  - 耗时 未知
  - 收益 重要，因为本金不少，当前收益太低
- 学习学习观
  - 好奇心 强
  - 耗时 短
  - 收益 强，对于自我学习/机器学习都是有用的
- 学习rust
  - 好奇心 一般
  - 耗时 较长
  - 收益 暂无收益
- 学习3d打印
  - 好奇心 较强
  - 耗时 未知
  - 收益 至少模型件能自己弄了！
- 学习 硬件
  - 好奇心 较强
  - 耗时 未知
  - 收益 极客工程师的基础

## 填下半年绩效

1. 开发过程严格规范代码，提高代码质量。P3及P3以上线上故障为0，P4故障不大于1个，MTTR时间为2h内。  40% 
2. 项目和需求能够100%以上准时完成。 30%
3. 至少2次以上主动pdca，至少一次以上的知识分享，开发工具为团队赋能。 30%

主要还是几个点，效率、质量、还有针对自己的上半年的一些不足。

质量的话 生产故障，测试阶段的 缺陷率 低于30%（bug数 / 任务初始预估 * 100%）

还有就是研发效率。需求平均研发时间不得超过5.5d。研发时间 = 技术评审完成 到 转测  时间段  +  验收完成 到 已上线 时间段。

确定方案，开发时间点 ->  任务安排 -> 关键节点 -> 梳理范围

关键节点要同步到位

2次以上有后续的主动pdca，

并且 发现问题，制定计划，检查，复盘，


1. 开发过程严格规范代码，提高代码质量。P3及P3以上线上故障为0，P4故障不大于1个，MTTR时间为2h内；测试阶段的缺陷率低于30%；
2. 项目和需求能够100%以上准时完成；提升研发效率，平均研发时间不得超过5.5d；
3. 完成2次以上闭环且有后续的主动pdca，开展一次以上的知识分享，开发工具为团队赋能；


1. 开发过程严格规范代码，提高代码质量。P3及P3以上线上故障为0，P4故障不大于1个，MTTR时间为2h内；测试阶段的缺陷率低于30%； 
权重：30%
2. 项目和需求能够100%以上准时完成；提升研发效率，平均研发时间不得超过5.5d；
权重：30%
3. 完成2次以上闭环且有后续的主动pdca，开展一次以上的知识分享，开发工具为团队赋能；
权重：20%
4. 精通库内的补货、回货、盘点等主要业务，熟悉数据流转和界面功能逻辑操作。
权重：20%