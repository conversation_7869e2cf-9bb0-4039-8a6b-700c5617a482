周二 2022-07-12 值班问题反馈

问题描述：WMS-作业人员分组 无法修改小组名称
应用环境：生产环境
问题原因：treeSelect组件keygen值由绑定对象改为绑定对象的id属性，未修改对应方法，导致方法内部调用有误
处理方式：将keygen值由对象id改回对象
责任人：黄靖婷

周四 2022-07-14 值班问题反馈

问题描述：wms-安灯问题管理页面 部门输入框，搜索的时候会导致页面报错不可用。
应用环境：生产环境
问题原因：treeSelect组件使用了keygen={d => d}，导致筛选的时候报错；
处理方式：通过查看代码，发现只需要获取id，不需要整个对象就可以满足需求了，所以改成 keygen={d => d.id}；
责任人：李根源

问题描述：MOT-移位上架完成后白屏
应用环境：生产环境
问题原因：页面重新加载，bbl组件会触发报错：ReferenceError regeneratorRuntime is not defined；
处理方式：babel 配置中加入regenerator-runtime 的polyfill；
责任人：架构组

问题描述：wms-全流程主看板页面--进入页面，获取主看板统计数据，报无权限节点错。
应用环境：生产环境
问题原因：接口前缀拼写错误，把wms/stat 写成 wms/front，导致调用接口报无权限节点错误；
处理方式：流量回滚，代码修复后跟随本周日常版本上线；
责任人：钱喜洁

问题描述：面单前置出库扫描 耗材条码双弹窗
应用环境：生产环境
问题原因：扫描商品条码的聚焦事件未阻止默认冒泡行为，导致耗材输入框聚焦事件触发两次
处理方式：代码完善并验证后跟随本周日常版本上线；
跟进人：黄靖婷；
责任人：无

周五 2022-07-15 值班问题反馈

问题描述：wms-盘点结果 缺失化妆品效期项目相关的新字段
应用环境：生产环境
问题原因：jira上的前端任务没有关联到需求上，导致漏合；
处理方式：下周一申请发版；
责任人：吴鑫泰






