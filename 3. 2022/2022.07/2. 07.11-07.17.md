# 7 月份 第 2 周

## 本周 todo

## 2023.07.11 周一

### life

### work
todo
- OFC-48391 wms pdf打印历史记录(已完成)
- 跟进下均萍的code review

## 2023.07.12 周二

### life

### work

- 值班
- OFC-48465 稳定性周报接口处理
- 跟进下均萍的code review
  - OFC-46153
  - OFC-46155

- OFC-46153（看板部分）
  - 合包异常上架看板 src/component/board/comb-exp-on-shelf/saga.js
  - 打包作业看板 src/component/board/exp-on-shelf/saga.js
  - 出库一分作业看板 src/component/board/first-board/saga.js
  - 打包作业看板 src/component/board/packing-board/saga.js
  - 出库拣货超时看板 src/component/board/pick-timeout-board/saga.js
  - 二分作业看板 src/component/board/second-board/saga.js
- OFC-46155（后台管理）
  - 品类与存储属性关系 src/component/basic/category-storage/reducers.js
    - oldPage 的state
  - 上架推荐配置 src/component/basic/putaway-strategy/putaway-recommend-config/reducers.js
    - 没问题
  - 上架规则配置 src/component/basic/shelf-rules-configuration/reducers.js
    - 没问题
  - 周转箱监控看板配置 src/component/basic/container-board-config/reducers.js
    - 没问题
- OFC-46156（自测部分）
  - 超期明细 src/component/board/overdue-data-query/reducers.js
    - 没问题
  - src/component/in-warehouse/breakage/return-loss-scan/list/reducers.js
    - 有点要改

## 2023.07.13 周三

### life

### work

## 2023.07.14 周四

### life

### work
- 安灯问题管理 部门 问题处理
  - 11:24 报障
  - 11:32 前端介入解决，并提供不阻塞流程的操作方式
  - 11:42 定位到问题 
  - 12:23 验证并修复问题，发测试找测试验证
  - 13:30 灰度验证通过
- 7.14移位上架完成白屏问题
  - 12:45 报障
  - 12:52 流量切回
  - 15:22 本地验证修复通过，发灰度找业务验证
  - 15:58 灰度验证通过
- 全流程看板 问题处理
  - 13:34 报障
  - 13:35 流量回滚
  - 13:38 定位到问题并修复
  - 14:18 发灰度验证
  - 14:31 灰度验证通过

问题描述：库内管理/异常管理/安灯问题管理页面的部门输入框，搜索的时候会导致页面报错不可用。
应用环境：生产环境
问题原因：treeSelect组件使用了keygen={d => d}，导致筛选的时候报错
处理方式：通过查看代码，发现只需要获取id，不需要整个对象就可以满足需求了，所以改成 keygen={d => d.id} 
责任人：李根源

问题描述：MOT移位上架完成后白屏
应用环境：生产环境
问题原因：页面重新加载，bbl组件会触发报错：ReferenceError regeneratorRuntime is not defined
处理方式：babel 配置中加入regenerator-runtime 的polyfill
责任人：架构组




## 2023.07.15 周五

### life

### work
todo
- 值班响应（但今天是下午5点才发布，所以趁机干活先）
- OFC-48885 补货策略配置/合并任务明细配置
  - 开发(已完成)

## 2023.07.16 周六

### work
因为值班周，所以过来加班，毕竟这周真的很玄。

09:39 过来加班了！今天6点下班！本周值班问题还挺多的。

todo
- 写值班反馈(已完成)
- 看完code review(已完成，周一反馈给均萍)

## 2023.07.17 周日

### life