const compareSku = (sourceObj = {}, targetObj = {}, skcName = 'skc', sizeName = 'size') => {
  const { skuCode } = sourceObj;
  // 两个对象都有skuCode则用skuCode比较，没则用skc+size判断
  return skuCode && targetObj.skuCode ? skuCode === targetObj.skuCode
    : (sourceObj[skcName] === targetObj[skcName] && sourceObj[sizeName] === targetObj[sizeName]);
};

const compareSkuWithDate = (sourceObj = {}, targetObj = {}, skcName = 'skc') => {
  if (compareSku(sourceObj, targetObj, skcName)) {
    if (Object.prototype.hasOwnProperty.call(sourceObj, 'productionDate') &&
      Object.prototype.hasOwnProperty.call(targetObj, 'productionDate')) {
      return sourceObj.productionDate === targetObj.productionDate;
    } else {
      return true;
    }
  } else {
    return false;
  }
};

const handleGoodList = (goodsList, editDateGoodList, editDateData) => {
  console.log('handleGoodList');

  console.log('goodsList', goodsList);
  console.log('editDateGoodList', editDateGoodList);
  console.log('editDateData', editDateData);

  // 处理editDateGoodList
  // editDateOriginIdx editDateGoodList 中以这条数据为原数据
  const editDateOriginIdx = editDateGoodList
    .findIndex((x) => x.productionDate === editDateData.productionDate);
  const editDateHasOrginData = editDateOriginIdx >= 0;

  console.log('editDateOriginIdx', editDateOriginIdx);

  const editDateOriginData = !editDateHasOrginData ? {
    ...editDateData,
    number: 0, // 已盘点的全部改日期了，所以盘点数为0
  } : editDateGoodList[editDateOriginIdx];

  console.log(`editDateOriginData`, editDateOriginData)

  const returnGoodList = [...goodsList];

  // 非原数据的，系统数=0
  const editDateOtherGoodsList = editDateGoodList
    .filter((x, index) => index !== editDateOriginIdx)
    .map((x) => ({
      ...editDateData,
      id: '', // 暂时为空，后续会使id值会唯一
      productionDate: x.productionDate,
      expiringDate: x.expiringDate,
      systemNumber: 0, // to check
      checkNumber: Number(x.number),
    }));

  console.log('editDateOtherGoodsList', editDateOtherGoodsList);

  // 要插入的goodList 数据
  const insertGoodsList =
    [
      ...[{
        ...editDateData,
        productionDate: editDateOriginData.productionDate,
        expiringDate: editDateOriginData.expiringDate,
        systemNumber: Number(editDateData.systemNumber), // to check
        checkNumber: Number(editDateOriginData.number),
      }],
      ...editDateOtherGoodsList,
    ];

  console.log('insertGoodsList', insertGoodsList);

  // 原数据的index, 用于删除
  const diffFindIdx = returnGoodList.findIndex((x) => x.id === editDateData.id);

  console.log('diffFindIdx', diffFindIdx);

  // 处理叠加
  returnGoodList
    .filter((x, index) => index !== diffFindIdx)
    .forEach((x) => {
      const findIndex = insertGoodsList.findIndex((y) => compareSkuWithDate(y, x, 'goodsSn'));

      if (findIndex > 0) {
        // item.systemNumber += insertGoodsList[findIndex].systemNumber;
        x.checkNumber += insertGoodsList[findIndex].checkNumber;
        insertGoodsList.splice(findIndex, 1);
      }
    });

  console.log('insertGoodsList', insertGoodsList);

  returnGoodList.splice(diffFindIdx, 1, ...insertGoodsList);

  // 使goodsList中的id 唯一
  returnGoodList.forEach((item, index) => Object.assign(item, { id: index }));

  console.log('returnGoodList', returnGoodList);

  return returnGoodList;
};

let one = {
  goodsList: [{
    checkNumber: 20,
    expiringDate: "2100-12-31",
    goodsBarCode: "S457413-24",
    goodsSn: "tee180412056",
    id: 0,
    index: 0,
    isDiff: 1,
    isMark: 0,
    productionDate: "2022-01-01",
    sequence: null,
    shelfLifeDays: null,
    size: "S",
    skuCode: "I2fq8u30bcs7",
    systemNumber: 4,
  }],
  editDateGoodList: [
    {
      canEditNum: true,
      expiringDate: "2022-01-02",
      number: "11",
      productionDate: "2022-01-02",
      shelfLifeDays: null,
    }, {
      canEditNum: false,
      expiringDate: "2100-12-31",
      number: "9",
      productionDate: "2022-01-01",
      shelfLifeDays: null,
    }
  ],
  editDateData: {
    checkNumber: 20,
    expiringDate: "2100-12-31",
    goodsBarCode: "S457413-24",
    goodsSn: "tee180412056",
    id: 0,
    index: 0,
    isDiff: 1,
    isMark: 0,
    productionDate: "2022-01-01",
    sequence: null,
    shelfLifeDays: null,
    size: "S",
    skuCode: "I2fq8u30bcs7",
    systemNumber: 4,
  },
}

let two = {
  goodsList: [
    {
      "checkNumber": 9,
      "expiringDate": "2100-12-31",
      "goodsBarCode": "S457413-24",
      "goodsSn": "tee180412056",
      "id": 0,
      "index": 0,
      "isDiff": 1,
      "isMark": 0,
      "productionDate": "2022-01-01",
      "sequence": null,
      "shelfLifeDays": null,
      "size": "S",
      "skuCode": "I2fq8u30bcs7",
      "systemNumber": 4
    },
    {
      "checkNumber": 11,
      "expiringDate": "2022-01-02",
      "goodsBarCode": "S457413-24",
      "goodsSn": "tee180412056",
      "id": 1,
      "index": 0,
      "isDiff": 1,
      "isMark": 0,
      "productionDate": "2022-01-02",
      "sequence": null,
      "shelfLifeDays": null,
      "size": "S",
      "skuCode": "I2fq8u30bcs7",
      "systemNumber": 0
    }
  ],
  editDateGoodList: [
    {
      canEditNum: true,
      expiringDate: "2022-01-03",
      number: "7",
      productionDate: "2022-01-03",
      shelfLifeDays: null,
    }, {
      canEditNum: false,
      expiringDate: "2100-12-31",
      number: "2",
      productionDate: "2022-01-01",
      shelfLifeDays: null,
    }
  ],
  editDateData: {
    "checkNumber": 9,
    "expiringDate": "2100-12-31",
    "goodsBarCode": "S457413-24",
    "goodsSn": "tee180412056",
    "id": 0,
    "index": 0,
    "isDiff": 1,
    "isMark": 0,
    "productionDate": "2022-01-01",
    "sequence": null,
    "shelfLifeDays": null,
    "size": "S",
    "skuCode": "I2fq8u30bcs7",
    "systemNumber": 4
  },
}

let newOne = handleGoodList(
  two.goodsList,
  two.editDateGoodList,
  two.editDateData
)




