# 8 月份 第 4 周

## 本周 todo
上周值班竟然平安无事！感谢诸位神明庇佑。可喜可贺，下次还这么来。

## 2023.08.29 周一
### life

### work
todo
  - code review(已完成)
  - 库存id 任务安排(已完成)
  - wcs StretchMenu 组件跟进

14:18 准备进入状态，开始研究问题

16:38 找到了一个修复的捷径，但是真正的原因还没找到，但可以确定在 onSearchChange 中


## 2023.08.30 周二
### life

### work
todo
- sotest 自测
- wcs StretchMenu 组件跟进
- 盘点巡检

13:53 优先过点自测，接着组件跟进
下午网络不行，太坑了。

## 2023.08.31 周三
### life

### work
todo
- 开始研究短拣盘点/二次盘点
- 研究react setState

14:00 先完成对短拣盘点的输出，接着在开始研究setState 的问题。

奇思妙想标题：
忒修斯之船驾驶与维护指南
-- 如何管理一个半年离职率35%的团队

当一艘船上的木头逐渐被替换，直到所有的木头都不是原来的木头，这艘船还是原来的那艘。按这个速度，仓储团队现在应该是v2.0版本了。

bug与电子农药
--typescript的使用


写到困了，挺没劲的。

## 2023.09.01 周四
### life

### work
todo
- 巡检：二次盘点
- 继续写乐子

今天的工作内容不急又没劲，得整点新乐子。

例如：去学一下Hooks的状态流状态哲学？去钻研下上次的setState问题？都可以，有点技术乐子

14:28 该去写二次盘点的巡检手册了，干点活

15:01 摸了！继续学习，钱难挣屎难吃，研究学习去。（今天又亏钱了，出货了，少亏一点是一点）

## 2023.09.02 周五
### life

### work
todo:
- 早上趁机输出二次盘点文档
- 

下午研究下ssr的框架搭建，这个项目争取怎么简单怎么来


## 2023.09.03 周六
## 2023.09.04 周日
### life

wms-week plus

1. 收集需求
2. 搭建框架开发

已有需求
- 定期执行(已有功能)
- 模板替换
- 立即执行
- 看接口和返回

该怎么搭框架呢？一个简单的