# 盘点

盘点 概念整理

现有的盘点类型
- 循环盘点
- 实时盘点
- 二次盘点
- 短拣盘点
- 动碰盘点
- 低水位盘点(准备废弃)
- 效期盘点

效期盘点 整箱库位和非整箱库位



盘点方式
- 明盘
- 盲盘

盲盘不展示盘点数

库位类型
- 大货
- 非佛山仓散货
- 佛山仓散货

佛山仓散货跟大货一致，只是提交时需要加序列号

非佛山仓散货 必须按照序号盘点，只能通过右上角进入盘盈页面。


动碰盘点 跟循环盘点基本一致

短拣盘点 下架短拣之后会自动生成短拣盘点任务

只能操作标红数据，不然



循环盘点：
-现场一般每个月会给仓库做一次全盘
来源：PC-盘点单管理-创建盘点单
结果：不产生盘盈盘亏，有差异会产生二次盘点

动碰盘点和循环的区别在于，创建盘点单会调用算法接口，从选中区域里挑选一些库存变动比较频繁的库位

二次盘点：
来源：循环盘点、动碰盘点有差异时产生
结果：会产生盘盈盘亏

二次盘点的盘点员不能和循环盘点一样

实时盘点：
-现场有掉落件、黑码商品，用实时盘点扫一个库位，盘上去
来源：没来源，提交后生成盘点结果、盘点单、盘点任务
结果：会产品盘盈盘亏