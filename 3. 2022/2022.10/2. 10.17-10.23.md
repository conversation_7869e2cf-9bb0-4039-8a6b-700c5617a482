# 10 月份 第 2 周

## 本周 todo
本周是值班周+项目管理开发阶段

本周值班宗教及负责人：
周一-基督教-耶稣
周二-印度教-毗湿奴/湿婆/梵天
周三-道家-老子
周四-儒教-孔子
周五-佛教-释迦牟尼
周六-伊斯兰教-默罕默德
周日-犹太教-亚伯拉罕


## 2023.10.17 周一
值班：周一-基督教-耶稣

### life

### work
todo
- 值班
  - 周一灰度发版 ✅
- WMS打印优化
  - 跟测试沟通时间调整 ✅
  - 跟后端沟通接口问题 ✅
- 中英文项目 
  - 跟后端确认接口（进行中
- OFC-61861【前置仓新流程优化项目】【WMS】新增 补货预稽查 页面
  - 开发
- OFC-58792-fix 国家线与安全库存系数配置页面优化
  - 开发 ✅

13:57 忙忙忙！极速歼灭需求

## 2023.10.18 周二
周二-印度教-毗湿奴/湿婆/梵天

### life

### work
todo 
- 值班
  - 周二 生产发版
  - 周二 灰度发版
- WMS打印优化
  - 跟业务沟通（进行中
- OFC-62626【PC-盘点单管理】循环盘点支持操作整箱库位任务 ✅
- OFC-62382【炬星搬运POC项目】【库内】mot -修改- 补货下架 - 领取  ✅
- 中英文项目 
  - 跟后端确认接口 ✅
- OFC-62220【wms打印优化】【一阶段】精准出库扫描
  - 开发

## 2023.10.19 周三
周三-道家-老子

### life

### work
todo
- 值班
  - 暂无
  - 周四需求代码评审 ✅
- WMS打印优化
  - 开发指南更新 ✅
  - 安排明天下午的技术概要评审 ✅
  - todo 上线时 新接口的权限配置问题
  - codereview 成员代码
- OFC-62220【wms打印优化】【一阶段】精准出库扫描
  - 开发

## 2023.10.20 周四
周四-儒教-孔子

### life

### work
todo
- 值班
  - 周四生产mr ✅
- WMS打印优化
  - 跟文辉商量演练的事情 ✅
  - 跟测试商量测试评审 ✅
  - 给yibao建任务 ✅
  - 技术概要评审 ✅
  - 会议纪要 ✅
  - 安排下周项目任务 ✅
  - 改概要设计，补流程图 ✅
  - todo 研究上线时 新接口的权限配置问题
  - codereview 成员代码
- OFC-62952 【业务需求】【wms】盘点结果界面查询条件选择盘点单搜索时不校验盘点时间 ✅
- OFC-62220【wms打印优化】【一阶段】精准出库扫描
  - zpl 模板
  - flexiblePrint
  - 自测

## 2023.10.21 周五
周五-佛教-释迦牟尼

### life

### work
todo
- 值班
  - 巴西的打印问题
- WMS打印优化
  - sotest中建自测任务 ✅
  - 改改概要设计 ✅
  - todo 确认 退供打包/面单补打管理
- OFC-60430 【产品需求】wms 库内/库存部分- 英文名切换【开发】
  - 联调 ✅
- OFC-62382 【炬星搬运POC项目】【库内】mot -修改- 补货下架 - 领取
  - 联调 ✅
- OFC-61861 【前置仓新流程优化项目】【WMS】新增 补货预稽查 页面
  - 联调 ✅
  - https://soapi.sheincorp.cn/application/2916/routes/post_wws_front_replenish_audit_config_query



## 2023.10.22 周六
周六-伊斯兰教-默罕默德

### life

### work
- 弄个待办(下周一再说，这个不是今天的活儿)
  - /basic/putaway-strategy/location-status 运营监控/库内看板/库位状态看板 可上库位数量查看 导出
  - try catch 的错误提示不准
- WMS打印优化
  - todo sotest跟jira 中更新包裹管理相关
  - todo 确认 退供打包/面单补打管理
  - todo 跟支撑要个批量获取系统配置 /wmd/front/config/getConfig
- OFC-60430 【产品需求】wms 库内/库存部分- 英文名切换【开发】 ✅
  - 跟测试对齐
- OFC-61861 补货预稽查+外部参数配置 ✅
  - 联调
- OFC-62220【wms打印优化】【一阶段】精准出库扫描 + 包裹管理
  - 包裹管理 开发 ✅
  - 精准出库扫描 开发 ✅
  - 灵活打印 
  - 精准出库扫描 zpl 模板
    - packagePrintTpl -> packageZpl(进行中) 
    - packageTpl -> 缺失，这个才是真麻烦，但已经找到数据了


- 好消息，找到了zpl的调试工具



10:20 开始工作，早上先光速做完包裹管理的打印功能，并涉及zpl 模板调试，让自己心里稳一些

## 2023.10.23 周日
周日-犹太教-亚伯拉罕

### life

### work

今天就干一件事，调试开发zpl模板

- 标签 16:29 ✅
- 面单 包裹明细


文辉，现在有个问题。降级方案中涉及两个模板，一个是标签，一个是包裹明细。

标签原先是有zpl模板，就是太久没更新，周六调整好了。但包裹明细原先是没有的模板，周日开发的时候遇到问题。

方案 A：包裹明细坚持zpl打印
- 优点：
  - 是zpl打印
- 缺点：
  - 调试困难，依赖外部网站labelary-viewer（周日开发调试时就网站崩过一回，隔天才好
  - 新开发的模板未经数据验证，要2人天的调试时间保底
- 后续：
  - 安排人日继续调试

方案 B：包裹明细改用网页打印
- 优点：
  - 模板 与pdf 服务是用同一个模板，不用调试
- 缺点：
  - 是网页打印，交互上需要额外操作
- 后续：
  - 调研 网页打印包裹明细的打印配置并在文档中输出，降低影响

