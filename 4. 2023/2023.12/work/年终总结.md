# 年终总结

## 要求

总时间预计是3小时，内容围绕KPI，自己做了什么，成果（质、效、量），经验教训，能力提升，来年展望等内容

具体形式不限，最好是PPT，大约15min一位

有参与公线事项的同学，也可以再多写点这部分

希望是个有效的年终总结，大家坐下来一起吃东西，讨论分享下

## 

**质量**
目标要求:
P3及P3以上线上故障为0，P4故障不大于1个，MTTR时间为2h内；测试阶段的缺陷率低于14%； 
指标描述:
开发过程严格规范代码，提高代码质量
自评:
下半年线上故障数为0，MTTR时间在2h内；测试阶段的缺陷率为13.29%

**时效**
目标要求:
项目和需求能够100%以上准时完成；提升研发效率，平均研发时间不得超过5.5d；
指标描述:
提升研发效率
自评:
项目和需求能够100%准时完成；平均研发时效为2.7d；

**自我驱动**
目标要求:
完成5次以上闭环且有后续的主动pdca，展开2次以上的知识分享;
自评:
5次主动pdca：
- 发版工具dutyRobot 的维护和功能迭代，
- BBL 词条嗅探工具findt 的维护和功能迭代
- 前端本地存储管理方案createStorage 的优化和仓库各系统的应用推进
- 前端代码生成工具 auto-soapi 的开发和功能迭代
- wms-la 的node 升级迭代

2次技术经验分享：
- wsk-na/wsk-la 独立部署技术经验分享
- 盘点重构技术方案 调研与分享

**业务**
目标要求:
精通库内的补货、回货、盘点等主要业务，逐步拆分盘点页面逻辑
自评:
- 精通库内的补货、回货、盘点等主要业务，日常负责排查库内盘点相关问题；组织架构切换后，负责拉美仓储的前端业务；
- 下半年产出了盘点重构的技术方案并推进了部分模块的重构工作，因架构变动，已将产出交付给国内团队；


选1～2个2023年重中之重且成功落地的事项进行经验分享，关注以下几点：
内容要求：
包括目标设定的原因、工作开展思路、落地情况以及成功经验总结提炼
关注“HOW”的能力呈现（能辨/能为/能燃）
求精 不求多：把一项工作说透，胜过对多项工作的蜻蜓点水
实事求是，客观清晰
格式要求：
PPT不过度追求形式，关注逻辑和内容
第二部分总体建议4～6页PPT说清楚


dutyRobot 值班萝卜 🤖️

介绍：
值班萝卜是个脚本工具，用于辅助值班人员完成值班工作，包括：整理本周发版需求，生成发版表，创建分支，提mr，整理发版信息等

背景：
海外业务兴起，前端的代码仓库要进行切割，导致代码仓库数量飙升。导致两个问题，一是发版工作量增加，二是发版信息不易查找。为了解决这两个问题，我们开发了值班萝卜工具。

工作开展思路：
1. 通过调研，发现值班工作中的痛点，包括：发版工作量大，发版信息不易查找，发版信息不易维护，发版信息不易追溯
2. 小步快跑，先从最简单的需求开始，逐步迭代，逐步完善
3. 通过不断的迭代，逐步完善了值班萝卜的功能，包括：分支创建，mr 提交，发版表生成等

落地情况：
1. 工具落地后，值班/开发/发版人员 每周的发版工作都变轻松了，而且分支漏合的情况基本消失
2. 工具依旧持续迭代，目前已经支持了国内/海外各区/综合的前端发版工作

### bbl 架构建设

介绍：
海外业务对翻译的要求较高，需要对存量词条和增量词条进行有效的管理。

工作流：
日常需求的新增词条需要在周二下午5点前提供给实施（）

存量词条：
- 后端：
- 前端：

增量词条：
- 后端：已开启BBL的“缺失词条”功能，
- 前端：前端开发了词条导出工具

BBL 的后续规划：
- chatgpt翻译（预计12月底）
- 工单系统（预计1月）

todo:
- [ ]

2～4页PPT，阐述2024年重点工作或项目
如果某个工作或者项目对你特别重要，并且需要通过本次汇报，对齐上下游，获得理解和支持，可以重点呈现，并直接“Q”相关同学。。。（鼓励）

2024年工作重点

1. 稳定性建设
核心是兜住，包括：线上故障，测试阶段的缺陷，研发效率

前端2024的重点工作是稳定性建设，核心是兜得住

针对系统的内部服务和外部服务有对应的告警监控，以及配套的降级方案

例如: pdf，BBL，云配置，极验等

配合和跟进










