# sheinq

## wms-la 原有功能

- index
  - sendGetRequest
    - createErrorProxy(getRequest)
  - sendPostRequest
    - createErrorProxy(methodRequest)
  - sendPostFileRequest
    - createErrorProxy(methodFileRequest)
- default
  - defaultFetch
- base-fetch
  - createErrorProxy
  - methodRequest
  - getRequest
  - methodFileRequest

config

## wms-la 改造

- index
  - // type BaseRequestConfig
  - // sendRequest
  - type PostRequestConfig
  - sendPostRequest
  - type GetRequestConfig
  - sendGetRequest
  - type PostFileRequestConfig
  - sendPostFileRequest

## ofp 参考

- index
  - BaseRequestConfig
  - sendRequest
  - PostRequestConfig
  - sendPostRequest
  - GetRequestConfig
  - sendGetRequest
