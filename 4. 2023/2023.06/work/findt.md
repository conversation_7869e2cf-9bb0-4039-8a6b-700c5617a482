# findt 脚本设计

想让findt脚本进行git操作

背景：
- 我们会针对某个项目进行开发，开发的时候会在某个分支上进行，例如pre-master-XX,XX是项目代号
- findt脚本用于找出t函数的字符串，用于i18n

现在的脚本工作流程:
1. git checkout master
2. git merge origin/pre-master-XX
2. 将findt.js放置到项目根目录
3. 执行命令：node findt.js <commit_id>
4. 如无意外，项目根目录下会生成findt.txt文件，文件内容为改动内容中的去重后的t函数字符串

目标：
想简化流程，脚本集成git 操作
1. 将findt.js放置到项目根目录
2. 执行命令：node findt.js
3. 项目根目录下会生成findt.txt文件，文件内容为改动内容中的去重后的t函数字符串

目前的findt.js脚本内容:
// 文件头部添加
/* eslint-disable @shein-bbl/bbl/translate-i18n-byT */

// findt 用于查找git commit记录中改动的内容中的t函数字符串
// 使用方式：
// 1. 将findt.js放置到项目根目录下
// 2. 执行命令：node findt.js <commit_id>
// 3. 如无意外，项目根目录下会生成findt.txt文件，文件内容为改动内容中的去重后的t函数字符串

const { exec } = require('child_process');
const fs = require('fs');

// commit ID
const commitId = process.argv[2] || 'HEAD';

// 正则表达式匹配模式
const tReg = /(?<=t\(\s*['"`])(.*?)(?=['"`]\s*(?:\)|,|\s*?\)))/g;

// 执行git show命令获取commit记录的改动内容
exec(`git diff ${commitId}^ ${commitId}`, (error, stdout) => {
  if (error) {
    console.error(`${'执行命令出错: '}${error}`);
    return;
  }

  // 使用正则表达式匹配t函数的字符串
  const matches = stdout.match(tReg);

  if (matches && matches.length > 0) {
    // 使用Set对匹配结果进行去重
    const uniqueMatches = [...new Set(matches)];

    // 创建结果文件
    const filename = 'findt.txt';
    fs.writeFile(filename, uniqueMatches.join('\n'), (err) => {
      if (err) {
        console.error(`${'写入文件出错: '}${err}`);
        return;
      }
      console.log(`${'改动内容中的去重后的t函数字符串已保存到 '}${filename}`);
    });
  } else {
    console.log('改动内容中未找到符合条件的t函数字符串');
  }
});
