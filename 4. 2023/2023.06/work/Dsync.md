# dangerSync 

## 第一版
背景：我们竟然遇到一个需求需要同时开发国内和波兰，而波兰的系统一开始就是从国内拷贝过来的，但是我们又不能每次都手动拷贝，所以我们需要一个自动化的工具来帮助我们同步国内和波兰的系统。

帮我写个脚本，国内写完后，能进行git操作，将改动的内容同步到波兰的系统中

我现在的做法是

一个项目中同时git remote了两个仓库，一个是国内的(wms)，一个是波兰的(wms-pl)。在国内的分支OFC-1 上开发完之后，git push OFC-1 wms,git reset HEAD^ --soft，接着git stash, 接着git checkout -b OFC-2 wms-pl/master, git stash pop, git add .

你看看能不能做成一个脚本，并进行优化

传入参数：国内分支名，波兰分支名(两个分支名可能存在重复的情况)



(wms/OFC-1)git reset HEAD^ --soft
(wms/OFC-1)git stash
(wms/OFC-1)git checkout -b fn-copy wms-pl/master
(wms-pl/fn-copy)git branch -D OFC-1
git checkout -b OFC-2 wms-pl/master
git cherry-pick <COMMIT_ID>

获取到 仓库a和仓库b 的git 远程仓库地址、在脚本下执行 git remote add a <仓库a地址>，git remote add b <仓库b地址>，然后执行 git fetch a，git fetch b，git checkout -b <新分支名> b/master，git cherry-pick <COMMIT_ID>，git push a <新分支名>，git push b <新分支名>，git checkout <原分支名>，git branch -D <新分支名>，git remote rm a，git remote rm b

## 第二版

需要将A仓库的branch-1分支的改动同步到B仓库的branch-2分支中去，请帮忙写个js脚本完成这个工作

背景：
branch-1是从A仓库的master分支上切出来的，branch-2也是从B仓库的master分支上切出来的

B仓库之前是从A仓库中fork出来的，但是后来B仓库的master分支和A仓库的master分支已经不同了

branch-1和branch-2是指令的入参，可以是任意分支名

想要脚本做的是：

- 根据AB仓库的本地文件路径，获取远程仓库路径
- 检查缓存，如果有缓存，就不需要clone仓库了；没有缓存，则创建临时目录，用于clone AB仓库
- 将clone出来的A仓库branch1分支的改动(指从master分支切出来之后的所有改动)同步到clone出来的B仓库branch2分支中去
- 将clone出来的B仓库branch2分支的改动文件复制到B仓库的本地文件路径

