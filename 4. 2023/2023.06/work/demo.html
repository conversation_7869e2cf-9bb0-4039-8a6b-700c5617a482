<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            font-family:serif;
        }
        .box{
            display: flex;
            height: 19.5mm!important;
            width: 70mm;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 0!important;
            margin: 0!important;
            page-break-after: always;
            overflow: hidden;
            font-size: 12px;
        }
        .header {
            display: flex;
            width: 62mm;
            height: 4mm;
            padding-top: 1mm;
            justify-content: center;
        }

        .header .skc{
            flex: auto;
        }

        .header .size{
            flex: 0 0 30%;
        }

        .barcode {
            display: inline-block;
            vertical-align: top;
            width: 62mm;
            height: 7mm;
            margin-top: -0.5mm;
        }

        .footer {
            display: flex;
            width: 62mm;
            flex-direction: column;
        }

        .footer .firstRow, .footer .secondRow {
            display: flex;
            height: 4mm;
        }

        /* .footer .firstRow {
            padding-top: 0.5mm;
        } */

        .footer .firstRow .left, .footer .firstRow .right, .footer .secondRow .left, .footer .secondRow .right {
            flex: 1 0 50%;
            text-align: center;
        }
    </style>
</head>
<body>
    
        
        
            <div class="box">
                <div class="header">
                    <div class="skc">sx2301214816772711</div>
                    <div class="size">CN37</div>
                </div>
                <div>
                    <img class="barcode" src=data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALQAAAAjCAYAAAA9riDJAAAAAXNSR0IArs4c6QAAAlBJREFUeF7tnMGSgzAMQ+H/P5pOS2hjT4zUhOPby053gaaq4khyYN+27di6n+M4X+77/vl9vb4Ouf7en+Mcl697na+u7/4/jyufV41/dlxq/ArHjF/GW30e93twP3c1XpcHFd7q/SscKn4pnN6shdDdBFZEdScyhI5IqQnqFkwInRhYAasAdytiBbhbwZ5eMVSFVOOlQjcEnyIOkuMEVOFQrSwQGg29RCBV8bJkyYSjQp/eDQ3dTOxqpUJDxzAAyYHkCBW+IgQpR8givnAor4IpxBR+EFCehtguEUWZlllt+NR1XXOE5EByWCYKQo+XWExhrIxIjpTKYAo9bepKDEwhphBTOIjdVOOqkoS5QGEKMYWYwt4VK1Pmmig0NBq6ry3u5jEqdEPABWx2oqmJzOake7Pnfj8QGkIPFCw5NPuhW3rimhUVv7HbLu7RoEIvpic0VsaEUpupiO0WiafiHdXCJYcmh+aOFe5Y+e63VhVbrXRDAW/g60o7cmhyaHJocuj5O0TcCpcrmao8s/0AVVHVeNHQaGha37S+fwhgCuNjJFQDJ3PHjcPYnFRUXhfw2Y6cWmrd67pLr/t51HF0CukU3pqXVUKunq/26dJY8faq/Bur5uOV1+BBM+Z+aggdCaukYWV6IbRoQSM5InXQ0DwKbErqoKG9vRlK6qlKjuRoCMxWKkVUlSqoXHeQaIUJ5a44rgRShFLjJYcmhyaHJocmh1buXcWX/5otcmhy6KFaqIhGDk0OPWXOXM3pakk0NA+aCZrxaeKopRZCE9v1CLwAHxP7iPDoCi4AAAAASUVORK5CYII=>
                </div>
                <div class="footer">
                    <div class="firstRow">
                        <div class="left"></div>
                        <div class="right">&&-818黑色</div>
                    </div>
                    <div class="secondRow">
                        <div class="left">Made In China</div>
                        <div class="right"></div>
                    </div>
                </div>
            </div>
         
    
        
        
    
        
        
    
        
        
    
        
        
    
        
        
    
</body>
</html>