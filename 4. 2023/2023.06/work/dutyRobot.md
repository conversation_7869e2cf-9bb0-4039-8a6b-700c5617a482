# shift robot

萝卜

## 一阶段炼丹

createPreMaster/createMR/filterTag

prompt:

帮我写个js脚本

给几个代码仓库基于master创建远程分支，分支名为pre-master-<名称>，如果没传入参数，则默认为pre-master-<当前月日>，例如pre-master-06-25

## 二阶段炼丹

自动填写 前端发版表格

autoTable

先整理需要用到的jira sql 语句

1. 筛选器：获取jira的任务菜单获取上线版本号

'project = OFC AND fixVersion = '+options.w

2. 筛选器：版本为"+options.w+"的需求里面的前端任务 ....

'project = OFC AND issuetype in (前端任务) AND status = 已完成 AND assignee in membersOf("产品研发中心-供应链研发部-仓储研发部-仓储前端部") AND issue in linkedIssuesInQuery("project = ofc AND issuekey in ('+ofc_data+')")', 

2. 筛选器：67925【wms】前端完成未上线需求 

project = OFC AND issuetype in (业务需求, 产研自提需求, 配合需求, 产品需求) AND 所属系统 in (供应链仓储线, "WMS(产品岗选这个)", OWMS（产品岗选这个）) AND status in (开发中, 测试中, 产品验收中, 业务验收中, 待上线) AND issue in linkedIssuesInQuery("project = OFC AND issuetype in (前端任务) AND status = 已完成 ")

需要的信息：

需求健值/代码分支/开发者/涉及系统/备注

issueKey/前端isssueKey/开发者

开始处理 涉及系统

17:00 脚本基本开发完成，还补充了文档，暂时先这样，这周看看什么时候有空过下脚本。





