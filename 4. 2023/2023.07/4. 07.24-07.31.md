# 7 月份 第 4 周
## 本周 todo


## 2023.07.24 周一
### life

### work
todo
- 修bug
  1. 【动态管理】鼠标hover在动态布局列表页数据后已开，展示的更多选项弹窗没有隐藏
    - 组件问题
  2. 【动态管理】动态布局列表页，样式选择下拉框选项取值错误
    - 组件问题
  5. 【动态布局】复制ABT分支数据时，需要禁用样式修改，目前可以修改样式 ✅
    - 漏看需求
  4. 【动态管理】复制列表默认分支数据，还可以添加ABT分支并保存，需要限制不能添加ABT ✅
    - wiki 没提
  6. 【动态列表】列表中ABT分支数据，不管什么状态都要禁用添加ABT的按钮 ✅
    - wiki 没提
  3. 【动态管理】新增ABT分支数据，ABT参数下拉框需要去除默认配置选项，只保留A-Z ✅
    - 属于优化 
  7. 【动态管理】给默认分支数据添加ABT，DSL表单内容不能修改的，提示文案需优化
    - 属于优化 Cannot edit in read-only editor
- 权限问题设置好

## 2023.07.25 周二
### life

### work
todo
- 修bug
  1. 【动态管理】鼠标hover在动态布局列表页数据后已开，展示的更多选项弹窗没有隐藏
    - 组件问题
  2. 【动态管理】动态布局列表页，样式选择下拉框选项取值错误
    - 组件问题
  5. 【动态布局】复制ABT分支数据时，需要禁用样式修改，目前可以修改样式 ✅
    - 漏看需求
  4. 【动态管理】复制列表默认分支数据，还可以添加ABT分支并保存，需要限制不能添加ABT ✅
    - wiki 没提
  6. 【动态列表】列表中ABT分支数据，不管什么状态都要禁用添加ABT的按钮 ✅
    - wiki 没提
  3. 【动态管理】新增ABT分支数据，ABT参数下拉框需要去除默认配置选项，只保留A-Z ✅
    - 属于优化 
  7. 【动态管理】给默认分支数据添加ABT，DSL表单内容不能修改的，提示文案需优化
    - 属于优化 Cannot edit in read-only editor
- 权限问题设置好 ✅
- 开发第三点需求

## 2023.07.26 周三
### life

### work
todo
- 开发需求
- 需求评审 ✅
- 灰度的问题修复

接口调整同步，部署服务test06，新增运营页面开关属性operationalPage

1、页面创建接口新增是否运营位属性 ✅
http://cccx-admin-server-cneast-test-0006.test.paas-test.sheincorp.cn/doc.html#/default/%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE-%E9%A1%B5%E9%9D%A2%E7%B1%BB%E5%9E%8B%20API/createUsingPOST_24

2、页面详情接口返回是否运营位属性 ✅
http://cccx-admin-server-cneast-test-0006.test.paas-test.sheincorp.cn/doc.html#/default/%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE-%E9%A1%B5%E9%9D%A2%E7%B1%BB%E5%9E%8B%20API/infoUsingGET_7

3、页面列表接口新增是否运营位筛选属性 ✅
http://cccx-admin-server-cneast-test-0006.test.paas-test.sheincorp.cn/doc.html#/default/%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE-%E9%A1%B5%E9%9D%A2%E7%B1%BB%E5%9E%8B%20API/listPageUsingGET_14

4、新增页面列表查询接口（目前供查询非运营页面使用）
http://cccx-admin-server-cneast-test-0006.test.paas-test.sheincorp.cn/doc.html#/default/%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE-%E9%A1%B5%E9%9D%A2%E7%B1%BB%E5%9E%8B%20API/listUsingGET_3



## 2023.07.27 周四
### life

### work
todo

估时
1	问题列表 接入AI自动分类处理	P1 3h
2	AI分类管理	P2 1.5d
3	反馈问题列表筛选优化	P1 1.5d
4	易用性问题详情页增加 删除易用性问题功能	P2 3h
5	易用性问题换绑问题	P2 6h
6	反馈问题列表页增加反馈不准逻辑	P2 6h
7	交互优化	P1 1.5d

4.5d+18h


## 2023.07.28 周五
### life

### work
todo
- 热区bug ✅
- 需求12的修复 ✅
- 需求3的开发

nonOperationalPage

痛苦，总会有漏的，还不如伪装成一个已有的pageType，就personalCenter吧

线索：
EPageType

新增入参:
pageTypeMode、pageTypeName


## 2023.07.29 周六
## 2023.07.30 周日
### life

### work
todo
1. 问题列表 接入AI自动分类处理	P1 【3h】(✅ 原本就有)
3. 反馈问题列表筛选优化	P1 【1.5d】
4. 易用性问题详情页增加 删除易用性问题功能	P2 【3h】(标记了位置)
5. 易用性问题换绑问题	P2 【6h】
7. 交互优化 P1 【1.5d】
  1. 问题模块配置 - 模块名称支持编辑，修改后只影响epp内的模块展示名称(✅)
  3. 反馈列表处理按钮优化 ，跟进露出，其他hover展开
  4. 批量处理按钮移动至翻页器左下
  5. BUG：自适应要给译文和原文
  6. 问题列表页，当选中待处理、不处理、已退回状态时，列表中去掉“处理状态“字段
  7. 列表内增加 AI 分类标识，区分关键词分类标识；运行AI分类的条目使用新icon，运行自动分类的保持原icon


遇到最奇葩的问题是项目跑不起来，今天算是白来了，周一再问问吧

## 2023.07.31 周一

todo
- 动态布局
  - 复制/添加ABT分支，参数没有带过去 ✅
  - 详情 基本信息空白 ✅
  - 新建配置 下一步 报错
- 接入ai分类

## 2023.08.01 周二
todo
- 接入ai分类
  - 后端协调联调事宜
- 动态布局 bug
  - 新建配置 下一步 报错
  - 全面借用广告位配置先，确认是否有问题 ❌ 即使是用PersonalCenterCondition 还是同样问题


todo
1. 问题列表 接入AI自动分类处理	P1 【3h】(✅ 原本就有)
3. 反馈问题列表筛选优化	P1 【1.5d】
4. 易用性问题详情页增加 删除易用性问题功能	P2 【3h】
5. 易用性问题换绑问题	P2 【6h】
7. 交互优化 P1 【1.5d】
  1. 问题模块配置 - 模块名称支持编辑，修改后只影响epp内的模块展示名称(✅)
  3. 反馈列表处理按钮优化 ，跟进露出，其他hover展开 ✅
  4. 批量处理按钮移动至翻页器左下 ✅
  5. BUG：自适应要给译文和原文 ✅
  6. 问题列表页，当选中待处理、不处理、已退回状态时，列表中去掉“处理状态“字段 ✅
  7. 列表内增加 AI 分类标识，区分关键词分类标识；运行AI分类的条目使用新icon，运行自动分类的保持原icon

