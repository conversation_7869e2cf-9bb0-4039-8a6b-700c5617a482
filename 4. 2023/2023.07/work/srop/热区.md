# CCCX--热区&时间转化交互调整

## 需求概览
1. 图片尺寸过大超出弹窗限定的绘制区域时，增加横向、纵向滚动条 ✅
2. 热区绘制拖动效果优化：绘制热区时，若拖动到弹窗边界且未到图片边界时，自动触发滚动条并保持绘制状态，当拖动至图片边界时，自动释放，完成热区绘制，确保热区不超出图片边界
    - (有点麻烦)
3. 当“底色选择”字段设置为无填充时，画布中组件区域取消上下左右预留的边距；当“底色选择”字段设置为纯色/渐变时，正常保留边距
    - bgColorType 

## 需求1 ✅
图片尺寸过大超出弹窗限定的绘制区域时，增加横向、纵向滚动条

## 需求3 ✅
当“底色选择”字段设置为无填充时，画布中组件区域取消上下左右预留的边距；当“底色选择”字段设置为纯色/渐变时，正常保留边距

bgColorType

TimeTransformCanvas 具体开发位置点

bgColorType 原本在formData中

11:30 ✅

## 需求2 
热区绘制拖动效果优化：

绘制热区时，

当拖动到弹窗边界且未到图片边界时，自动触发滚动条并保持绘制状态，
当拖动至图片边界时，自动释放，完成热区绘制，确保热区不超出图片边界

先不考虑到边的问题，先考虑加入滚动条后，

怎么知道到边呢？

16:42 继续完成，但是怎么实现持续滚动

17:06 他妈的怎么持续滚动

17:25 超长时的双滚动条问题 好像不是问题，等再次出现再试试

jhqqghf065

还是要处理持续滚动的问题


- 鼠标拖拽 定时执行 ✅
- 边界判断 ✅
- 子组件 onScroll
- finshHotZoneDraw 的rect 数据问题处理
- 代码优化
- code review


