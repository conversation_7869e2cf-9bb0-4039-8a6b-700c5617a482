# 晋升材料

## 责任贡献	

1、独立完成符合规范、满足需求功能的代码需求 
2、解决常见的性能问题（了解常见的性能问题、如何避免） 
3、熟悉所负责的业务，在需求讨论调研与评审阶段中，能针对产品需求提供合理的建议 
4、【B端方向】兼容性解决：在87%（使用量）以上的浏览器运行是正常工作【计算公式：报错数/PV<13%】 
5、【B端方向】产出2个系统以上共用的业务组件/模块/插件 【C端方向】*产出2个以上共用的业务组件/模块/插件 
6、【C端方向】*熟练运用日志工具排查并解决线上问题

基于最近1~2年所承担的责任目标、创造的工作成果来举证，可参考以下角度：
1、工作职责（责任目标、影响范围等）
2、角色（发挥什么作用——例如在指导下执行、参与协助、独立执行、主导推进、带团队实施等）
3、成果和价值（如收入、效率、机会等）
4、目标完成情况及内外部客户认可

1. 担任wsm库内业务前端负责人，日常参与库内需求的评审、开发、测试、上线等工作，同时负责需求的管理工作，包括需求分配、技术指导、团队建设等工作。
2. 【B端方向】产出过3个业务组件: StretchMenu、WmsSearchArea、createStorage
3. 担任「WMS打印优化项目」的项目经理，主导推进了项目的开发、测试、上线等工作，为出库核心打包业务提供本地打印功能，提升了核心业务的稳定性。

## 专业能力

https://wiki.dotfashion.cn/pages/viewpage.action?pageId=1075070510


1. 针对公共JSON配置的序列化出错导致系统白屏问题，提供了前端本地存储管理方案并输出相应的toolKit: createStorage，在仓储系统中得到了广泛的应用，例如：wms/mot/wsk/wms-outbound/wms-inbound等。

2. 为支持黑五活动，提升出库打包业务的稳定性，提出前端项目「wms 打印优化」，提供了wms打印的本地降级方案。通过zpl打印机驱动，实现了本地打印功能，避免网络服务不稳定导致的打印失败问题。在2022年黑五活动中提供了技术支持，保证了打包核心业务的稳定性。

3. 主动关注前端发版问题，提出了前端发版的自动化方案，通过gitlab和jira的集成，实现了前端发版的自动化，提升了前端发版的效率，降低了发版的风险。 

dutyRobot

## 三能力

## 能辨	

响应客户：积极倾听客户需求，做出及时有效地响应 分析问题：能通过独立的思考和推导，解决工作中例行的问题

在2022年pdf 服务网络问题出现后，积极响应，提出了「wms打印优化」项目，保障了出库核心打包业务，并在2022年黑五前上线

## 能为	
快速行动：主动澄清工作目标，快速采取行动，确保工作结果按质按量交付 持续学习：持续自主寻找并利用学习机会，迅速吸取并应用新知识、技能或方法

主动关注到公司在推进海外仓库导致wms前端项目可能导致的发版效率降低和可能导致的发版出错风险，主动开发dutyRobot 脚本工具，从提出到第一版落地仅需一周，并持续进行版本迭代，满足需求

## 能燃	

高效沟通：根据不同情况采取有效的方式进行沟通表达，确保彼此充分理解，达成共识

在2023.07-2023.08 期间，支援aidc 部门完成ccc项目的v3.0.0 版本重构。在技术栈与wms部门差异较大的情况下，快速上手系统的开发工作，在1个月内按时完成了3个需求，为ccc项目的上线提供了技术支持，并获得了认同





