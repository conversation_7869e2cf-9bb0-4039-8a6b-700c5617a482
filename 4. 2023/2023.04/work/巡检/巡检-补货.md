# 巡检补货

## 巡检
巡检补货业务，确保前端相关功能正常运行，以及数据正常。

包括：
- 生成(生成补货任务)
- 下发(下发任务/任务指派)
- 补货下架
- 装托扫描
- 移位上架

## 背景和相关名词介绍

- 补货是干什么的？
- 补货是将存在于备货区中近期不销售的商品，从备货区补货至拣货区的一个行为，他的目的是为了保证拣货区有货可拣。补货类型分为两种，分别为**日常补货**、**紧急补货**。

- **日常补货**
  - 参考最近7天历史销量进行有预见性的从备货区往拣货区补货，采取整库位下架的操作。其生成周期为：每天4次，分别是5:30、12:30、17:30、22:30（包含村站、卫星仓）
- **紧急补货**
  - 是销售订单需求的商品在拣货区没有足够可用库存时，从备货区触发缺货件数的补货。其生成周期为：每小时1次，每逢整点生成（村站为9:00、16:00）
- 短拣
  - 是指在补货过程中，由于拣货区库存不足，导致补货下架的数量小于补货上架的数量，这种情况下，需要将补货上架的数量进行回退，以保证拣货区库存不变。
- 整箱/非整箱

## 涉及页面
核心功能页面：
- 【wms】库内管理/补货管理/补货分析
  - 路由：/statistical/replenishment-analysis
  - 功能：生成日常补货任务
- 【mot】补货下架 
  - 路由：/order-picking/replenish-down
- 【mot】装托扫描
  - 路由：/scan-new/load
- 【mot】移位上架
  - 路由：/put-shelves/shift-up

相关配置页面：
- 【wms】系统配置/库内配置/交接流程配置
  - 路由：/basic/handover-process-configuration
  - 功能：看配置
- 【wms】库内管理/补货管理/补货查询
  - 路由：/statistical/replenishment
- 【wms】海外补货配置
  - 路由：/basic/overseas-replenish-config
  - 功能：海外补货相关
- 【wms】补货策略配置
  - 路由：/basic/overseas-replenish-config
  - 功能：海外补货相关

## 业务流程

流程：

生成补货数据->盘点->生成补货任务->下发任务/任务指派->补货下架->装托扫描->领取上架任务->移位上架

## 数据来源
2.1 紧急补货数据
2.1.1 实时盘点 在【pda-库内管理-盘点-实时盘点】
2.1.2 执行定时任务

往“紧急补货表”里插一条数据，再往库位上盘商品进去，然后执行定时任务，就可以生成紧急补货任务了。

2.2 日常补货数据：
方法一：手动生成日常补货数据
方法二：定时生成日常补货数据

2.2.1 实时盘点 在【pda-库内管理-盘点-实时盘点】
2.2.2 执行定时任务

在补货分析中选择数据，生成日常补货

这里选择“紧急补货”的形式，生成补货任务

1、添加库存
http://wmsauto-test01.shein.com/#/invemntoryIn

指定库位，skuCode,数量，然后点击“添加库存”

2、往缺货记录表中写入数据
利用：http://wms-auto-new-wmspyauto-test.test.paas-test.sheincorp.cn/py/docs/#/WSS-API/post_py_wss_perform_tasks

也可以用接口——可见wiki https://wiki.dotfashion.cn/pages/viewpage.action?pageId=1087341040

3、执行job


## 操作流程
【补货下架】-【装托扫描】-【移位上架】
3. 补货下架 操作步骤
3.1 任务下发
3.2 非整箱补货下架
3.3 非整箱短拣
3.4 整箱补货下架
3.5 整箱短拣

4. 移位上架 操作步骤
补货下架之后，就需要进行上架操作，该如何走上架操作呢，可见【系统配置-库内配置-交接流程配置】中的补货业务，交接流程可自行进行配置，为了方便，通常也就配个装托扫描和领取上架任务。

4.1 装托扫描

4.2 领取上架任务

4.3 移位上架


