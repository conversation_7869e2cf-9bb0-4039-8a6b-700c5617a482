# 理货

## 巡检
巡检理货业务，确保前端相关功能正常运行，以及数据正常。

包括：
- 生成(生成理货任务)
- 下发(下发任务)
- 回货下架
- 装托扫描
- 移位上架

## 背景和相关名词介绍

- 理货，是升级版回货，从abc上游提供
- 回货
  - 回货是将拣货区近期销售不好的商品挪至备货区，目的是为了让更多销量更好的sku可以存放至拣货区，实现更快出库。
- 快流/慢流

## 涉及页面
核心功能页面：
- 【wms】库内管理/回货管理/理货管理 
  - 路由：/in-warehouse/return-stock-manage/tally-manage
  - 功能：生成日常补货任务
- 【mot】回货下架 
  - /order-picking/back-down
- 【mot】装托扫描
  - /scan-new/load
- 【mot】移位上架
  - /put-shelves/shift-up

相关配置页面：
- 【wms】系统配置/库内配置/国家线与库存 
  - /management-sys/rolls-configuration/national-line-inventory
- 【wms】系统配置/库内配置/库龄等级配置
  - /basic/goods-age-grade
- 【wms】系统配置/库内配置/存储等级库存配置 
  - /basic/goods-store-grade
- 【wms】系统配置/库内配置/交接流程配置
  - /basic/handover-process-configuration

## 业务流程
流程：

生成理货任务->下发任务->回货下架->装托扫描->领取上架任务->移位上架

## 数据来源
生成理货任务

## 操作流程
生成回货任务->下发任务->回货下架->装托扫描->领取上架任务->移位上架
