# 回货

## 背景和相关名词介绍

- 目测已废弃，现在采用了理货，待确认

- 回货是将拣货区近期销售不好的商品挪至备货区，目的是为了让更多销量更好的sku可以存放至拣货区，实现更快出库。
- 回货不同于拣货的是，回货时，若有历史销量时，要进行回货时，不可以去占用历史销量*2个库存的。如：预计回货件数200，现备货区库位数分别为30、30、30、100、200（30+30+30+100<200，30+30+30+100+200>200），本来如果历史销量为0，则现在备货区所有库位数都应该回货，但是现在因为历史销量不为0，则不能占最后一个库存为200的数据，且回货占库位时，从小的开始占起。


## 涉及页面
核心功能页面：
- 【wms】库内管理/回货管理/回货查询
  - /in-warehouse/return-stock-manage/return-stock-query
- 【mot】回货下架
  - /order-picking/back-down
- 【mot】装托扫描
  - /scan-new/load
- 【mot】移位上架
  - /put-shelves/shift-up

相关配置页面：
- 【wms】系统配置/库内配置/交接流程配置
  - /basic/handover-process-configuration

## 业务流程
流程：

生成回货任务->下发任务->回货下架->装托扫描->领取上架任务->移位上架

## 数据来源


## 操作流程
【回货下架 】-【装托扫描】-【领取上架任务】-【移位上架】

1. 回货下架 

进入【PDA-回货下架】页面，输入补货周转箱，点击回车按钮。此时页面显示相应信息，输入相应信息，点击回车按钮。所有“待下架”数据若未下架完成，则状态为“下架中”，若全部下架完成，则状态变为“待上架”。其中该任务的明细页面，已下架的明细，状态由“待下架”变为“已下架”

2. 装托扫描

进入【PDA-交接扫描-装托扫描】页面，输入回货下架时使用的补货周转箱，托盘使用空闲状态的数据，点击回车，然后进行关托操作。

3. 领取上架任务

进入【PDA-上架-领取上架任务】页面，输入回货下架时的补货周转箱，点击回车后，显示任务单号后，点击右下角的关单按钮。

4. 移位上架

进入【PDA-上架-移位上架】页面，输入补货周转箱，部分上架后，任务明细由“待上架”变为“上架中”，任务明细页面，状态仍为“已下架”。然后无论点击箱空还是回车，将所有数据都上架，该任务的状态都为“已上架”，同时任务明细一直为“已下架”