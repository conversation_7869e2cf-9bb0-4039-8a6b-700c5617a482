# 4 月份 第 2 周
## 本周 todo
4/12 产生了离职的想法💡，AI太吸引人了，想加入

## 2023.04.10 周一
### life

### work
todo
- OFC-88341 【wms】【前端本地存储】文档整理 & 手动处理剩余部分
- 让ai生成wms模板（这个想法真是太令人兴奋了🥰）

改动1:
JSON\.parse\(\s*([a-zA-Z0-9_]+\.getItem\(\))\s*\)
$1

改动2:
(\w+)\.setItem\(JSON\.stringify\(([\s\S]*?)\)\)
$1.setItem($2)

涉及到的storage:
boardOutstockMonitoringStorage
replenishmentProgressBoardSubWarehouseIdsStorage
replenishmentUpperSubWarehouseIdsStorage
combinationWellenOfBoxCollectStorage
wechatUserCodeStorage
selectedDataIndexExceptionPackageStorage
collectOperationListStorage
returnSubwarehouseStorage
returnAreaStorage
outboundTasksReplenishDetailNewStorage
managementSysUserManageStorage
wmsTopTabsStorage
outboundTasksTaskStorage
notificationObjStorage
qmsDefectiveReturnRecordQueryStorage
selectedDataIndexReceiveQcDetailManageStorage
specialOutSpecialSpecialPickTaskStorage
specialOutSpecialSpecialPickTaskDetailStorage
specialWellenManageListStorage
selectedDataIndexTransferBillStorage
selectedDataIndexTransferBillDetailStorage
quickEntryObjStorage
wmsMenuObjStorage
wmsUserRoleListStorage

## 2023.04.11 周二
### life

### work
todo 
- 写mot脚本
  1. 先把常量换回来（还是用脚本吧，人工操作有危险）✅
  2. 统计所有的key，保证对应有对应的方法 ✅
  3. 转换脚本用的格式 ✅
  4. 跑脚本 ✅
  5. 找JSON相关的key，统计 
  6. 统计对应的测试点 

改动1:
JSON\.parse\(\s*([a-zA-Z0-9_]+\.getItem\(\))\s*\)
$1

改动2:
(\w+)\.setItem\(JSON\.stringify\(([\s\S]*?)\)\)
$1.setItem($2)


脚本有漏网之鱼，需要手动处理

本次只改动对对象类型的key，请务必要保证这点


## 2023.04.12 周三
### life

### work
- OFC-88336	前端存储优化
  - 【wms】OFC-88341
  - 【mot】OFC-88628

todo
- OFC-88632 createStorage集成到npm包中
- 写wms跟mot的测试范围(中午前) ✅
- JSON试水，保证
  - getItem 与JSON.parse ❌ 
    - 不行，JSON.parse(text[, reviver])，必须是字符串
    - 会系统报错 Unhandled Rejection (SyntaxError): "[object Object]" is not valid JSON 
    - 场景复杂，无法保证
  - setItem 与JSON.stringify ✅
    - 兼容，即使漏改了，也不会影响
- mot 的getItem()
- 制定codereview 的表格和计划
- 本期对象类型的先不改，风险可控了不少

- 对象类型的风险太大，转弯，只改基本类型的

- 重新统计key 
  - mot
  - wms

赶紧做完手上的活，赶紧折腾ai，要做好准备了

16:44 好了，开始折腾潘多拉

## 2023.04.13 周四
### life

### work
todo
- OFC-88558【拣货区整箱存储项目】
  - wms
    - 【库内任务管理/任务明细】列表 增加-操作模式字段 ✅
    - 【移位单管理/移位明细】列表 增加-操作模式字段 ✅
  - mot
    - 【移位上架】扫描库位/提交数据 逻辑变更 ✅
    - 【补货下架】配合后端改动(前端交互逻辑无改动) ✅
    - 【容器号查询】补货周转箱新增展示字段 ✅

16:34 完事儿了，开始折腾潘多拉

## 2023.04.14 周五
### life

### work
todo
- OFC-88558【拣货区整箱存储项目】
  - 联调
- pandora 计划

- 先本地nodejs 跑起来openai的demo ✅
- 读api 文档

- 聊天窗口遮盖层，很炫酷


## 2023.04.15 周六
## 2023.04.16 周日
### life
