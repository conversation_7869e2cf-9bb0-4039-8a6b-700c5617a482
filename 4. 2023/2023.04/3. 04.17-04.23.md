# 4 月份 第 3 周
## 本周 todo


## 2023.04.17 周一
### life

### work
todo
- OFC-89578 补货相关巡检手册整理
  - 补货
  - 理货/回货

今天主要是写文档为主
- 先看测试的资料 
- 输出基础的文档
- 在看产品的资料
- 修正文档，并总结问题来问产品跟测试，先垫垫底，少问无效问题

想要巡检补货跟理货的难点在于生成任务，手动生成任务有点麻烦

## 2023.04.18 周二
### life

### work
todo
- OFC-89578 补货相关巡检手册整理
  - 补货
- 需求评审

- 今天要尽快把文档赶出来，下午要参加需求评审，要攒出时间来搞“页面仔”

## 2023.04.19 周三
### life

### work
todo
- 页面仔
  - web端
    - 样式 ✅
    - 通信 websocket ✅
    - 发出请求 ✅
  - node端
    - 通信 websocket ✅
    - 接入openai ✅
    - prompt处理
- OFC-90152 【WMS-补货】补货查询，增加"付款时间"筛选 ✅
  - wms
  - wms-pl
- 前端本地存储改造
  - OFC-88341 wms
  - OFC-88628 mot
- OFC-90044	理货相关巡检整理

抓不关屏幕事件：
- 优秀的猎人往往以猎物
- 仔细看，那个其实是屏保，故意的，里面每一条都是虚假信息，可以根据信息反向追踪

## 2023.04.20 周四
### life

### work
todo
- 巡检理货 ✅
- 页面仔
  - prompt 处理
  - loading
  - 多状态识别
  - 连接gpt 4 (可以看看chatGPTBox，主要还是接口访问问题)


## 2023.04.21 周五
### life

### work
todo
- fix-master-approval-detail 审批单滚动条修复 ✅
- OFC-90542 【wsk】【wsk-pl】新建工单中调整「退供通知单」字典取值 RejectTaskStatus 改成 ReturnNoticeStatus ✅
- OFC-90597	 【稳定性建设】排查盘点扫描库位接口传参问题 ✅


页面仔
- todo 处理prompt
  - 访问模拟：gpt4 网页版（毕竟还没有gpt4的api）
  - prompt处理
    - node端的message是完整的
    - web端的message是省略的，因为包含了生成文件的信息


## 2023.04.22 周六
## 2023.04.23 周日
### life

长沙行 2日行

周六
- 7:22 高铁
- 10:XX 到长沙

数不尽的茶颜悦色和2次黑色经典
黑色经典-茶颜悦色- 吃了粉 - 长沙文和友

方脑壳

糖油粑粑
紫苏桃子
米酿

周日
长沙博物馆