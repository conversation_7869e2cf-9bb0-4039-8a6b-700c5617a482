import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { t } from '@shein-bbl/react';
import {
  Input, Select, Rule,
} from 'shineout';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import DateRangePicker from '@shein-components/dateRangePicker2';
import store, { defaultLimit } from '../reducers';

const rules = Rule({
  timeRange: {
    func: (val, formData, callback) => {
      if (!formData.startTime || !formData.endTime) {
        callback(new Error(t('时间不能为空')));
      }
      // 限制可选时间段最大不超过7天
      if (moment(formData.endTime)
        .diff(moment(formData.startTime), 'days', true) > 7) {
        callback(new Error(t('所选时间段不能超过{}天', 7)));
      }
      callback(true);
    },
  },
});

class Header extends Component {
  render() {
    const {
      loading,
      limit,
      preSubWarehouseList,
      receiptParkTypeList,
      receiptSubWarehouseList,
      handleLinkList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          // labelStyle={{ width: 90 }} // 修改label宽度
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => {
            store.changeLimitData(formatSearchData(defaultLimit, val));
          }}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，应该将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => { store.changeData({ formRef: f }); }}
          collapseOnSearch={false}
          clearUndefined={false}
          alwaysVisible={[t('收货园区'), t('收货子仓'), t('创建时间')]}
        >
          <Input
            label={t('物资交接单号')}
            name="handoverCode"
            type="text"
            placeholder={t('请输入')}
            clearable
          />
          <Select
            label={t('处理节点')}
            name="handleLinkList"
            data={handleLinkList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictCode || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            placeholder={t('全部')}
            clearable
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase()
              .indexOf(text.toLowerCase()) >= 0}
          />
          <Select
            label={t('收货园区')}
            name="receiptParkType"
            data={[{ dictCode: '', dictNameZh: t('全部') }, ...receiptParkTypeList]}
            required
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictCode || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            placeholder={t('全部')}
            clearable
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            onChange={(val) => {
              store.changeParkType({
                type: 'receiptParkType',
                parkType: val,
              });
            }}
          />
          <Select
            label={t('收货子仓')}
            name="receiptSubWarehouseId"
            data={limit.receiptParkType !== '' ? receiptSubWarehouseList : preSubWarehouseList}
            required
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            renderUnmatched={(r) => r.dictCode || <span style={{ color: '#bbb' }}>{t('全部')}</span>}
            placeholder={t('全部')}
            clearable
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
          />
          <DateRangePicker
            label={t('创建时间')}
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['startTime', 'endTime']}
            defaultTime={['00:00:00', '23:59:59']}
            span={2}
            rules={[rules.timeRange(limit)]}
          />
        </SearchAreaContainer>
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number.isRequired,
  limit: PropTypes.shape(),
  preSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  receiptParkTypeList: PropTypes.arrayOf(PropTypes.shape()),
  receiptSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  handleLinkList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
