# index

## 进度
需要解决上下文丢失的问题，因为是生成代码太长了

受制于gpt的token长度限制，目前不能生成太长的代码，

“请接上文继续”明显不够好使

## 目标

生成一个标准的wms列表页面，包括搜索、导出、列表功能，页面名称：差异中心。

搜索条件包括：

## 知识

wms列表页面的基本文件结构
- me.json // 页面的配置文件
- service.js // 页面的接口文件
- styles.less // 页面的样式文件
- reducer.js // 页面的状态管理文件
- view.jsx // 页面的入口文件
- jsx/header.jsx // 页面的头部文件
- jsx/handle.jsx // 页面的操作文件
- jsx/list.jsx // 页面的列表文件

以差异中心的为例，页面的基本结构如下：

给你一个差异中心的header.jsx文件，请你解释下代码功能

用“继续”来生成完整答案



这是差异中心的reducers.js文件，它并不是标准的react reducer，而是一个自定义的reducer，请你解释下代码功能




