# index

## 步骤

1. 步骤1：准备输入数据
创建一个JSON格式的数据结构，将用户的自然语言描述转换为JSON。

2. 步骤2：创建一个ChatGPT API调用
创建一个API调用，将结构化的输入数据传给ChatGPT。您需要将数据转换为自然语言描述，并且附上上述文件结构，以便ChatGPT能够根据输入信息来生成相关的前端代码。

3. 步骤3：处理ChatGPT生成的代码
ChatGPT将返回与您的输入描述匹配的代码。您需要处理这些代码，并将其添加到您的项目文件中。例如，在生成代码时，将相关的部分插入到对应的文件（me.json, service.js, styles.less, reducer.js, view.jsx, jsx/header.jsx, jsx/handle.jsx, jsx/list.jsx）。

4. 步骤4：整合和测试
将生成的代码整合到您的项目中，确保一切正常运行。如果有任何问题，请检查生成的代码并根据需要进行调整。在整个过程中，您可能需要对ChatGPT生成的代码进行微调，以使其符合您的项目要求。

## 想法
很大程度上是依靠模板

自然描述->模板->代码

有一点：
很多时候，要的是功能

像是操作记录

还有一点就是，这个demo要做到什么地步

4.30 今天的目标：
将页面全部改为模板，看看能不能早上完成，下午就可以摸鱼了

5.1 今天的目标：
1. 将模板部份完成
  - header ✅
  - list ✅
  - reducers ✅
  - handle
  - view
  - service
  - styles
2. 调教prompt
3. 生成到指定位置
4. 前端部份
  - 通信

另外几个模板不改了，原样输出就行了，反正演示中不需要展示