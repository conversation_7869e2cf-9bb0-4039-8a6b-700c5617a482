{"page_name": "差异中心", "features": ["搜索", "导出", "列表"], "search_conditions": [{"type": "Input", "label": "t('物资交接单号')", "name": "handoverCode", "placeholder": "t('请输入')", "clearable": true}, {"type": "select", "label": "t('处理节点')", "name": "handleLinkList", "data": "{handleLinkList}", "keygen": "dictCode", "format": "dictCode", "renderItem": "dictNameZh", "renderUnmatched": "{(r) => r.dictCode || <span style={{ color: '#bbb' }}>{t('全部')}</span>}", "placeholder": "{t('全部')}", "clearable": true, "onFilter": "{(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}"}, {"label": "收货园区", "type": "select"}, {"label": "收货子仓", "type": "select"}, {"label": "创建时间", "type": "date_range"}], "list_columns": ["差异单号", "物资交接单号", "处理节点", "发货园区", "发货子仓", "调度任务号", "质控确认人", "操作记录"]}