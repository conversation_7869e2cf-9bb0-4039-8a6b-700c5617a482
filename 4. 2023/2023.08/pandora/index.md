# 潘多拉计划

## 思路

想要实现的

1. 自行创建模板
2. 自行写header
3. 自行写list
4. 自行写reducers.js(模块式加载)

langchain

## 背景

页面仔，你写了这么多页面，写页面的效率怎么没变？是鼠标点得不够快，还是键盘敲得不够响？

人重复写再多页面，效率也就那样，不会有太多提升，但ai可以。

换ai来，取代人。

## 前言

去完aidc进修之后，我意识到我们wms 这边的工作更具挑战性，因为wms的代码更脏，说白了就是私有架构和私有方法用得多，跟业内最佳实现走得有点远，ai理解起来困难。例如你就很难解释reducers.js。

如果是vue的compusition api, react的hooks，对ai来说能好理解，因为是业内最佳实现，这方面的代码读得多。按惯性思路，应该是把wms的代码进行优化，抽象，更好的与ai结合。

但我就不。

因为wms是个老系统，要求的是稳。而且，优化抽象需要额外的工作量，而且不会提升效益，还会增加风险。从个人的角度来看，脏才好，对ai来说更具有挑战性。优雅的规范代码ai写得多，写脏代码才考验ai，能忍住写脏代码才更像人，也才能更好的取代人。

所以，我决定不优化，不抽象，不规范，不改变现有的代码，而是在现有的代码上进行改造，让ai能够更好的理解现有的代码，更好的与现有的代码结合。

## 实现

输入 -> 处理 -> 输出

输出的是代码，输入的是需求，处理的是逻辑

其中“输入”集中在：prd和soapi
“输出”集中在：文件处理和gitlab

新建页面的需求通常有几种：
1. 查询
2. 配置
3. 流程操作

其中，查询和配置的相对简单，所以先从这里入手。

最终目的当然是让ai自行完成整个需求，但是这个过程是渐进的，所以先一步一步来。

页面页面的核心主要是header, list, reducers.js

问题：怎么将prd 拆解成prompt?
问题：怎么将soapi 拆解成prompt?

list 文件该怎么组合？




