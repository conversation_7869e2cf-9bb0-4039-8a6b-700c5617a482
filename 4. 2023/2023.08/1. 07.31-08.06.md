# 8 月份 第 1 周
## 本周 todo


## 2023.07.31 周一
### life

### work
todo
- 动态布局
  - 复制/添加ABT分支，参数没有带过去 ✅
  - 详情 基本信息空白 ✅
  - 新建配置 下一步 报错
- 接入ai分类


## 2023.08.01 周二
### life

### work
todo
- 接入ai分类
  - 后端协调联调事宜
- 动态布局 bug
  - 新建配置 下一步 报错
  - 全面借用广告位配置先，确认是否有问题 ❌ 即使是用PersonalCenterCondition 还是同样问题
  - 逐步构建 ✅

1. 问题列表 接入AI自动分类处理	P1 【3h】(✅ 原本就有)
3. 反馈问题列表筛选优化	P1 【1.5d】
4. 易用性问题详情页增加 删除易用性问题功能	P2 【3h】
5. 易用性问题换绑问题	P2 【6h】
7. 交互优化 P1 【1.5d】
  1. 问题模块配置 - 模块名称支持编辑，修改后只影响epp内的模块展示名称(✅)
  3. 反馈列表处理按钮优化 ，跟进露出，其他hover展开 ✅
  4. 批量处理按钮移动至翻页器左下 ✅
  5. BUG：自适应要给译文和原文 ✅
  6. 问题列表页，当选中待处理、不处理、已退回状态时，列表中去掉“处理状态“字段 ✅
  7. 列表内增加 AI 分类标识，区分关键词分类标识；运行AI分类的条目使用新icon，运行自动分类的保持原icon

13:30 解决了，妈的，是依赖的问题

## 2023.08.02 周三
### life

### work
todo
- 解决构建问题 ✅


## 2023.08.03 周四
### life

### work
todo
- 盘点重构 架构改造
  - eslint ✅
  - type ✅
  - useHook ✅


## 2023.08.04 周五
### life

### work
todo
- 盘点重构 架构改造
  - 旧页面跳转 ✅
  - state 拆分 
  - 


## 2023.08.05 周六
## 2023.08.06 周日
### life

正式搁置「明日方舟」小号，因为没啥意义，还不如玩副游
