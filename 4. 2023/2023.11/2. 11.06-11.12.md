# 11 月份 第 2 周
## 本周 todo
- bbl
  - 词条工具
  - 词条管理
- 本周日常需求
  - 大件稽查打印（跟后端确认方案
  - 排班 页面表格优化
- 需求：仓库切换为权限仓库
  - node版本升级 
  - me.json 注入
  - 切换仓库下拉数据

## 2023.11.06 周一
### life

### work
todo
- 先让我本地跑起来
- OFC-129139 【产品需求】WMS-L所有页面仓库选择范围优化

## 2023.11.07 周二
### life

### work
todo
今天的核心就是把wms-la 本地跑起来

- ioa 与gitlab ✅
  - 解决了，应该是我之前的项目都是ssh连接，又没有配置key 的缘故，不管了。改成https 好了、
- wms-la
  - 14.19.3 版本
- SwitchyOmega 的la配置


## 2023.11.08 周三
### life
- 中午跟朋友吃饭 ✅

### work
todo
- 需求：仓库切换为权限仓库
  - node版本升级 
  - me.json 注入
  - 切换仓库下拉数据


## 2023.11.09 周四
### life

### work
todo
- 需求：仓库切换为权限仓库
  - me.json ✅
  - 文档提取 ✅
  - 开发 ✅
    - base 36
    - inbound 7
    - outbound 29

这周的需求完成得差不多了，得想想摸鱼做什么了

## 2023.11.10 周五
### life
摸鱼日，整点花活

如何利用chatgpt的api 来整活

### work
- 读读文档，了解下api，抓紧这些摸鱼时间
- 如果觉得完成一个需求这一点太大，那么就想办法拆分工作，先将工作拆分成能让ai完成的

双头并行，避免

- 重点：接收soapi文档，化为代码

保证自己有足够的事情可以切换状态
- 了解 github next ✅
- 读api 文档，那么读一下文档好了
- 读文章 ✅
- 了解autoGPT
- 整理下本地的快链 Raycast 快捷链接怎么快速编辑
- playground 
- 发现账号没钱，不能玩了，哭了，想整一个新账号，用来免费耍耍

## 2023.11.11 周六
## 2023.11.12 周日
### life
