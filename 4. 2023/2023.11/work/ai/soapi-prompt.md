I am currently developing a frontend application using React and TypeScript. I need to create a function to handle the following API request:

API Details:
[api]

This function should send a POST request and process the returned data. I need it to be able to handle request parameters and response data, and to be able to handle possible errors. Please help me write the implementation code for this function, including the necessary interface definitions, based on the provided API details.

The expected output code format should be:

```typescript
interface GetXXAPIReq {
  // ...
}

type GetXXAPIRes = {
  // ...
};

/**
 * @description ...
 */
export const getXXAPI = (data: GetXXAPIReq): Promise<APIBaseResponse<GetXXAPIRes>> => {
  const url = '...';
  return sendPostRequest({ url, data });
};
```

Please note, APIBaseResponse is a generic response type that can be referenced directly, and its definition is as follows:

```typescript
export interface APIBaseResponse<T> {
  code: number;
  msg: string;
  info: T;
  bbl?: {
    info: {
      [key: string]: {
        key: string;
        tag: string;
        value: string;
      };
    };
  };
}
```

interface 和type 中的字段必须有注释说明. Fields with underscores should be converted to camel case. Only the 'info' part needs to be declared in the 'Res' type, as the 'code', 'msg', and 'bbl' parts are already declared in APIBaseResponse.

The code should adhere to modern React and TypeScript best practices. The code should be output directly without explanations or interpretations.