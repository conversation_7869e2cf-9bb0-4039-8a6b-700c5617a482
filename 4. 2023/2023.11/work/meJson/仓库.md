const { currentWarehouseList: warehouseList } = yield 'nav';

- base/src/component/basic/goods-receipt-restriction-rules ✅
  32,23:             label={t('仓库')}

- base/src/component/basic/inbound-configuration/subwarehouse-params-management ✅
  16,25:       warehouseList, // 仓库列表
  40,25:             label={t('所属仓库')}
  77,59:   warehouseList: PropTypes.arrayOf(PropTypes.shape()), // 仓库列表

- base/src/component/basic/inbound-configuration/weight-discrepancy-allocation ✅
  36,23:             label={t('仓库')}

- base/src/component/basic/outbound-config/channel-management ✅
  40,23:             label={t('仓库')}
  51,18:               // 仓库与渠道名称联动

- base/src/component/basic/outbound-wave-group-rule ✅
  60,23:             label={t('仓库')}

- base/src/component/basic/putaway-rules-config ✅
  37,30:           alwaysVisible={[t('仓库'), t('园区')]} // 需要校验的字段，需在这里配置且需要加required
  40,23:             label={t('仓库')}

- base/src/component/basic/special-stock-occupy-config ✅
  43,23:             label={t('仓库')}

- base/src/component/basic/stock-occupy-config
  37,23:             label={t('仓库')}

- base/src/component/basic/stock-occupy-config-new
  37,23:             label={t('仓库')}

- base/src/component/basic/stock-out-config/wechat-push-config
  146,39:               Modal.error({ title: t('仓库不能为空') });

- base/src/component/basic/supplier-sub-warehouse/commodity-type
  188,23:             label={t('仓库名称')}
  240,59:   warehouseList: PropTypes.arrayOf(PropTypes.shape()), // 仓库列表

- base/src/component/basic/supplier-sub-warehouse/pocket
  19,35:       pocketWarehouseTypeList, // 仓库类型列表
  20,31:       pocketWarehouseList, // 仓库列表
  226,103:                 renderUnmatched={(r) => r.warehouseTypeName || <span style={{ color: '#bbb' }}>{t('请选择仓库类型')}</span>}
  251,99:                 renderUnmatched={(r) => r.warehouseName || <span style={{ color: '#bbb' }}>{t('请选择仓库')}</span>}
  309,69:   pocketWarehouseTypeList: PropTypes.arrayOf(PropTypes.shape()), // 仓库类型列表
  310,65:   pocketWarehouseList: PropTypes.arrayOf(PropTypes.shape()), // 仓库列表

- base/src/component/basic/user-bind/list
  50,16:     // 以前的逻辑：修改仓库时要调获取用户接口

- base/src/component/board/board-common
  29,35:           <Form.Item label={`${t('仓库')}:`}>

- base/src/component/board/inventory-board
  39,30:           alwaysVisible={[t('仓库'), t('片区'), t('园区'), t('子仓'), t('库区'), t('库区类型'), t('日期')]} // 需要校验的字段，需在这里配置且需要加required
  42,23:             label={t('仓库')}

- base/src/component/board/stock-scroll-screen
  21,19:               {t('仓库')}

- base/src/component/domestic-salary/balance-and-rules/basic-salary
  75,23:             label={t('仓库')}

- base/src/component/domestic-salary/balance-and-rules/performance-formula
  37,23:             label={t('仓库')}

- base/src/component/examples/something
  44,53:               <span className={styles.labWidth}>{t('仓库:')}</span>

- base/src/component/in-warehouse/handover-disass
  52,25:             label={t('收货仓库')}

- base/src/component/in-warehouse/inventory-query/list
  47,21:                 {t('仓库')}

- base/src/component/in-warehouse/receipt-query/list
  64,23:             label={t('仓库')}
  85,41:                       nameZh: t('请在右上角选择仓库'),

- base/src/component/owc-manage/bill-management/bill-import-details
  62,25:       warehouseList, // 仓库列表
  175,23:             label={t('仓库')}

- base/src/component/owc-manage/bill-management/expense-management
  114,23:             label={t('仓库')}

- base/src/component/owc-manage/bill-management/statement-account
  143,23:             label={t('仓库')}

- base/src/component/owc-manage/bill-management/statement-details
  81,25:       warehouseList, // 仓库列表
  128,23:             label={t('仓库')}

- base/src/component/owc-manage/bill-management/system-estimate-details
  71,25:       warehouseList, // 仓库列表
  154,23:             label={t('仓库')}

- base/src/component/owc-manage/certificate-bills-manage/sub-certificate-query
  123,23:             label={t('仓库')}

- base/src/component/owc-manage/cost-rule-manage/add-basic-message
  116,43:             <Form.Item required label={t('仓库')}>
  118,27:                 label={t('仓库')}
  129,46:                 rules={[rules.required(t('请选择仓库'))]}

- base/src/component/owc-manage/cost-rule-manage/servicer-cost-manage
  55,23:             label={t('仓库')}

- base/src/component/owc-manage/expense-account-manage/pay-order
  57,25:       warehouseList, // 仓库列表
  121,23:             label={t('仓库')}

- base/src/component/owc-manage/expense-account-manage/pay-order-detail
  69,25:       warehouseList, // 仓库列表
  128,23:             label={t('仓库')}

- base/src/component/owc-manage/service-supplier-manage/payment-subject-manage
  18,29:       warehouseIdList, // 服务仓库
  54,25:             label={t('服务仓库')}

- base/src/component/sysconfig/outbound/material-configuration/view
  135,23:             label={t('仓库')}

- micro-inbound/src/component/inbound/other-storage-manage/muti-stock-diff
  76,59:   warehouseList: PropTypes.arrayOf(PropTypes.shape()), // 仓库下拉列表

- micro-inbound/src/component/inbound/other-storage-manage/non-genuine-boxing
  134,59:   warehouseList: PropTypes.arrayOf(PropTypes.shape()), // 仓库下拉列表

- micro-inbound/src/component/inbound/other-storage-manage/non-genuine-config
  34,23:             label={t('仓库')}
  69,59:   warehouseList: PropTypes.arrayOf(PropTypes.shape()), // 仓库下拉列表

- micro-inbound/src/component/inbound/other-storage-manage/storage-rule-config
  35,23:             label={t('仓库')}

- micro-inbound/src/component/inbound/reject-order/return-package-scan
  12,17:           {t('当前仓库{}', '：')}

- micro-inbound/src/component/inbound/transfer-operation-manage/return-package
  38,36:                   title: t('请在右上角选择仓库'),

- micro-inbound/src/component/qms/defective/refund-box-scan-new
  43,36:                   title: t('请在右上角选择仓库'),

- micro-outbound/src/component/combination/exp-on-shelf/list
  69,32:           alwaysVisible={[t('拣货仓库'), t('合包地址')]}
  72,25:             label={t('拣货仓库')}

- micro-outbound/src/component/combination/integrated-inventory-management
  48,32:           alwaysVisible={[t('拣货仓库'), t('合包地址')]}
  67,25:             label={t('拣货仓库')}

- micro-outbound/src/component/combination/parcel-package/list
  234,25:             label={t('合包仓库存占用类型')}

- micro-outbound/src/component/combination/subpackage-onshelf-manager/list
  103,25:             label={t('合包仓库存占用类型')}

- micro-outbound/src/component/combination/transshipment/list
  59,40:           callback(new Error(t('合包地址和拣货仓库从中选一个')));
  102,32:           alwaysVisible={[t('拣货仓库'), t('合包地址')]}
  110,25:             label={t('拣货仓库')}
  264,25:             label={t('合包仓库存占用类型')}

- micro-outbound/src/component/combination/transshipment/view
  30,23:             label={t('仓库')}

- micro-outbound/src/component/combination/wellen-of-box-collect-detail/list
  126,26:             label={t('未齐批仓库')}

- micro-outbound/src/component/exception/package/list
  59,38:         callback(new Error(t('选择了出库作业仓库则必选出库作业子仓')));
  338,27:             label={t('出库作业仓库')}

- micro-outbound/src/component/outbound/destination-port-management
  33,30:           alwaysVisible={[t('仓库'), t('目的港')]} // 需要校验的字段，需在这里配置且需要加required
  37,23:             label={t('仓库')}

- micro-outbound/src/component/outbound/gathers/gather-data/list
  35,38:         callback(new Error(t('选择了出库作业仓库则必选出库作业子仓！')));
  275,27:             label={t('出库作业仓库')}

- micro-outbound/src/component/outbound/gathers/gather-master/list
  43,38:         callback(new Error(t('选择了出库作业仓库则必选出库作业子仓！')));
  141,27:             label={t('出库作业仓库')}

- micro-outbound/src/component/outbound/gathers/gather-master/view
  27,23:             label={t('仓库')}

- micro-outbound/src/component/outbound/gathers/gather-sub/list
  35,38:         callback(new Error(t('选择了出库作业仓库则必选出库作业子仓！')));
  155,27:             label={t('出库作业仓库')}

- micro-outbound/src/component/outbound/gathers/gather-sub/view
  26,23:             label={t('仓库')}

- micro-outbound/src/component/outbound/intelligent-delivery/config
  42,23:             label={t('仓库')}

- micro-outbound/src/component/outbound/package/view
  43,21:           label={t('仓库')}

- micro-outbound/src/component/outbound/transfer/first/view
  26,23:             label={t('仓库')}

- micro-outbound/src/component/outbound/transfer/second/view
  26,23:             label={t('仓库')}

- micro-outbound/src/component/outbound-management/localized-deployment-cut
  27,41:           <Form.Item required label={t('仓库')} labelWidth={40}>

- micro-outbound/src/component/outbound-management/package-plan
  54,19:               {t('仓库')}

- micro-outbound/src/component/outbound-management/special-outbound-query
  96,23:                 {t('出库仓库')}
  103,23:                 {t('入库仓库')}
  175,21:                 {t('仓库')}

- micro-outbound/src/component/outbound-management/wave-query
  69,19:               {t('仓库')}

- micro-outbound/src/component/oversea/distribution/oversea-first-query
  65,23:             label={t('仓库')}

- micro-outbound/src/component/oversea/distribution/oversea-second-query
  65,23:             label={t('仓库')}

- micro-outbound/src/component/special-out/box-detail-query/list
  132,26:             label={t('第三方仓库产品条码')}

- micro-outbound/src/component/special-out/box-scan/list
  607,23:       {/* 当前用户未绑定/绑定多个仓库时弹窗选择 */}

- micro-outbound/src/component/special-out/code-region-config
  38,23:             label={t('仓库')}

- micro-outbound/src/component/special-out/order-manage/detail-list
  106,31:           <Input label={t('第三方仓库产品条码')} name="fnSku" placeholder={t('请输入')} delay={0} />
  109,23:             label={t('仓库')}

- micro-outbound/src/component/special-out/outbound-order-config
  40,25:             label={t('出发仓库')}
  54,25:             label={t('到达仓库')}