# 3 月份 第 3 周
## 本周 todo


## 2023.03.13 周一
### life

### work
todo
- OFC-81635【工单管理】3期 测试中
- OFC-83336【物资管理项目】【wms】差异中心
  - 开发 前提工作
  - 调研：table 中的table ✅
- 研究ai
  - 思考，记录

11:00 ai 这事儿太有乐子了，ai 玩够了，得稍稍深入点了解下

盘点 XHPDSS22120900000013

BG2303130002952

疯狂忙活其他事儿，耽误了开发进度

## 2023.03.14 周二
### life

### work
todo
- OFC-81635【工单管理】3期 测试中
- OFC-83336【物资管理项目】【wms】差异中心
  - 开发
  

库内评审：
- OFC-81257 盘点结果，展示盘盈亏原因  
  - 【mot】【盘点】盘点结果 逻辑变更 2.5d
  - 【wms】【盘点结果】新增盘点结果(有联动) 4h
- OFC-77984 【WMS盘点】- 盘盈时超品项上限，支持配置是否阻断
  - 【mot】【盘点】品项上限 逻辑变更 1d
- OFC-81218 理货下架任务装箱品项支持一箱多码，支持最小维度配置为库区，按配置的装箱品项上限可装箱品项
  - 配合新增字段 1h
- OFC-82227 【佛山-返仓装箱】 允许输入效期
  - 【mot】新返仓装箱 新增效期 逻辑 2d
  - 【wms】移位单明细 加字段


## 2023.03.15 周三
### life

### work
todo 
- OFC-83336【物资管理项目】【wms】差异中心
  - 开发 （完成了80%，剩下联调）
  


## 2023.03.16 周四
### life

### work
todo
- OFC-84103【产品需求】盘点结果，展示盘盈亏原因
  - mot 盘点结果
    - 初步改动
    - 接入场景
  - wms ✅

- 盘点结果api:https://soapi.sheincorp.cn/application/2916/routes/post_wws_front_check_result_list
- 盘点结果导出api:https://soapi.sheincorp.cn/application/2916/routes/post_wws_front_check_result_export
- 盘点确认（新）：https://soapi.sheincorp.cn/application/2916/routes/post_wws_front_check_inventory_check_confirm_new



这个盘点结果的改动变动可以很大，相当于定向爆破，涉及到的state

- isTwiceOrPick: false, // 是否二次盘点或短拣盘点；用于实现OFC-18735需求的盘点结果页面【且是佛山仓，扫库位时会判断重置】
  - 不能改 ❌ isTwiceOrPick 不止是二次/短拣，更是盘点结果页
- checkResultExceptionSelect: [], // 短拣-盘点结果下拉数据
  - 肯定保留 ✅
- checkResultTwiceSelect: [], // 二次-盘点结果下拉数据
  - 肯定保留 ✅
- checkResultCode: null, // 二次盘点或短拣盘点，盘点结果下拉值，传接口
  - 存疑 🤨
  - 去除，因为要换新形式
- checkResultCodeText: '', // 二次盘点或短拣盘点，盘点结果下拉文案，界面显示
  - 已去除 ✅
- showPicker: false, // 盘点结果弹层选择
  - 改造
- showModalPicker: false, // 弹窗-盘点结果弹层选择
  - 存疑 🤨
  - 去除，因为要换新形式
- showEmptyDiff: false, // 二次盘点和短拣盘点，空库位也要进入盘点结果页面进行盘点结果选择
  - 保留 ✅

ECPDSS23031400000002
BH1002
1000297987

ECPDSS23031400000006
BH2001

XHPDSS23031600000005
LYQ-0101-11	
S343395-27

## 2023.03.17 周五
### life

### work
#### 故障
OFC-80833 前端本地存储优化 吃故障了，好好想想这次的原因，2周背2个，直接超额了上半年的线上故障数。但凡按照规范来，都不会出事，至少出事了也轻一点。

原因点有两个：
1. 代码冲突处理出错了(自己本地合master，处理冲突太多了：原因：没严格按照规范进行)
2. 验证时差时不知道这个点，导致验证漏了（验收标准中需求改动点遗漏，导致验收不到位）

问题描述：包裹异常页面选择印第安纳发货仓，默认时差-12，时差不生效
应用环境：wms生产环境
直接原因：前端需求storage优化，解决冲突有误导致
根本原因：没按照规范执行，本地需求分支合并master处理冲突，代码遗漏导致问题出现
处理方式：修复后发版
解决时长：
报障单： SFD-7345 - 2023.03.17-海外仓 业务-WMS-出库管理-1004 值班业务处理中
责任人：郑智彬


#### todo
今天的任务其实提前完成了
- OFC-84103【产品需求】盘点结果，展示盘盈亏原因
  - mot
  - wms
- OFC-83336【物资管理项目】【wms】差异中心
  - wms



## 2023.03.18 周六
## 2023.03.19 周日
### life
