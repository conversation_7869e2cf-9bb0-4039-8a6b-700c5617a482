# 前端本地存储改造

- 统计各系统（wms、mot、wsk）的
- 根据统计的样本

写脚本批量替换，人为检查

https://github.com/facebook/jscodeshift

写个jscodeshift脚本 来打印出项目中所有的sessionStorage和localStorage键值

## 总进度
- wms试水 ✅
- npm 包（4/7前完成）

## wms
进度：
- 一期改造：nav 部分试水 ✅
- 二期改造：时区优化

## mot
进度：
- 一期改造：login/list 部分试水

## mot-pl
进度：
等mot 试水完成后

## wms-pl
- wms试水 ✅

## wsk
有，但不多

## wcs

## 

## gwms
- 已经改造完成

## gmot
- 已经改造完成

## prompt

你好，需要你帮忙写个jscodeshift脚本来改写文件

给sessionStorage和localStorage 换个写法

有个Map，key是sessionStorage和localStorage的键值，value是对应的新函数名字，例如：

const storageMap = new Map([
  ['warehouseId', 'warehouseIdStorage'],
  ['lang', 'langStorage'],
]);

根据这个map，找到符合条件的storage的key，然后替换成新的函数名字

替换方式：localStorage.getItem('warehouseId') 会变成 warehouseIdStorage.getItem()
并且在文件头部加入对应引用：import { warehouseIdStorage } from 'src/lib/storage-new';

新函数的用法：XXXStorage.setItem('zh');XXXStorage.getItem();XXXStorage.removeItem();






