# ai

- “毁灭你，与你何干”
- 人类，一败涂地

ai对人类工作的降维打击

解构工作，将工作中的场景细化，让ai去辅助、甚至主导一部分工作

## ai 小原理

- 什么是上下文？
- 什么是语料？
- ai的逻辑是怎么构成的，为什么能理解人类说话？


- 什么自然语言处理(NLP)

NLP是自然语言处理（Natural Language Processing）的缩写，是指计算机科学与人工智能领域中，处理和理解自然语言的技术和方法。NLP旨在实现计算机对人类语言的理解和生成，使计算机可以像人类一样“读懂”自然语言，进而实现与人类的交互、沟通和协作。

NLP是一门涵盖多个子领域的学科，包括语言模型、句法分析、语义分析、信息抽取、机器翻译、对话系统等。NLP的应用范围广泛，例如智能客服、机器翻译、智能搜索、自然语言生成、舆情分析、人机对话等。

## ai 试水

—— 为什么用ai而不是AI？
—— 因为感觉它还有成长空间，假以时日，它能成长为Ai，再长大成AI。

### 课题1 wms开发·从prd到页面

所需材料：
对应路由名字
1. 合适的模板

### 打通各端应用
- gitlab 
- jira: OFC-xxx 快速跳转需求号
- 企业微信的excel 

### 前端本地存储管理方案

能帮忙设计一个前端本地存储管理方案吗
- 以 工厂函数的思路来设计，像是const nameStorage = createStorage('name'); nameStorage.setItem('Tom');nameStorage.getItem(); 的方式
- createStorage的第一个参数是key，
- createStorage的第二个参数是type，type有两种，一种是sessionStorage，一种是localStorage,默认是localStorage
- createStorage的第三个参数是expire，expire是过期时间，单位是ms，默认为0，表示永不过期
- 过期时，getItem 依旧会返回过期值，但会标识出这个值已过期
- 有个isExpired方法，用来判断值是否过期
- try-catch语句，用来捕获JSON.parse解析数据的异常
- setItem时，如果传入的是对象，会自动转换成JSON字符串
- getItem时，如果是JSON字符串，会自动转换成对象

