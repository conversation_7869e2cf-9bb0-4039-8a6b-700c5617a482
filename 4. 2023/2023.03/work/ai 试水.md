# ai 试水

—— 为什么用ai而不是AI？
—— 因为感觉它还有成长空间，假以时日，它能成长为Ai，再长大成AI。

## 课题1 wms开发·从prd到页面

所需材料：
对应路由名字
1. 合适的模板

## 前端本地存储管理方案

能帮忙设计一个前端本地存储管理方案吗
- 以 工厂函数的思路来设计，像是const nameStorage = createStorage('name'); nameStorage.setItem('Tom');nameStorage.getItem(); 的方式
- createStorage的第一个参数是key，
- createStorage的第二个参数是type，type有两种，一种是sessionStorage，一种是localStorage,默认是localStorage
- createStorage的第三个参数是expire，expire是过期时间，单位是ms，默认为0，表示永不过期
- 过期时，getItem 依旧会返回过期值，但会标识出这个值已过期
- 有个isExpired方法，用来判断值是否过期
- try-catch语句，用来捕获JSON.parse解析数据的异常
- setItem时，如果传入的是对象，会自动转换成JSON字符串
- getItem时，如果是JSON字符串，会自动转换成对象
- 