#  月份 第 4 周
## 本周 todo
本周核心待办：打印


## 2023.03.22 周一
### life
请假，在家


## 2023.03.23 周二
### life

### work
todo
- OFC-94816 【前端需求】出库打包业务-本地打印【开发&自测】
- 库内需求评审

15:10 全力开发打印需求

- 恢复 包裹明细 调试现场
- 调试 包裹明细 打印
- 上次的404打印标签网络问题

调试小妙招：直接输入网址，这样都不用构建测试环境了

转向问题：css中的tranform不是打印样式，浏览器会直接忽略

所以现在的路线：生成base64图片，转向后嵌入到html中，上传html，然后打印

开搞，争取今天能在打印机上打印出来

图片的路线挺严重的，因为要考虑打印包裹明细的分页问题

20:00 打印机终于可以了！

## 2023.03.24 周三
### life

### work
todo
- OFC-94816 【前端需求】出库打包业务-本地打印【开发&自测】

测试点：
1. 短明细：打印样式与pdf-service一致
2. 长明细：分页测试

window.EPH.print(...['', 'label'])
window.EPH.print(...['', 'label'])

收集样品：
- pdf 短明细 

window.EPH.print(...['https://pdf-service-ha.dotfashion.cn/pdf-proxy/pdf2/wms-test01.dotfashion.cn/2023/5/24/1684899101726-95bcb66b33e6d077ea50a778d1334ac4.oss-ha.pdf', 'label']) 

- pdf 长明细 

window.EPH.print(...['https://pdf-service-ha.dotfashion.cn/pdf-proxy/pdf2/wms-test01.dotfashion.cn/2023/5/24/1684899110405-669598cf67ebdc33d0a8d8ebc72e3ac8.oss-ha.pdf', 'label']) 

- html 短明细 

window.EPH.print(...['https://files-dev.dotfashion.cn/wms/2023/05/24/16848992062703221523.html', 'label']) 

- html 长明细 

window.EPH.print(...['https://files-dev.dotfashion.cn/wms/2023/05/24/16848992221330071284.html', 'label']) 

先调好短的，顺便试试长的

最后弄了80宽度的布局，不折腾旋转了，累了。

## 2023.03.25 周四
### life

### work
- OFC-95691 【前端需求】【mot】移位上架错误提示优化【开发】
  - mot 开发
  - mot-pl 开发
- OFC-94816 【前端需求】出库打包业务-本地打印【开发&自测】

成了成了，等文辉那边升级完sheink，就继续对接

## 2023.03.26 周五
### life

### work
todo
- OFC-94794 【SFS发票项目】【前端】【wms】库存侧改造-国内


## 2023.03.27 周六
### life
感觉重感冒，又感觉像复阳。晚上回去测一测


### work
todo
- OFC-94794 【SFS发票项目】【前端】【wms】库存侧改造-国内
  - 准备好调试 ✅
  - 改成注入 

现在的精神状态很一般啊，因为今天没正事，而且病了，所以没劲干活啊，连摸鱼都没有方向，太坑了。不得劲。
  

## 2023.03.28 周日
### life
