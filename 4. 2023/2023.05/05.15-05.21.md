# 5 月份 第 3 周
## 本周 todo
超级值班周过去了，得休养生息

本周长线任务：图片打印

## 2023.05.15 周一
### life

### work
todo
- 周一灰度（✅ 无
- 任务安排（✅
- 文件替换脚本（上午完成 搁置
- OFC-94753	【WMS-仓库管理】"仓库名称，时差"搜索条件优化 ✅
- OFC-94816	【前端需求】出库打包业务-图片打印【开发&自测】
ß
## 2023.05.16 周二
ß### life

### work
todo
- OFC-94816	【前端需求】出库打包业务-图片打印【开发&自测】
  - html2canvas ✅
  - 打印机测试 ✅ -> sheink不能图片打印，试试zpl
  - zpl 嵌入图片的精度太差，不可用
- 周二灰度 ✅
- replaceFile 脚本修正 ✅
- OFC-94065	【海外时区切换项目】【WMS】时区切换2期-库内看板调整【开发】
- OFC-94072	【海外时区切换项目】【WMS-PL】时区切换2期-库内看板调整【开发】
  - 库位状态看板
  - 补货明细进度监控
  - 积压监控看板
  

## 2023.05.17 周三
### life

### work
todo
- 时区看板 ✅
  - OFC-94065	【海外时区切换项目】【WMS】时区切换2期-库内看板调整【开发】
  - OFC-94072	【海外时区切换项目】【WMS-PL】时区切换2期-库内看板调整【开发】
- 脚本优化 ✅
- 本地打印
  - jsPDF 尝试（试了下，还可以
  - sheink 改造


## 2023.05.18 周四
### life

### work
todo
- 本地打印
  - 准备点文件地址
    - 图片：包裹标签 window.EPH.print(...['https://files-dev.dotfashion.cn/wms/2023/05/18/包裹标签.png', 'A4'])
    - 图片：包裹明细 window.EPH.print(...['https://files-dev.dotfashion.cn/wms/2023/05/18/包裹明细.png', 'label'])
    - html：包裹标签 
    window.EPH.print(...['https://files-dev.dotfashion.cn/wms/2023/05/18/16843968221050702814.html', 'label'])
    - html：包裹明细
    window.EPH.print(...['https://files-dev.dotfashion.cn/wms/2023/05/18/16843968673495803521.html', 'A4'])
  - 确定为html路线，开始统计工作

todo
- 统计
  - packageTpl EPHPrint('label', printUrl);
  - packagePrintTpl EPHPrint('A4', url);
  - EPHPrint('label', returnInsPrintTemplateUrl); // 
- 写公共方法

  

## 2023.05.19 周五
### life

### work
todo
- 本地打印
  - 写公共方法

今天要完成开发工作吧
- 公共方法开发
  - 文件上传 完成 ✅
  - html 转向 
- 精准出口扫描应用



sheink 功能改造
- 文件缓存
- 接收文件流





## 2023.05.20 周六
## 2023.05.21 周日
### life
周六团建，带了女票去团建，愉快的体验
