# Rust 程序设计语言

## 介绍

- 1-4
  - 第一章介绍如何安装 Rust
  - 第二章是 Rust 语言的实战介绍
  - 第三章，它介绍了 Rust 中类似其他编程语言中的功能
  - 第四章，学习 Rust 的所有权系统。
- 5-6
  - 第五章讨论结构体和方法
  - 第六章介绍枚举、match 表达式和 if let 控制流结构
- 7-9
  - 第七章你会学习 Rust 的模块系统和私有性规则来组织代码和公有应用程序接口（Application Programming Interface, API）。
  - 第八章讨论了一些标准库提供的常见集合数据结构，比如 可变长数组(vector)、字符串和哈希 map。
  - 第九章探索了 Rust 的错误处理哲学和技术。
- 10-12
  - 第十章深入介绍泛型、trait 和生命周期，他们提供了定义出适用于多种类型的代码的能力。
  - 第十一章全部关于测试，即使 Rust 有安全保证，也需要测试确保程序逻辑正确。
  - 第十二章，我们构建了属于自己的在文件中搜索文本的命令行工具 grep 的子集功能实现。为此会利用之前章节讨论的很多概念。
- 13-15
  - 第十三章探索了闭包和迭代器：Rust 中来自函数式编程语言的功能。
  - 第十四章会更深层次的理解 Cargo 并讨论向他人分享库的最佳实践。
  - 第十五章讨论标准库提供的智能指针以及启用这些功能的 trait。
- 16-17
  - 第十六章会学习不同的并发编程模型，并讨论 Rust 如何助你无畏的编写多线程程序。
  - 第十七章着眼于比较 Rust 风格与你可能熟悉的面向对象编程原则。
- 18-19
  - 第十八章是关于模式和模式匹配的参考章节，它是在Rust程序中表达思想的有效方式。
  - 第十九章是一个高级主题大杂烩，包括 unsafe Rust、宏和更多关于生命周期、 trait、类型、函数和闭包的内容。
- 20
  - 第二十章将会完成一个项目，我们会实现一个底层的、多线程的 web server！