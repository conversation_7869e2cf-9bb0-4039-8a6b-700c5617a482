# 基本类型

## 数值类型

### 整数类型
整数是没有小数部分的数字。i32 类型，表示有符号的 32 位整数（ i 是英文单词 integer 的首字母，与之相反的是 u，代表无符号 unsigned 类型）。

Rust 中的内置的整数类型：

长度	有符号类型	无符号类型
8 位	i8	u8
16 位	i16	u16
32 位	i32	u32
64 位	i64	u64
128 位	i128 u128
视架构而定	isize usize

无符号数表示数字只能取正数，而有符号则表示数字既可以取正数又可以取负数。

Rust 整型默认使用 i32

#### 整型溢出
假设有一个 u8 ，它可以存放从 0 到 255 的值。那么当你将其修改为范围之外的值，比如 256，则会发生整型溢出。


### 浮点类型
浮点类型数字 是带有小数点的数字，在 Rust 中浮点类型数字也有两种基本类型： f32 和 f64，分别为 32 位和 64 位大小。默认浮点类型是 f64。

#### 浮点数陷阱
浮点数由于底层格式的特殊性，导致了如果在使用浮点数时不够谨慎，就可能造成危险，有两个原因：
1. 浮点数往往是你想要数字的近似表达
2. 浮点数在某些特性上是反直觉的 

为了避免上面说的两个陷阱，你需要遵守以下准则：

1. 避免在浮点数上测试相等性
2. 当结果在数学上可能存在未定义时，需要格外的小心

```rust
fn main() {
    let abc: (f32, f32, f32) = (0.1, 0.2, 0.3);
    let xyz: (f64, f64, f64) = (0.1, 0.2, 0.3);

    println!("abc (f32)");
    println!("   0.1 + 0.2: {:x}", (abc.0 + abc.1).to_bits());
    println!("         0.3: {:x}", (abc.2).to_bits());
    println!();

    println!("xyz (f64)");
    println!("   0.1 + 0.2: {:x}", (xyz.0 + xyz.1).to_bits());
    println!("         0.3: {:x}", (xyz.2).to_bits());
    println!();

    assert!(abc.0 + abc.1 == abc.2);
    assert!(xyz.0 + xyz.1 == xyz.2);
}
```

#### NaN

对于数学上未定义的结果，例如对负数取平方根 -42.1.sqrt() ，会产生一个特殊的结果：Rust 的浮点数类型使用 NaN (not a number)来处理这些情况。

所有跟 NaN 交互的操作，都会返回一个 NaN，而且 NaN 不能用来比较，下面的代码会崩溃

出于防御性编程的考虑，可以使用 is_nan() 等方法，可以用来判断一个数值是否是 NaN

### 数字运算
Rust 支持所有数字类型的基本数学运算：加法、减法、乘法、除法和取模运算。

```rust
fn main() {
  // 编译器会进行自动推导，给予twenty i32的类型
  let twenty = 20;
  // 类型标注
  let twenty_one: i32 = 21;
  // 通过类型后缀的方式进行类型标注：22是i32类型
  let twenty_two = 22i32;

  // 只有同样类型，才能运算
  let addition = twenty + twenty_one + twenty_two;
  println!("{} + {} + {} = {}", twenty, twenty_one, twenty_two, addition);

  // 对于较长的数字，可以用_进行分割，提升可读性
  let one_million: i64 = 1_000_000;
  println!("{}", one_million.pow(2));

  // 定义一个f32数组，其中42.0会自动被推导为f32类型
  let forty_twos = [
    42.0,
    42f32,
    42.0_f32,
  ];

  // 打印数组中第一个值，并控制小数位为2位
  println!("{:.2}", forty_twos[0]);
}
```

### 位运算
Rust的运算基本上和其他语言一样

运算符	说明
`&` 位与	相同位置均为1时则为1，否则为0
`|` 位或	相同位置只要有1时则为1，否则为0
`^` 异或	相同位置不相同则为1，相同则为0
`!` 位非	把位中的0和1相互取反，即0置为1，1置为0
`<<` 左移	所有位向左移动指定位数，右位补零
`>>` 右移	所有位向右移动指定位数，左位补零

### 序列Range
Rust 提供了一个非常简洁的方式，用来生成连续的数值，例如 `1..5`，生成从 1 到 4 的连续数字，不包含 5 ；`1..=5`，生成从 1 到 5 的连续数字，包含 5

序列只允许用于数字或字符类型，原因是：它们可以连续，同时编译器在编译期可以检查该序列是否为空，字符和数字值是 Rust 中仅有的可以用于判断是否为空的类型。

```rust
for i in 'a'..='z' {
    println!("{}",i);
}
```

### 有理数和复数
Rust 的标准库相比其它语言，准入门槛较高，因此有理数和复数并未包含在标准库中：

- 有理数和复数
- 任意大小的整数和任意精度的浮点数
- 固定精度的十进制小数，常用于货币相关的场景

好在社区已经开发出高质量的 Rust 数值库：num。

### 总结

- Rust 拥有相当多的数值类型. 因此你需要熟悉这些类型所占用的字节数，这样就知道该类型允许的大小范围以及你选择的类型是否能表达负数
- 类型转换必须是显式的. Rust 永远也不会偷偷把你的 16bit 整数转换成 32bit 整数
- Rust 的数值上可以使用方法. 例如你可以用以下方法来将 13.14 取整：13.14_f32.round()，在这里我们使用了类型后缀，因为编译器需要知道 13.14 的具体类型

## 字符、布尔、单元类型

### 字符类型char
Rust 的字符不仅仅是 ASCII，所有的 Unicode 值都可以作为 Rust 字符，包括单个的中文、日文、韩文、emoji 表情符号等等，都是合法的字符类型。

由于 Unicode 都是 4 个字节编码，因此字符类型也是占用 4 个字节

### 布尔 bool

Rust 中的布尔类型有两个可能的值：true 和 false，布尔值占用内存的大小为 1 个字节

### 单元类型

单元类型就是 `()` ，唯一的值也是 `()` 

fn main() 函数的使用吧？那么这个函数返回什么呢？

看到过很多次 fn main() 的返回值是什么？main 函数就返回这个单元类型 ()，你不能说 main 函数无返回值，因为没有返回值的函数在 Rust 中是有单独的定义的：发散函数( diverge function )，顾名思义，无法收敛的函数。

再比如，你可以用 () 作为 map 的值，表示我们不关注具体的值，只关注 key。 这种用法和 Go 语言的 struct{} 类似，可以作为一个值用来占位，但是完全不占用任何内存。

## 语句与表达式

语句完成一个具体的操作，但是并没有返回值

表达式会进行求值，然后返回一个值。

表达式不能包含分号，一旦你在表达式后加上分号，它就会变成一条语句，再也不会返回一个值

## 函数

```rust
fn add(i: i32, j: i32) -> i32 {
   i + j
}
```