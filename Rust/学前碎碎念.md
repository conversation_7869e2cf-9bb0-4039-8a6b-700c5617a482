# 碎碎念

## 1000小时计划！

希望能尽快掌握rust这门语言，提升自我。每100小时为一个节点。现在先集中学习《Rust程序设计语言》

## 学习资料

- [学习 Rust](https://www.rust-lang.org/zh-CN/learn)
- [Rust 程序设计语言](https://kaisery.github.io/trpl-zh-cn/)
- [The Rust Programming Language](https://doc.rust-lang.org/book/title-page.html)
- [Rust语言圣经(Rust Course)](https://course.rs/about-book.html)

## 日常记录

统计下自己学习rust的小时数，以及学习进度

2022.5.21-22 4h 基本入门，常见编程概念，所有权
2022.7.24 时隔2个月！重新开始学习，寻找前端新的出路！

## 学习方式

不能光看，没啥用。Rust 是一门应用型语言，所以针对它的学习，应该也是应用为主。

思维导图+笔记+demo

笔记是学的时候记录的，demo和思维导图是学完后总结的。用来回忆的