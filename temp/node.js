fetch(
  'https://gtms-eu-test01.dotfashion.cn/api/ldes/api/label_api/print_box_label?service_id=2550&product_id=113442&package_num=1&shipping_time=&receive_time=&receive_name=&length=0&width=5&height=5&total_weight=0.020&package_weight=0.020&batch_no=&type=1&box_no=Y022505090000001&product_name=sqioPO-DPO-CA-BN-Na&lading_no=&base_standard=60%2A44%2A44&sensitive_cat=%E6%95%8F%E6%84%9F%E6%99%AE%E8%B4%A7&box_sign=&bags=0&bags_index=0&des_airport=&product_full_name=POl2&add_time=2025-05-09+17%3A00%3A58&add_name=%E6%BD%98%E5%8B%87%E5%AE%8F&tape_color=&paper_direction=2&box_tag_remark=&channel_code=&magnetism_type=0&package_no=BG1746759561144&declare_mode=0&channel_make_no=&product_box_no=&is_bagged_success=&sort_code=POl2&box_cpt=&box_cpt_os=',
  {
    headers: {
      accept:
        'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
      'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'cache-control': 'max-age=0',
      priority: 'u=0, i',
      'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"macOS"',
      'sec-fetch-dest': 'document',
      'sec-fetch-mode': 'navigate',
      'sec-fetch-site': 'none',
      'sec-fetch-user': '?1',
      'upgrade-insecure-requests': '1',
    },
    referrerPolicy: 'strict-origin-when-cross-origin',
    body: null,
    method: 'GET',
  },
)
  .then(response => {
    console.log('状态码:', response.status);
    console.log('响应头:', JSON.stringify(response.headers, null, 2));
    
    // 检查内容类型，判断如何处理响应
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      // 如果是 JSON 格式
      return response.json().then(data => {
        console.log('响应数据 (JSON):', JSON.stringify(data, null, 2));
        return data;
      });
    } else if (contentType && contentType.includes('text/html')) {
      // 如果是 HTML 格式
      return response.text().then(text => {
        console.log('响应数据 (HTML):', text);
        return text;
      });
    } else if (contentType && contentType.includes('application/pdf')) {
      // 如果是 PDF 格式
      return response.arrayBuffer().then(buffer => {
        const fs = require('fs');
        const path = require('path');
        
        // 生成文件名（使用当前时间戳）
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const fileName = `label_${timestamp}.pdf`;
        const filePath = path.join(__dirname, fileName);
        
        // 将 buffer 写入文件
        fs.writeFileSync(filePath, Buffer.from(buffer));
        console.log(`PDF 已保存为: ${filePath}`);
        return { filePath, buffer };
      });
    } else {
      // 其他格式
      return response.text().then(text => {
        console.log('响应数据:', text);
        return text;
      });
    }
  })
  .catch(error => {
    console.error('请求失败:', error);
  });