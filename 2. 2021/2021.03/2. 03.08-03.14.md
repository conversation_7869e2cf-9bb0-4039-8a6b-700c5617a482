# 03.08 - 03.14
本周 Vue、React深入学习。下周一开始进入实战篇，即修改简历，针对项目经验。

## 2021.03.08 周一
开始深度学习Vue 相关技术

主线任务
- Vue 2 使用
- Vue 3 使用
- Vue 2 原理
- Vue 3 原理
- Vite
- Vue 都考哪些问题？

工作上：搭建taro的开发框架，这一点还挺有意思的。taro/ts/vue3

- api
- 路由
- 页面跳转

## 2021.03.09 周二

工作目标是搭建taro开发框架，用上vue3, ts。

时间安排
- 过一遍vue3新特性，做笔记
- 自我总结一次
- 横向对比react hook
- 了解怎么在项目中使用ts，并且规范使用

- [Vue3 究竟好在哪里？（和 React Hook 的详细对比）](https://zhuanlan.zhihu.com/p/133819602)
- [Vue Composition API 和 React Hooks 对比](https://juejin.cn/post/6847902223918170126)

h5有现成的项目模板，但我想试试整活。毕竟我是个整活干员。小项目，大试验，好事儿。

在这个项目里面，我要搭建框架，制定规范，试水Vue3和TypeScript。反正是一人项目，可以随便折腾，应该就是我走之前的最后一个项目了。加油

15:13 快速入门ts，看看怎么写
17:57 看了好多，反而觉得能看懂就行，但真的得看看别人的项目才行


## 2021.03.10 周三
制定typescript项目规范
- 规范针对哪些地方
- 用到什么语法
- 对应的配置

那位荒山大哥的文档对强化前端知识体系真的很有帮助，很强力。建议有空就学，还要整理笔记。

- 前端规范
- 工程化
- 技术选型

typescript项目模板->拿到了现成的模板->看到了一点新语法糖->晚上结合女票的项目，看看怎么整个新的模板，还有谢谢规范


## 2021.03.11 周四
小案例：利用代理解决跨域问题

## 2021.03.12 周五
完成代理方面的改造，水了一篇，可以继续学习了

## 2021.03.13 周六


## 2021.03.14 周日

