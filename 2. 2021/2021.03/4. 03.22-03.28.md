# 03.22 - 03.28
这周要开始投简历试水了

## 2021.03.22 周一
今天有时间就修改简历，工作暂时没这么急。

优先级：
1. 修改简历
2. 微信H5 模板
3. 新项目开发

不要想着优化项目了，积重难返，又不写注释，改个屁。又没有收益，还不如多花点心思在修改简历上。

## 2021.03.23 周二
简历上还剩第3，第4个项目还没修改。晚上回去就能改完。

今日工作优先级：
1. 新项目开发
2. 下班后回家修改简历

## 2021.03.24 周三
简历初版写出来了，接下来就是准备面试和投简历了。

不要刷信息流了，干点正事，有空不如去刷面试题
- 刷面试题
- 看Vite
- 学React原理

今日工作
- 尽可能小的改动，不对原有代码优化，除非是有利于简历的。减少时间投入，以完成目标为主。
- header组件
  - active
  - dropdown
- 智慧作业分析
  - 搭建路由
  - 试写ECharts部分


17:00 进入划水学习环节

## 2021.03.25 周四

10:10 划水时间结束，认真规范今日工作。上午把工作完成得差不多了再开始划水学算法。

今日工作
- Echarts 部分组件复用
- header 组件

## 2021.03.26 周五
整理公司，周一投简历，同时重新开始学算法。

10：11 先确定今天能不能划水，其实是可以的，因为没有任务安排。那么开始安排今天的学习计划。

首先是昨天的fetch -> axios;
vite;
wasm;

16:04 早上整理了项目中wasm相关的问题，感觉问题不大了，可以去下一个点了。


## 2021.03.27 周六


## 2021.03.28 周日

