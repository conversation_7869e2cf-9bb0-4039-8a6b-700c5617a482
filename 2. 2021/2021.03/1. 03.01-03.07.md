# 03.01 - 03.07
要开始了。文件上传demo，准备跳跳。

从本周开始要安排跳跳计划了。

## 2021.03.01 周一
15:00 找到了一篇超不错的，就按照它的来设计 js和wasm的交互这一块
17:10 初步拿到指针（指针那块还是不太熟，导致返回了int），暂时就这样，转头去研究监听dom

- [如何监听 DOM 变化](https://juejin.cn/post/6844904000467255303)
- [MutationObserver](https://developer.mozilla.org/zh-CN/docs/Web/API/MutationObserver)
- [Mutation Observer API](https://javascript.ruanyifeng.com/dom/mutationobserver.html)

## 2021.03.02 周二
11:10 通信交互demo完成，可以去干下一件事（整理知识网络）了。
14:00 该开始了！

## 2021.03.03 周三
14:00 早上修bug去了，但是修不好，组件交互细节太麻烦了。等后端回复
15:50 开始正式摸鱼，修bug太花时间了。
16:37 划水也要有个方向啊

想看框架相关的了，还有就是js基础


## 2021.03.04 周四


## 2021.03.05 周五


## 2021.03.06 周六
前端知识体系整理，大方向上分为

- 算法
- 网络
- 前端基础（js/css/html）
- Vue
- React
- 工程化
  - Webpack
  - vite

网络搞得差不多了，该去搞Vue了，毕竟Vue还有Vue3要搞呢

## 2021.03.07 周日

