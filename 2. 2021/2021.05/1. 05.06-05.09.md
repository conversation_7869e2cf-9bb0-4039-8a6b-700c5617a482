# 05.06 - 05.09

## 2021.05.06 周四
环境变量的处理

- env
  - [DefinePlugin](https://webpack.docschina.org/plugins/define-plugin/)
  - [cross-env](https://www.npmjs.com/package/cross-env)
  - [npm config](https://docs.npmjs.com/cli/v7/using-npm/config)
  - [Three Things You Didn't Know You Could Do with npm Scripts](https://www.twilio.com/blog/npm-scripts)
  - [如何获取npm script的自定义参数](https://segmentfault.com/q/1010000016981987/a-1020000016982998)
  - [npm scripts 使用指南 - 阮一峰的网络日志](http://www.ruanyifeng.com/blog/2016/10/npm_scripts.html)
  - [NPM script字段使用技巧](https://zhuanlan.zhihu.com/p/107631483)

todo-list:
- 搞清楚为啥npm script设置的env无效（已完成）
- themeIsAva(已完成)
- projectIsAva(已完成)
- gz压缩处理


- npm 使用自定义参数


## 2021.05.07 周五
gzip压缩处理（并没有）

研究页面性能去了

## 2021.05.08 周六
研究页面性能去了

## 2021.05.09 周日

