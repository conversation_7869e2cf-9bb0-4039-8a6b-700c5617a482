# 05.24 - 05.30
本周干活，规划下每天干活

1. 青小鹿教学云平台-后台管理-前端重构
- 公共组件
  - header组件
  - 侧边栏权限
- 后台管理 用户信息(1)
  - 用户信息
  - 修改密码
  - 手机绑定
  - 关联管理
- header(1)
  - 数据库备份
  - 离线升级
- 基础数据
  - 教师(1)
  - 学生(1)

## 2021.05.24 周一
原定计划：
- 公共组件
  - header组件
  - 公共逻辑
  - 侧边栏权限

10:00 首先得把jsp页面跑起来先
11:37 利用slot使headerde的配置更为灵活

今天还要考虑路由跟权限的问题

## 2021.05.25 周二
原定计划：
- 后台管理 用户信息(1)
  - 用户信息
  - 修改密码
  - 手机绑定
  - 关联管理

早上处理了一下api，下午开始折腾项目的权限控制

- 从接口中获取权限
- 一整套系统的权限管理

参考vue-element-admin调整router跟permission

- 动态加载route

- layout 是用来干嘛的？


## 2021.05.26 周三
- 权限控制和动态加载route(完成)
- 整理request(完成)
- 关掉permission先(完成)
- 整理layout()



- 基础数据
  - 教师(1)


## 2021.05.27 周四

sidebar和router的关联性处理

今日工作：
完成
- user page && route(已完成)
- tailwind(已完成)
- request token(已完成)
- sidebar和router的关联性处理


- 基础数据
  - 学生(1)


## 2021.05.28 周五

- 设备管理-青鹿盒子/点阵笔(1)


## 2021.05.29 周六

今天下午的时候试着写写页面（虽然没啥好写的，因为没有UI图，只能写功能，但也只有mock数据

本日目标：将teacher的功能和接口划分下就得了，接口跟页面这个鸟样，也做不了太多事情


## 2021.05.30 周日

下周工作：
- 设备管理-青鹿盒子/点阵笔(1)
- 权限管理-空中课堂(1)
- 注册教育局/注册学校(0.5)
- 第三方管理(0.5)
- 远程升级(2)
