# 05.17 - 05.23

## 2021.05.17 周一
维护下vue-template，升级下脚手架和相应配置。

用回vue-cli，同时配置eslint prettier 

- vue-cli 中的babel 配置


## 2021.05.18 周二
可以做的工作
- vue-template 项目配置优化
- 项目中的http怎么更好的管理
  - axios 
  - 异步加载
  - loading
- 项目中的路由怎么更好的管理

想学习的
- 微前端管理
- event loop跟task跟promise

## 2021.05.19 周三
今天继续学习event loop跟task 相关


## 2021.05.20 周四
感觉需要正经干活了

- 搭建项目

husky的烦恼
husky v6跟之前v4区别太大了，有点莫名其妙的样子，难顶
vue cli中自带husky的变种yorkie，尽量用这个好了

- [yorkie](https://github.com/yyx990803/yorkie)
- [husky](https://github.com/typicode/husky)
- [husky doc](https://typicode.github.io/husky/#/)
- [vue-cli创建的项目中的gitHooks原理解析](https://juejin.cn/post/6844904063969001480)
- [从 0 开始手把手带你搭建一套规范的 Vue3.x 项目工程环境](https://juejin.cn/post/6951649464637636622)
- [husky使用总结](https://zhuanlan.zhihu.com/p/366786798)
- [How to use commitlint with yorkie & vue-cli@3.x](https://dev.to/martinkr/how-to-use-commitlint-with-yorkie-vue-cli-3-x-4617)

- 用vue cli创建完项目之后不用`git init`不然会把vue cli 自创的git hook给顶掉
- vue cli中尽量用yorkie(反正也没配啥，yorkie能应付)

cz工具集
- [commitizen](https://github.com/commitizen/cz-cli)
- [cz-customizable](https://github.com/leoforfree/cz-customizable)
- [commitlint](https://github.com/conventional-changelog/commitlint)
- [husky](https://github.com/typicode/husky)
- [yorkie](https://github.com/yyx990803/yorkie)


## 2021.05.21 周五

项目框架搭建

## 2021.05.22 周六


## 2021.05.23 周日

