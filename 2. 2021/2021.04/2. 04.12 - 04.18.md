# 04.12 - 04.18
面试，总结，升级

## 2021.04.12 周一
14:30 上午去面试去了，自我感觉良好，有必要补补短板。现在有点迷茫，要不要动手修补项目中的webpack？

项目性能优化
- babel升级（做了）
- webpack升级
- sass升级
- 针对pulgin的精细调教
- pageage.json的维护

整理当前plugins是有意义的，毕竟号称做了性能优化，vue cli2自带的要知道。

## 2021.04.13 周二
今天的任务强化js基础

- Symbol(完成)
- Array(完成)
- Object(完成)
- Function(完成)
- new 一个对象(完成)
- Map 和 Set
- Reflect
- Proxy

10:27 JS基础强化

过一遍MDN的JS API

## 2021.04.14 周三
继续昨天的学习，强化js基础。哎，孩子都学傻了，这么多东西。搞得自己都有点迷茫了。

前端 4年经验 广州 14k左右吧，多的也开不出。

- 选大厂吧，累人，但是镀金了，更好的上升空间
- 选小厂吧，问得没这么深，工作没这么忙。但上升空间优先
- 广州大厂不多，没这么多坑位，难度大
- 但无论如何，这回都是要跳槽的，因为薪资太低了

- proto,prototype,constructor
- 手写Promise.all


## 2021.04.15 周四


## 2021.04.16 周五


## 2021.04.17 周六


## 2021.04.18 周日

