# 04.25 - 04.30
折腾项目升级，最好这周内完结。本周有6天

## 2021.04.25 周日
升级了项目，跑通了dev

## 2021.04.26 周一
跑通了build，但优化项太多，在看

## 2021.04.27 周二
9:44 整理可以进行的优化

项目的性能提升点有很多，要考虑哪些要做，哪些推迟到之后处理

todolist：
- BundleAnalyzerPlugin 打包分析 √
- gzip 配置 √
- progress-bar-webpack-plugin 构建时添加进度条配置 √
- 本地打开文件 √
- 整理paths √
- 弃用babel-polyfill √
- 控制包的大小 √
- 按需加载 lodash √
- 按需加载 echarts √
- 组件懒加载 √
- TerserPlugin 配置 √


- **path**
  - [Webpack中publicPath详解](https://juejin.cn/post/6844903601060446221)
  - [output publicpath](https://webpack.docschina.org/configuration/output/#outputpublicpath)
  - [output path](https://webpack.docschina.org/configuration/output/#outputpath)
  - [本地运行编译后的dist文件](https://zhuanlan.zhihu.com/p/136381828)
  - [解决webpack中css独立成文件后图片路径错误的问题](https://blog.csdn.net/logan_LG/article/details/82107390)

- **babel**
  - [一文读懂babel编译流程，再也不怕面试官的刁难了](https://segmentfault.com/a/1190000039895291)
  - [Babel学习系列4-polyfill和runtime差别(必看)](https://zhuanlan.zhihu.com/p/58624930)
  - [Babel7 转码（五）- corejs3 的更新](https://segmentfault.com/a/1190000020237817)


- **dll**
  - [外部扩展(externals)](https://webpack.docschina.org/configuration/externals/)
  - [DllPlugin](https://webpack.docschina.org/plugins/dll-plugin/)
- **thread-loader**
  - [thread-loader](https://webpack.docschina.org/loaders/thread-loader/)
- **Manifest**
  - [傻傻分不清的Manifest](https://zhuanlan.zhihu.com/p/68136798)

- 性能优化
cdn配合externals



推迟：
- api


## 2021.04.28 周三
继续

## 2021.04.29 周四
完成整体项目升级

## 2021.04.30 周五
首屏优化



