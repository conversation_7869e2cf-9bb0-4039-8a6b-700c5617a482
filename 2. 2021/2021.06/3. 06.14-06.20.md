## 2021.06.16 周三

先复刻

- 数据库备份
- 离线升级
- 安徽省平台数据同步

原先计划的联调

- 数据库备份
- 离线升级
- 安徽省平台数据同步
- 全连接数据同步(已完成)
- 数据批量管理(被需求波及到，被干掉了)
- 教师(被需求波及到)
- 学生(被需求波及到)
- 青鹿盒子(被需求波及到)
- 点阵笔(被需求波及到)
- 远程升级(场景联调困难)

这么说今天全力开发三个复刻就完事了

- 数据库备份(16:20 已完成)
- 离线升级
  - template
  - 接口
  - handle 交互
  - 逻辑

## 2021.06.17 周四

继续奋战离线升级！如果做完了，距离补锅就剩一步了

## 2021.06.18 周五

昨天主要是开会，今天才是主力干活

- 离线升级
  - 后台管理页面的对应逻辑（完成）
    - disable
    - visible
  - login 页的对应逻辑
  - clear SET_SHOW_UPGRADE_STATUS
  - 前置接口的处理

## 2021.06.19 周六

- 处理 title(完成)
- 处理同步弹框
- 处理 redirect

- 同步弹框
  - vuex
  - api
  - dialog 功能逻辑
  - UI(周一再做)

17:00 整完了，下班
