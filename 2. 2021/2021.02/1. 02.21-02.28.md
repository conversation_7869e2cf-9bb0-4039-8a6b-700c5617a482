# 02.21 - 02.28
2月最后一周，也是新年第一周，开始计划。这周目标：文件转码上传，并修改简历，加上这一项。

文件转码上传-任务拆分
- 点击上传文件下载文件（通信）
- 上传文件下载到转码后文件（搞定C工程->wasm）
- 边转码边上传，前端-node（完全体）

## 2021.02.21 周日
任务拆解
- 搞清楚 js -> wasm 的通信(已完成)
- 搞清楚 wasm -> js 的通信(已完成)
- Module是什么，window.Module是什么？
- importObject是什么？
- 将file传到wasm中(已完成)


我需要的是什么？

现在用的是自动生成的index.html 文件，实际上我要的只是js文件，并且发起调用。先调用一个简单的C程序，做到双通信。

14:29 js->wasm的通信基本懂了点，想了解importObject

今日目标：实现 file 从js->wasm，wasm->js

先写一个C程序，读取文件，返回文件名，接着开始用“胶水”

胶水基本上靠emcc自动生成js文件，js->wasm有了，但怎么wasm->js呢？

## 2021.02.22 周一
今日的目标大致实现了，靠这个回答[How can I load a file from a HTML input into Emscripten's MEMFS file system?](https://stackoverflow.com/questions/61496876/how-can-i-load-a-file-from-a-html-input-into-emscriptens-memfs-file-system),
但是还不够，要更进一步，打通wasm->js的关系

实践：
- [记一次完整 C++ 项目编译成 WebAssembly 的实践](https://developer.aliyun.com/article/740902)
- [c++项目转成wasm全过程](https://zhuanlan.zhihu.com/p/158586853)
- [使用emscripten编译ffmpeg程序为js](https://blog.csdn.net/congbai1203/article/details/87192741)
- [Exporting file from Wasm memory to JS](https://stackoverflow.com/questions/58864109/exporting-file-from-wasm-memory-to-js)

书：
- [深入浅出WebAssembly](http://reader.epubee.com/books/mobile/d4/d403824d4bba4f9ac649ac8a4e4c8f03/text00006.html)
- [C/C++面向WebAssembly编程](https://github.com/3dgen/cppwasm-book/blob/master/zh/README.md)

doc:
- [emcc doc](https://emscripten.org/docs/tools_reference/emcc.html#emccdoc)
- [emcc settings](https://github.com/emscripten-core/emscripten/blob/master/src/settings.js)
- [Emscripten 编译器(emcc) 命令总结](https://blog.csdn.net/wngzhem/article/details/105192706)

核心问题是wasm->js的通信问题，按照直接的思路不太对，之前是想着利用importObject的imports，这样是可以，但之前只停留在wasm层，现在要想想怎么在C层调用import进来的function，用于将转码后的数据/整个文件返回给js

说白了，我需要的是交换数据，不，都要，搞清楚

- C层怎么调用import进来的function（在4readFile中实践）
- 搞清楚emcc配置 --js-library --post-js

15:30 成功实现了c中调用js function
15:53 解决了一个大问题，接下来达成今日目标1

今日任务1：将文件名返回到DOM
今日任务2：将文件传回给js部分
明日目标：将C工程转成wasm

16:40 目前难点：文件系统不太懂，任务1基本能简单解决，目标2的js部分也好处理做成下载文件即可。就不用去获取文件名了，任务1划掉，直奔任务2。

首先要解决的是C语言的读写文件


## 2021.02.23 周二
9:54 开始投入工作，本周要完成这个目标（文件转码上传），中途可能开始有任务下达，没有这么有空，所以得抓紧时间写demo。

文件转码上传-任务拆分
- 点击上传文件下载文件（通信）
- 上传文件下载到转码后文件（搞定C工程->wasm）
- 边转码边上传，前端-node（完全体）

今日工作：
- C语言的读写文件
- 做出demo，点击上传文件就下载文件

10:45 今天想早上尽快达成通信部分的目标，下午开始折腾C工程wasm化

- [C 文件读写](https://www.runoob.com/cprogramming/c-file-io.html)
- [项目实践 wasm-experiments/zip](https://github.com/piotrkabacinski/wasm-experiments/tree/master/zip)

14:00 上午的问题还没能解决，但知道了一点，利用内存在读写文件是不可取的，但暂时就先走内存路线，等跑通目标1再说

14:28 可以了，重点就是C文件的读写操作[How to copy a image to a Directory in C language](https://stackoverflow.com/questions/21713777/how-to-copy-a-image-to-a-directory-in-c-language)。对于C语言来说，text file和binary file是明显不一样的[C File Handling](https://www.programiz.com/c-programming/c-file-input-output)。

开始对付大难题：如何将C工程转成wasm，首先就是要将它去配置化（猜的，不一定对，得查查资料）

先让之前的程序跑起来，接着尽可能的去掉vc配置化。

17:00 项目跑通了，接下来就是怎么让项目可以在wasm中使用。

狂学c语言

## 2021.02.24 周三
今日目标，继续倒腾C工程，打算做到以gcc来编译项目即完成（应该就可以了吧）

任务1：跑通ffmpeg的例子
任务2：将例子魔改，接着跑通
任务3：应该可以转wasm了

嗨，该准备面试了。但工作时间还是忙工作，我真的想实现出来诶。真的得找人抱大腿了。

今天的主要工作真的就是说服C，抱上大腿。

14:49 成功了，成功拉树叶入坑，不再是摸鱼级的工作了。得打好下手了。这么说我就是负责写胶水，得认真起来了。胶水就是数据交换，这边由我来负责。

- 文件传递
- 总结出js与C之间交换数据的方式
- 准备上传文件demo

思路：用`indexedDB`和`Mutation Observer`

- [如何监听 DOM 变化](https://juejin.cn/post/6844904000467255303)
- [IndexedDB - MDN](https://developer.mozilla.org/zh-CN/docs/Web/API/IndexedDB_API)
- [浏览器数据库 IndexedDB 入门教程 - 阮一峰](http://www.ruanyifeng.com/blog/2018/07/indexeddb.html)
- [indexedDB使用总结](https://www.jianshu.com/p/8fe30cecad36)
- [IndexedDB使用与出坑指南](https://juejin.cn/post/6844903570005835789)

## 2021.02.25 周四

今天要鞍前马后服务到家，搭建好前端部分

- indexedDB相关的胶水服务
- 监听DOM
- 文件上传demo + indexedDB + 监听DOM

## 2021.02.26 周五
今天早上先修复bug，下午再继续折腾wasm

10:00 修就快点修，不要耽误时间了，原计划要本周搞定的就本周搞定。
15:33 搞定，去折腾wasm


## 2021.02.27 周六

20:38 今天试试在demo的基础上进行内存交换数据。c->js，中间通信

今日目标 js中读取结构体数据（失败，摸鱼去了）

## 2021.02.28 周日

17:28 继续，争取今天做出来

- [记一次完整 C++ 项目编译成 WebAssembly 的实践](https://developer.aliyun.com/article/740902)
- [wasm + ffmpeg实现前端截取视频帧功能](https://www.yinchengli.com/2018/07/28/wasm-ffmpeg-get-video-frame/comment-page-1/)
- [ffmpeg-wasm-video-to-picture](https://github.com/liyincheng/ffmpeg-wasm-video-to-picture)
- [2.4 JavaScript与C交换数据](https://www.cntofu.com/book/150/zh/ch2-c-js/ch2-04-data-exchange.md)