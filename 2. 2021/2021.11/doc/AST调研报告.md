# 基于 AST 做重构的可行性调研

AST 牛逼！

## 调研前因

在开发时间控件替换任务时，我发现开发过程中大部分为重复性工作。这种工作可以尝试工具 🔧 来通过 AST 实现代码替换，节约开发人员的生命，开发人员只要 code review 就行了。

所以开展本次调研。

## 首先是用什么开发

考虑的方向：

- 命令行工具 cli
- 编辑器插件 vscode plugin
- 编辑器插件 webstorm plugin

命令行工具 cli
优势

- 通用性强，久经考验
- 但是没有界面，交互性弱
- 编辑器插件 vscode plugin
  - 基于 typescript 开发，对前端来说能快速上手
  - vscode 开源，文档不清晰的地方，可以直接看源码
  - vscode 用户基数大，社区繁荣
  - vscode 背靠大树微软，更新频繁，维护积极
  - 好处有很多，不够我再编
- 编辑器插件 webstorm plugin
  - vscode 的点反着来基本就是 webstorm 的
  - 个人倾向于 vscode plugin, 与 cli 相比，能提供更丰富的交互；与 webstorm plugin 相比，开源能看代码，而且前端普遍不懂 java。

不是 webstorm 的问题，是我问题，抱歉。

## AST 的使用场景

也就是说，只要操作是在“能读取文件，获取 AST，遍历和更新节点，写入文件”这么个框里的，理论上都可以写。所以在安排代码重构需求前可以考虑通过 AST 来实现。

AST 适用的场景: 1. ast 代码容易写;2. 重复的工作量大。

## 参考

## AST 是什么

由于篇幅有限，这么牛逼的东西只能简单吹吹。

抽象语法树(Abstract Syntax Tree) 简称 AST，是以树状形式表现编程语言的语法结构，树上的每个节点都表示源代码中的一种结构。JavaScript 引擎工作的第一步就是将代码解析为 AST，Babel、eslint、prettier 等工具都基于 AST。

以 Babel 为例，使用 AST 的三板斧：parse 解析 -> transform 转换 -> generate 生成（分别对应工具包：@babel/parser，@babel/traverse，@babel/generator）。

## AST explorer

这是一个 ast 在线调试工具，有了它，我们可以非常直观的看到 ast 生成前后以及代码转换。

- [AST explorer](https://astexplorer.net/)

## Babel

直接 pass，因为 Babel 的目的是对代码向下兼容的，会进行代码转换，而且即使不做任何修改，输出的代码和原本的也有区别，比如空格，空行，注释位置会变化，所以不使用。

Babel 的 4 个核心包

- @babel/parser
- @babel/traverse
- @babel/generator
- @babel/types

但 Babel 的 4 个核心包是可以单独使用的，前 3 个已经介绍过了，@babel/type 是权威的 AST 语法字典，即使是其他项目也会以此为标准。

- 优势
  - 最出名的 AST 工具（之一）
- 劣势

  - 会进行代码转换，即使不做任何修改，输出的代码和原本的也有区别

- [babel-types](https://babeljs.io/docs/en/babel-types)

## jscodeshift

这里提一下代码重构的专业选手 ✊ React 团队，在推出 React v16，断崖式升级之后，推出了 react-codemod，用于帮助用户更新 React API。（是感到愧疚了么）

react-codemod 是 jscodeshift 的脚本封装，jscodeshift 是 recast 的封装，recast 的默认 parser 是 esprima，esprima 是著名的 AST 引擎之一。

react-codemod -> jscodeshift -> recast -> esprima，这套俄罗斯套娃 🪆 可以选 jscodeshift。因为 react-codemod 是命令行工具，jscodeshift 则可以直接引入调用，而且 jscodeshift 除了可以使用自身 API 之余，也能使用 recast 的 API。

- 优势
  - 比较有名的 AST 工具
  - API 是 jquery 风格，易上手
  - 老牌代码重构工具，网上资料较多
- 劣势

  - 没有官方文档，看 API 是直接看源码（但还行）
  - 因为套娃，要用 API 还得看 3 份文档，jscodeshift 的，recast 的，AST Types 的（有点难顶，后续我会继续整理）

- [jscodeshift](https://github.com/facebook/jscodeshift)
- [react-codemod](https://github.com/reactjs/react-codemod)
- [recast](https://github.com/benjamn/recast)
- [esprima](https://github.com/jquery/esprima)
- [AST Types](https://github.com/benjamn/ast-types)

## gogocode

gogocode，阿里妈妈团队开发出来的工具 文档写得很好看，API 易上手，还提供了 30 个 case，能满足大部分的使用场景。

它跟 jscodeshift 相比，除了有自己的 cli，还有自己的 PlayGround（相当于 ast explorer），还有自己的 vscode 插件。相当于给 jscodeshift 配了副轮椅，非常舒服，上手难度低，满足大部分场景。

- 优势：
  - API 是 jquery 风格，易上手
  - 有自己的文档，playGround，cli，vscode 插件
- 劣势：

  - 2021 年 3 月的新项目，用户群体不大，vscode 插件只有 190 install
  - 跟 jscodeshift 的套娃 🪆 相比，跟社区的互动太少，一揽子工程

- [阿里妈妈出的新工具，给批量修改项目代码减轻了痛苦](https://juejin.cn/post/6938601548192677918)
- [「GoGoCode 实战」一口气学会 30 个 AST 代码替换小诀窍](https://juejin.cn/post/6943114726175932452)
- [GoGoCode - 文档](https://gogocode.io/zh/docs/specification/getting-started)
- [](https://github.com/thx/gogocode)

## 结尾

基于 AST 做重构是可行的，社区有大量的经验可供参考。重点是怎么降低 AST 操作的复杂性，gogocode 是个很好的尝试，但能否承担日后的需求有待讨论。jscodeshift 的缺点是 API 难找，但可能是我的问题，后续会继续整理
