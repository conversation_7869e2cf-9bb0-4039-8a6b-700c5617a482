# vscode

## 思路

路子应该是可行的：通过 babel 获取 AST 树，改 AST，根据 AST 生成 js 文件/jsx 文件

AST 的话，jscodeshift 更符合我的要求，但是 api 有点莫名其妙，得多看几篇

要干的事情：

- 更换组件，并替换属性
-

代码自动升级工具 codemod

## 进度

- 用上 git commit(11.10 11:45, 已完成)
- 解析 js 相关的文件，获取 AST
  - 需要学习 Babel 相关的前置知识
  - 在项目使用 babel(env, react)
- 拿到 js 文件的 AST(11.11 10:00, 已完成)
- 拿到 jsx 文件的 AST(11.11 13:30, 已完成)
- 获取 AST 的节点(11.11 14:00, 已完成)
- 修改 AST 的节点
- 根据 AST 原样输出文件

- 改为使用 jscodeshift
- 将 jscodeshift 集成到 vscode 中(已完成)
- 重新生成源码(已完成)

- damo 已经跑通，接下来

## 资料

### vscode

- [vscode api](https://code.visualstudio.com/api)
- [VS Code 插件开发文档](https://github.com/Liiked/VS-Code-Extension-Doc-ZH)
- [VSCode 插件开发全攻略系列](https://github.com/sxei/vscode-plugin-demo)
- [How to access the api for git in visual studio code - stackoverflow](https://stackoverflow.com/questions/46511595/how-to-access-the-api-for-git-in-visual-studio-code)

### Babel 和 AST

- [JavaScript AST 入坑指南](https://juejin.cn/post/6945392689655316494)
- [babel-parser 官方文档](https://babeljs.io/docs/en/babel-parser)
- [AST in TypeScript 实践](https://cloud.tencent.com/developer/article/1452826)
- [ast explorer](https://astexplorer.net/)

### jscodeshift

- [react-codemod](https://github.com/reactjs/react-codemod)
- [jscodeshift](https://github.com/facebook/jscodeshift#readme)
- [recast](https://github.com/benjamn/recast)
- [esprima](https://www.npmjs.com/package/esprima)

目前要解决的事情是

- 根据文件生成 AST
- 遍历和更新 AST
- 将 AST 重新生成源码

原理是 解析 parser，转换，生成 generator

# The jscodeshift API

## 核心概念

**AST nodes**

**Path objects**

**Builders**

- [超实用的 AST 的基本操作，你学会了吗？](https://juejin.cn/post/6984972209240408078)
  - 核心阅读
- [jscodeshift 入门](https://juejin.cn/post/6844904195624009736)
- [我在真实项目中使用了 AST 大法！](https://juejin.cn/post/6844904016455925768)
  - 用了 recast，有一定的借鉴意义
- [jscodeshift - github](https://github.com/facebook/jscodeshift)
- [[手把手系列] 开发一个 VS Code 业务插件](https://segmentfault.com/a/1190000022914807)
- [手把手教你写几个实用的的 AST 插件](https://segmentfault.com/a/1190000021482427)
- [如何使用 jscodeshift+Commander 来开发一个简易的重构脚手架](https://juejin.cn/post/6987603312237346830)
- [字节前端如何基于 AST 做国际化重构？](https://mp.weixin.qq.com/s/O7HaOKBGMXwaE3KkqANX3A?forceh5=1)
- 写得很好 [重构利器 jscodeshift](https://mp.weixin.qq.com/s/emM3JbmKWV7NurrIGgIRtg)
- [jscodeshift 那点秘密](https://juejin.cn/post/6991349946381238303)
