# AST开发指南

## 调试工具 ast explorer
- [astexplorer](https://astexplorer.net/)

这是一个AST的在线调试工具，有了它，可以很直观的看到生成的AST和对应的代码块

## The jscodeshift API
核心概念：

AST nodes


Path objects

Builders
即ast构造器，有时我们不只要修改节点，还要创造节点，比如自动导入foo模块，

jscodeshift所有ast相关操作都来自于ast-types, 具体构造器参数可以查看ast-types

Collection





## 参考
- [像玩 jQuery 一样玩 AST](https://juejin.cn/post/6923936548027105293)
- [超实用的AST的基本操作，你学会了吗？](https://juejin.cn/post/6984972209240408078)
- [jscodeshift 入门](https://juejin.cn/post/6844904195624009736)

- [阿里妈妈出的新工具，给批量修改项目代码减轻了痛苦](https://juejin.cn/post/6938601548192677918/)
