# 11 月份 第 4 周

本周就是黑五了！得支棱起来

## 本周 todo

### 生活

- 明日方舟：松烟行动
- 模型：rg 海牛
- 周末：26 号 女票生日
- 周末：27 号 帮忙搬家

### 工作

- 准备分享 AST 操作（学学正统 babel）

## 2021.11.22 周一

### 生活

- 明日方舟更新：松烟行动

### 工作

- 继续测试 bbl，确认是否可以上测试环境(已完成)
- 调研 menu 组件

menu 组件要求:

1. 适应我们的可搜索
2. 颜色可配置
3. 宽度可拉伸

- 调研 menu 组件

  - 确认 menu 的参数
  - 确认 stickyMenu 的参数
  - 确认 wms sticky menu 的参数
  - 确认现在已实现的功能

- shineout menu
  - 比不上 antd，少了很多子组件写法
- stickyMenu
  - 亮点是折叠功能
- wmsStickyMenu
  - fork 自 stickyMenu，去掉了折叠功能，完善了搜索功能
- 现在的问题的：该怎么开发？

## 2021.11.23 周二

### 生活

### 工作

实现的目标：wms 项目中的业务组件从 antd 迁移到 shineout

- 集成搜索功能
- 集成主题换色功能（theme 够用么）
- 集成拉伸功能

现在的资料

- shineout menu (基本用不了)
- stickymenu (魔改，多了搜索功能)
- wmsstickymenu (魔改的魔改，删了折叠相关，兼容了数据)

Q：基于 wmsstickymenu 继续进行开发？
A：不改了，直接基于 wmsstickymenu 上继续开发，毕竟已经继承了搜索功能，直接开始做下一个功能: 拉伸，边做边发现问题

20:00 完成拉伸 demo，明日测试下多数据和省略号

## 2021.11.24 周三

### 生活

今天 6 点下班快乐拼胶

准备下弹药：

- 兄弟，距离 12 月不到 1 周了，还是没消息？
- 还是抓紧时间整了吧，不然没完没了的，整上了我也就不烦你了
- 我也只是要求你们提供本来应该有的东西，不过分吧，都快 2 个月了。，脾气够好的了，换其他人早投诉过去了
- 你听听，这话合理吗
- 我也不想咄咄逼人，我吃点亏，改成 20M3 个月 240 得了，把之前钱退了吧 ，720-240=480

结果他又当鸵鸟了，习惯了，先预热一下。不要让这家伙坏了我的好心情

### 工作

- menu
  - 可拉伸
    - 使用 wms 真实数据(已完成)
    - 省略号处理(已完成)
  - 业务功能直接内置进去
  - theme
    - css 参数化
  - 搜索
    - 配置化
    - canFilter

现在是整理 API，将搜索功能整合进去

还有那个 renderItem，太 TM 扯淡了，但这个是 menu 的，有点难改善

别盯着狗屎代码不放了，去看看 antd 是怎么实现的更好

## 2021.11.25 周四

### 工作

11:00 今天完成组件的整体开发，周五开始试用组件，争取不加班
15:00 工作量主要集中搜索上，先定制 Item
16:00 MenuItem 已完成，开始暴露搜索相关的配置

- menu

  - 可拉伸
    - 使用 wms 真实数据(已完成)
    - 省略号处理(已完成)
  - theme
    - css 参数化
  - 搜索

    - 配置化 canSearch(已完成)
    - 搜索功能内置化(高亮, )
    - 模块暴露

    - menuItem(已完成)
    - searchKey(已完成)
    - openKey

## 2021.11.26 周五

### 生活

不知道为啥，最近好像魔怔了一样，总觉得吉姆特装跟吉姆镇暴的设计很简洁很对胃口，很想整一个，可能某天我就莫名其妙买了

### 工作

交互行为优化，一方面是 default，进入页面时可以根据路由选中 key，并展开对应层级；另一方面是搜索，点击之后收起其他层级

重点还是那个莫名其妙的 openKey

- menu
  - 可拉伸
    - 使用 wms 真实数据(已完成)
    - 省略号处理(已完成)
  - theme
    - css 参数化
  - 搜索
    - 配置化 canSearch(已完成)
    - menuItem(已完成)
    - searchKey(已完成)
    - handleOpenChange(令人迷惑, 但不影响开发，可以先放放)
    - openKey
      - 路由匹配(已完成)
      - 搜索点击后收起其他层级
        - 需要 openKeys，将组件改为受控
        - menuData,menuOpenkeys 状态化

受控的 openKeys

搞完这个就可以收工了，可以试试在自己项目中用用.
15:31 艸，把孩子都改哭了，代码的耦合度高到吓人，先将 menuData,menuOpenkeys 状态化

## 2021.11.27 周六

### 生活

- 女票生日
- 帮人搬家
- 拼胶：海牛

## 2021.11.28 周日

### 工作

爷今天就想把组件整到满意为止

- menu
  - 搜索
    - handleOpenChange(令人迷惑, 但不影响开发，可以先放放)
    - openKey
      - 路由匹配(已完成)
      - 搜索点击后收起其他层级
        - 需要 openKeys，将组件改为受控
        - menuData,menuOpenkeys 状态化
