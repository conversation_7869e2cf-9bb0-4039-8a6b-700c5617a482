# OFC-26489 可拉伸菜单

## 进度

- WMS 引用组件
  - 布局(已完成)
  - 参数(已完成)
  - title 部分
  - 功能(已完成)
  - 点击后的切换，页面跳转(已完成)
  - 省略号(todo 应该加到 MenuItem 中)
  - 兼容 icon(已完成)
  - 渐特效(已完成)
  - Tooltip(已完成)
  - uiText
- 组件
  - 伸缩条改为 4px(已完成)
  - menuItem(已完成)
  - 禁止拉伸 属性 (已完成)
  - 改为非受控组件
  - 多写 case

## 0.0.4

非受控改造

将 openKeys 和 searchKey 封装进去

- 封装 openKeys 和 onOpenChange
- 封装 searchKey 和 onSearchChange

- search 部分(已完成)
- openKeys 部分
  - openKeys, onOpenChange(已完成)
  - getOpenKeys
- uiText

openKeys 的处涉及到 activeKey

12/10 改造：

- getOpenKeys 改造 property -> keygen(已完成)
- openKeys 内置化 props->state(已完成)
- onClick 处理(已完成)
- defaultOpenKeys
-

暴露一个方法，获取 key 值

一个遍历方法，获取 key 值
一个遍历方法，获取 openKeys

都是为了设置 openKeys

- 初始值，设置 active
- utils.getCurrentNode
