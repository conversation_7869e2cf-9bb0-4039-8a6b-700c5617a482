# OFC-26302 返仓流程改造项目

## 概览

C-210470
基线文档：https://ax.sheincorp.cn/unpkg/ax/W5DDYY/newest/start.html#id=4pf6vb&g=1

开发时间：11/27-12/09
测试时间：12/10-12/21
验收时间：12/22-12/27
上线时间：12/30

## 前端工作内容

- 库内管理 / 异常管理 / 取消返仓列表 修改
  - 列表新增 存储属性 装箱人 上架人 装箱批次
- 后台管理 / 基础数据 / 工位管理 修改
  - 列表新增 园区
  - 打印
- 系统配置 / 库内配置 / 返仓混装配置 新页面

## 进度

- 开发
  - server(已完成)
  - reducers
  - jsx
    - header(已完成)
    - list(80%)
    - handle(80%)
    - modal
      - 常规 form(已完成)
      - 混装组合(已完成)
  - 针对“编辑”处理
  - 接口传入数据处理
  - modal 确认的逻辑
- 联调
  - 取消返仓列表
    - 列表栏新增字段 上架人、装箱人、存储属性（已完成）
  - 工位管理（等支撑组分配后端来开发）
    - 列表栏新增字段 园区
    - 打印功能（待定）
  - 返仓混装配置
    - 一整个新增页面
      - 园区修正（等正确的接口）


- 取消返仓列表
  - 多件不能盘亏(完成)
  - 操作记录，多人逗号隔开(这个也是后端要做的)
- 工位管理 
  - 注意编辑(完成，没啥好注意的，数据都是后端返回的)
  - 打印 场景代码: A4(完成)
- 返仓混装配置(完成)

按时完成任务了