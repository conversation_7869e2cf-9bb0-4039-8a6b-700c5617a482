# AST 实战速成

批量重构代码，减少体力劳动。
在实战中速成，在速成中实战。

## 需求是什么

简单来说，就是要将 `import { t } from 'rrc-loader-helper'` 替换为`import { t } from '@shein-bbl/react'`

## AST nodes

简单来说就是识别出 含有 t 和 rrc-loader-helper 的 import 语句。

那么换成 AST 的说法，应该怎么描述？见 AST Explorer

也就是说我们要找的是符合这个结构的 ImportDeclaration

```
{
  type: "ImportDeclaration"
  ...
  specifiers: [
    ...
    {
      type: "ImportSpecifier",
      ...
      imported: {
        type: "Identifier"
        ...
        name: "t"
      }
    }
  ]
  source: {
    type: "StringLiteral"
    ...
    value: "rrc-loader-helper"
  }
}
```

事就是那么一回事儿，继续往下鼓捣

## jscodeshift

- [jscodeshift - github](https://github.com/facebook/jscodeshift)

这里我用的是 jscodeshift 来 work，原因是方便

jscodeshift 是一个重构代码的工具集，对 recast（一个通过分析 AST 做代码修改的库）做了封装，通过 jscodeshift 编写 codemod, 然后对指定文件运行就可以批量重构代码。常见的 react 升级的 codemod 就是基于 jscodeshift 的。

简单介绍下 jscodeshift API 的几个概念

- AST nodes
- codemod
- Collection
- Builders

### codemod

举个一个例子:

```

```

1. 找到节点

- `api.jscodeshift(fileInfo.source)`将字符串源文件`fileInfo.source`转换为一个可遍历/操作的 Collection
- `findVariableDeclarators('foo')`就是找到变量 foo 的声明语句

2. 操作节点

- `renameTo('bar')`是把变量 foo 重命名为 bar

3. 输出

- `toSource`把处理后的 ast 转换为字符串输出。

这就是 codemod 的基本流程：找到节点->操作节点->输出

### Collection

jscodeshift 对 recast 的一个重要封装就是 Collection, 顾名思义 Collection 是一个 ast 节点集合。

这里和 jQuery 或数据库的操作真的很像，比如`api.jscodeshift(fileInfo.source).findVariableDeclarators('foo')`是不是就是`$('.foo')`或`db.collection.find( { id: 'foo' } } )`

那么它的 api 在哪里看呢？直接看源码，因为没有 api 文档，而且源码的注释写得还行

常见的 api 有

- filter()
- forEach()
- some()
- every()
- map()
- ...

具体的要去看源码

- [Collection.js](https://github.com/facebook/jscodeshift/blob/main/src/Collection.js)
- [index.js](https://github.com/facebook/jscodeshift/blob/main/src/collections/index.js)

### Builder

Builder, 即 ast 构造器，有时我们不只要修改节点，还要创造节点。

举个例子，比如自动导入 foo 模块，也即是要在代码里插入`import foo from 'foo';`。此时就要构造这个节点

```javascript
j.importDeclaration(
  [j.importDefaultDeclaration(j.identifier('foo'))],
  j.literal('foo')
);
```

jscodeshift 所有 ast 相关操作都来自于 ast-types, 具体构造器参数可以查看 ast-types。builder 的 API 具体的要去看 babel/types。这里只是简单介绍，具体的可以看文档。

- [babel-types](https://babeljs.io/docs/en/babel-types)
- [ast-types - github](https://github.com/benjamn/ast-types)

### 文档太少

除了看文档之后，看可以去看 js-codemod，相当于官方提供的习题册大全。

- [js-codemod - github](https://github.com/cpojer/js-codemod/)

## 回到案例

到这里，直接看代码就好了

```javascript
const j = require('jscodeshift');

const I18N_FUNCTION_NAME = [
  't',
  'i18n',
  'useI18n',
  'I18NProvider',
  'I18nContext',
];
const NEW_IMPORT = '@shein-bbl/react';
const ORIGINAL_IMPORT = 'rrc-loader-helper';
const PARSE_OPTIONS = { quote: 'single' };

/**
 * @name transform
 * @description 通过jscodeshift进行代码转换
 * @param {String} fileSource
 * @returns {Boolean|String}
 */
export const transform = (fileSource: string) => {
  const root = j(fileSource); // 根ast
  const importDeclaration = root.find(j.ImportDeclaration, {
    source: {
      value: ORIGINAL_IMPORT,
    },
  });

  let i18nSpecifiers: any[] = []; // i18n相关的specifier
  let otherSpecifiers: any[] = []; // i18n不相关的specifier

  importDeclaration.map((item: any) => {
    let specifiers = item?.value?.specifiers || [];

    for (const specifier of specifiers) {
      let name = specifier?.imported?.name || '';

      if (I18N_FUNCTION_NAME.includes(name)) {
        i18nSpecifiers.push(specifier);
      } else {
        otherSpecifiers.push(specifier);
      }
    }
  });

  // 没有i8n相关函数，不用改变
  if (!i18nSpecifiers.length) {
    return false;
  }

  if (otherSpecifiers.length) {
    return importDeclaration
      .replaceWith([
        j.importDeclaration(i18nSpecifiers, j.literal(NEW_IMPORT)),
        j.importDeclaration(otherSpecifiers, j.literal(ORIGINAL_IMPORT)),
      ])
      .toSource(PARSE_OPTIONS);
  } else {
    return importDeclaration
      .replaceWith(j.importDeclaration(i18nSpecifiers, j.literal(NEW_IMPORT)))
      .toSource(PARSE_OPTIONS);
  }
};
```

这段代码有效的减少了体力劳动，节约了生命。环保又节能。

## 总结

用关键词串联一下：jscodeshift, AST node, codemod, Collection, Builders。

jscodeshift 这个工具集，就像个架子上的葡萄，提起来一串一串，连 API 都不好找。但成了之后真的省事，希望这篇实战能降低大家上手 jscodeshift 的门槛。
