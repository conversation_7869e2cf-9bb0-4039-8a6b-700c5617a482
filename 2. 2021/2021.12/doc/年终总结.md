# 2021年度 总结

供应链研发部-仓储研发部-仓储前端部-郑智彬

由于只入职了 3 个月，还不到，所以这是季度总结！

## 2021 年回顾

1. 入职 3 个月，完成参加并完成任务 18 个。参与了 wms 项目二期优化，开发了封装了业务组件拉伸菜单，编写了 AST 脚本等。
2. 2021 年，线上故障数量为 0（占了入职时间短和黑五的便宜）
3. 提升开发效率，利用 AST 开发脚本实现了 bbl 版本升级
4. 技术分享，输出了 AST 分享文档，并计划以此开展分享

### 不足

1. 对业务的流程还不够熟悉，增加了开发耗时
2. 不够细心，导致测试阶段仍有一些简单的bug

## 2022 年展望

1. 能够熟练掌握业务的数据流程和操作流程，创造有价值的产出。
2. 优化系统，降低开发难度，提升工作效率。
3. 技术分享，输出分享文档，营造团队技术氛围。

## 建议

1. 制定了规范就要维护规范，尽量用工具去维护
2. 开发针对 wms 的 vscode 插件，更方便的定义常量，更快捷的调用到公共函数，更简单的使用常见项目代码片段(我自己想干的)

1. 赋能
2. jira 计划->产出
开发规范 

1. 规范、流程
