# 10 月份 第 2 周

超级繁忙的第 2 周，因为要搬家

## 本周计划

### 生活

搬家大计划

### 工作

- 对 mot 系统进行 demo 开发，并 code review
- 搭建系统 demo，配置 cicd 系统，配置 paas
- 系统应用能够顺利启动

mot 和 系统 demo

## 2021.10.08 周五

### 生活

晚上回去跟新房东签合同，接下来整理东西

### 工作

todo：

- 折腾下 shineout 的组件，特别是 SearchArea，有点奇怪 🤔
- 中午，去找咸鱼上的二手冰箱价格
- 晚上，去签合同（准备钱）

今天没有任务，还是自己看组件跟 react 的状态管理

## 2021.10.09 周六

### 生活

晚上回家整理东西，疯狂整理东西，周日搬家。拆桌子，拆置物架，收拾家电（不眠之夜啊）

昨天跟新房东签约了，嗨，以后每个月都要掏 2k+的房租了。

### 工作

todo：

- 早上 新员工入职培训 zoom

今天还是没有安排什么任务，倒是可以继续看看组件。跑跑 mot，看看 demo 跟组件。发现没啥好看的，还不如学学 react 的状态管理。

- 对 mot 系统进行 demo 开发

## 2021.10.10 周日

### 生活

预搬家，必然累死的一天。

呵，结果还好诶，几乎把一半的东西搬过去了，剩下的还差一次的量，而且有表弟帮忙，更快了。

## 2021.10.11 周一

### 生活

表弟上来广州，晚上去恰夜茶。

### 工作

- 熟悉项目代码
- 想研究 rrc-loader-helper
- rudex 学习：概念(完成)->API

rrc-loader-helper 有点难下手呐，后面再想想办法。语雀好像是被屏蔽了，想办法把半成品文章搬下来。现在要学的是 redux 的 API，那我还是学学 redux 好了

2:30 有事儿干了，明天下午 6 点 code review，要好好准备了。

5:50 文件结构已完成，现在写组件备注，草，太容易昏迷了，转去学 react 的状态管理，redux saga 的还没学完呢

## 2021.10.12 周二

晚上开周会，我要准备好开 code review 的准备。

11:30 收到开发任务了，赶紧醒来工作！
13:40 人睡醒，还挺困，建议等下半身冷却下来去买瓶可乐冷静下。靠，感觉还要饮料清醒一下，键盘经常误触。

开发笔记：

1. 如果默认时间为''或没定义，改为 undefined
2. 如果 form 包裹下，用 value1，value2
3. 数据补全

计划，今天/明天完成吧，应该不难。4 点半之后继续看看 demo 有啥好说的

- /outbound/package(已完成)
- /outbound/wellen(已完成)
- /outbound/batch(已完成)

- /outbound/tasks/task(已完成)
- /outbound/tasks/pick-data(已完成)
- /outbound/tasks/pick-detail(已完成)

- /outbound/gathers/gather-sub(已完成)
- /outbound/gathers/gather-master(已完成)

- /outbound/transfer/first(已完成)
- /outbound/transfer/second(已完成)
- /outbound/transfer/detail(已完成)

- /outbound/packChecking/package-detail(已完成)
- /outbound/outShelvesDetail(已完成)
- /combination/weight(已完成)

## 2021.10.13 周三

### 工作

- 完成 OFC-23159
  - 完成剩余 3 个页面
  - 提交到 ft_dev 分支
- 完成 OFC-23272
  - 先完成一个页面试试

14:30 搞定了 OFC-23159，去处理 OFC-23272

- /qms/receipt-management/in-storage-diff
- /qms/receipt-management/difference-order
- /in-warehouse/storage-query/list
- /in-warehouse/storage-query-detail/list

四个页面，今天周三，预计完成 1 个先，试试水

1. wms 系统管理页面统一引入高级搜索和筛选列功能。
2. wms 系统的 UI 组件库统一使用 shineout，移除遗留的 antd 组件库。
3. wms 系统的代码写法统一使用 reducer 写法，将 saga 写法替换成 reducer 写法。

一个页面的处理，包括了

- 去掉 nav/saga
- 引用处理（已完成）
- reducers
- jsx
  - handle(这个是重点，可以往后放放)
  - header
  - list

reducers 写法进行中

思路缓一缓，以模板为底，慢慢将旧功能实现。从 list 开始

## 2021.10.14 周四

能安安静静的上班就挺好，回家又是一场战斗（指搬家），加油，5 点前结束战斗。

todo list:

- /qms/receipt-management/in-storage-diff 入库管理/收货管理/入库差异复议
- /qms/receipt-management/difference-order 入库管理/收货管理/差异订单列表
- /in-warehouse/storage-query/list 入库管理/入仓管理/入库单查询
- /in-warehouse/storage-query-detail/list 入库管理/入仓管理/入库单明细

今天能完成个 in-storage-diff，确认没问题之后，再继续，熟能生巧的工作。

11:30 完成 handle 部分，接下来是 list 部分，再到 header 部分
14:45 完成 list 部分，接下来就是核心 header
16:30 整完了

## 2021.10.15 周五

### 生活

早上 搬家把人累到崩溃，不过总算搬过去了。
晚上 表弟竟然会被坑到培训班去，没想到啊没想到，竟然这么白纸。晚上的时间去解决这事儿了，没有整理

### 工作

入库差异复议， check 一下有没有问题
16:53 入库差异复议做完了，开始做差异订单列表

- server(已完成)
- header
- handle
- list

## 2021.10.16 周六

公费旅游第一天

### 生活

珠海长隆还不错的咧。熬夜从 12 点到 4 点玩狼人杀，我太菜了，而且还高概率狼人，这玄学有点诡异

## 2021.10.17 周日

公费旅游第二天

### 生活

一次不错的团建，回家拼好了置物架和桌子，晚上撑不住，补觉到昏迷。。。
