# redux 学习

## 目标
rrc-loader-helper 做了很多事情，很是神秘，想一探究竟

项目中用到了redux，react-redux，redux
## 概览

- [redux](https://redux.js.org/)
- [redux 中文](https://www.redux.org.cn/)
- [react-redux](https://react-redux.js.org/)
- [react-redux 中文](https://www.redux.org.cn/docs/react-redux/)
- [redux-saga](https://redux-saga-in-chinese.js.org/)
- [redux-saga 中文](https://redux-saga-in-chinese.js.org/)

- [Redux 入门教程（一）：基本用法](https://www.ruanyifeng.com/blog/2016/09/redux_tutorial_part_one_basic_usages.html)
- [Redux 入门教程（二）：中间件与异步操作](https://www.ruanyifeng.com/blog/2016/09/redux_tutorial_part_two_async_operations.html)
- [Redux 入门教程（三）：React-Redux 的用法](https://www.ruanyifeng.com/blog/2016/09/redux_tutorial_part_three_react-redux.html)

## redux

三大原则：

1. 单一数据源
2. state 是只读的
3. 使用纯函数来执行修改

基础：
- Action
- Reducer
- Store

基本概念和API
- Store
  - createStore()
- State
  - store.getState()
- Action
  - Action Creator
  - store.dispatch()
- Reducer
- store.subscribe()

中间件和异步操作：
- applyMiddlewares()
- redux-thunk 中间件
- redux-promise 中间件

react-redux 用法
- connect() 用于从 UI 组件生成容器组件
- mapStateToProps() 
- mapDispatchToProps() 

redux-saga 用法
- 


```javascript
import { connect } from 'react-redux'

const VisibleTodoList = connect(
  mapStateToProps,
  mapDispatchToProps
)(TodoList)
```





