## 本周计划

正式启动面试计划了，到了补短板的时候了，时间还是很急的，周四周五请假去面试

- 广州奥格智能科技（周四 早上）
- 致景科技（周四 下午）
- 广州棒谷（周五 上午）

## 2021.08.23 周一

在约面试的过程中，时间消失了

## 2021.08.24 周二

开始滚轮式复习，先复习一遍，遇到想深入的记下来，抽时间来个专题

专题：

- css 画三角形
- 组件库怎么搭建
- v8 垃圾回收
- js 的事件模型

14:18 继续画脑图~

下班前要把自己写过的《培养指南》过一遍(√)

## 2021.08.25 周三

安排：

- 晚上面试正式开始，所以周三的任务是过题还有过项目
- 晚上回去做 cider 的面试题
- 早上过题，下午看自己的简历项目

过题：

- CSS 系列
- HTTP 系列
- webpack 系列
- JS 系列

优先看自己的短板

13:36 得抓紧时间了，现在才看完 css，还有 http/webpack/JS

15:46 终于看完 webpack 了, 半个小时内看完 http 篇（高速略过，重点是 JS），告诉略过 http 之后，告诉略过自己的项目（重点是 wasm）

18:07 任务基本完成，还差一个"事件"

- Cider(一面 电脑笔试) 过了

## 2021.08.26 周四

- 致景 (一面) 挂了
- 微盛 (一面) 过了

## 2021.08.27 周五

- 棒谷 (全部面) 过了，正在定薪
- 浩鲸 (一面 电话) 过了
- 微盛 (二面，视频面试) 过了

## 2021.08.28 周六

- Cider (二面，视频面试)，结果未知

## 2021.08.29 周日

- 整理，复盘

## 2021.08.30 周一

打算下午面试浩鲸，毕竟是真的远啊。周三再面试天河智慧城那两家（位置在一起，舒服些）

有价格的问题：
- Proxy，怎么代理一个对象？
- React的fiber 
- Vue router 的实现原理(√)
- Vue router 的路由守卫触发顺序(√)
- webpack 的常见配置项
- Vue 2 怎么用typescript
- Vue 2 怎么用JSX
- JS 修饰符的原理是什么

**Vue router 路由守卫**
- 全局路由守卫
  - 全局前置 router.beforeEach
  - 全局解析 router.beforeResolve
  - 全局后置 router.afterEach
- 路由独享守卫
  - 路由配置项 beforeEnter
- 组件内守卫
  - beforeRouterEnater
  - beforeRouterUpdate
  - beforeRouterLeave

顺序 路由组件a->路由组件b 
- 组件a beforeRouteLeave
- 全局 beforeEach
- 组件b 路由配置项 beforeEnter
- 组件b 组件内的守卫 beforeRouteEnter
- 全局 beforeResolve
- 全局 afterEach

## 2021.08.31 周二
最后一天，删掉电脑里面多余的文件，打包带走有价值的东西

- 带走电脑上的有价值东西
- 带走代码
- 删掉自己的信息
