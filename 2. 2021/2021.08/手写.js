// 节流
function debounce(fn, deplay = 200) {
  let timer = null;
  return function () {
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, arguments);
    }, deplay);
  };
}

// 防抖
function throttle(fn, threshhold = 2000) {
  let timer = null;
  let start = new Date();
  return function () {
    let cur = +new Date();
    clearTimeout(timer);
    if (cur - start >= threshhold) {
      fn.apply(this, arguments);
      start = cur;
    } else {
      timer = setTimeout(() => {
        fn.apply(this, arguments);
        timer = null;
      }, threshhold);
    }
  };
}

// call
// 1. call 改变了 this 的指向
// 2. bar 函数执行了
// 3. call 函数还能给定参数执行函数
// 4. this 参数可以传 null，当为 null 的时候，视为指向 window
// 5. 函数是可以有返回值的！
function call(context, ...args) {
  var context = context || window;
  context.fn = this;
  let result = eval(`context.fn(${args})`);
  delete context.fn;
  return result;
}

// apply
// 跟call 就只有参数的区别
function apply(context, arr) {
  var context = context || window;
  context.fn = this;
  let result;

  if (!arr) {
    result = context.fn();
  } else {
    result = eval(`context.fn(${args})`);
  }
  delete context.fn;
  return result;
}

// bind
// 1. 返回函数
// 之所以 return self.apply(context)，是考虑到绑定函数可能是有返回值的
// 2. 传参
// 3. 构造函数效果

function bind(context) {
  var self = this;

  // 获取bind2函数从第二个参数到最后一个参数
  var args = Array.prototype.slice.call(arguments, 1);

  var fNOP = function () {};

  var fBound = function () {
    // 这个时候的arguments是指bind返回的函数传入的参数
    var bindArgs = Array.prototype.slice.call(arguments);
    // 当作为构造函数时，this 指向实例，此时结果为 true，将绑定函数的 this 指向该实例，可以让实例获得来自绑定函数的值
    // 以上面的是 demo 为例，如果改成 `this instanceof fBound ? null : context`，实例只是一个空对象，将 null 改成 this ，实例会具有 habit 属性
    // 当作为普通函数时，this 指向 window，此时结果为 false，将绑定函数的 this 指向 context
    return self.apply(
      this instanceof fNOP ? this : context,
      args.concat(bindArgs)
    );
  };
  fNOP.prototype = this.prototype;
  fBound.prototype = new fNOP();
  return fBound;
}

// new
function mockNew(fn, ...args) {
  const obj = {};

  obj.__proto__ = fn.prototype;

  let result = fn.apply(obj, args);

  return typeof result === "object" ? result : obj;
}

// promise
function promise() {}
