## 本周计划

本周出门！

## 2021.08.16 周一

我应该干嘛，学点啥？typescript, vue 3？因为简历中有写到。

啊，有点烦躁，现在的我还有哪些短板？

Vue 的，JS 基础已经补好了，就只剩 CSS 了。CSS 针对性学一点吧，毕竟本来就不专攻。

整理下要求的事情：

- 补短板（CSS、Vue 3、Webpack、React 等）
- 提离职（还是赶紧提了吧，只剩 2 周了，赶紧）
- 修改简历
- 投简历

## 2021.08.17 周二

10:10 昨天已经提离职了，一身轻松！接下来就是走流程而已，希望月底离职。

剩余调休时间结算，剩余 18.71 小时，可以请 2 天+一个下午，用来面试。下周出去面试，周五肯定是要走流程，那么就还剩下周二下午开始+周三周四，2 天半的高强度面试。

所以还是很忙的，抓紧时间充实自己。

今日时间安排：

- Vue 3 的核心 api（得找找课程）
- CSS（直接找题目来做吧，都不知道有啥可以考）
- 整理下目前对微前端的知识积累

（结果一天下来，大半时间都在忙离职的事情）

### 7.8 月加班结算

7/6 +1:04
7/7 +1:03
7/22 +1:03
7/24(周六) +6.40
7/26 +0:18
7/28 +0:05
7/29 +1:18
7/30 +0:25
7-31-8.01(周六) +10.05
8.02 -3:00

(零碎)+5.26
(周六)+6.40
(周六)+10.05
(调休)-3.00

还剩余 18.71 小时

## 2021.08.18 周三

14:23 早上去忙工作上的事情了，下午继续学习

**typescript**

- 类型
- 枚举 emun
- 类型断言 as
- 泛型 T
- 接口 interface
- 类型别名 type
- interface 和 type 的区别：
  - 扩展，interface 用 extend, type 用&
  - 接口可以自动合并，类型别名不行
  - 能用 interface 就用 interface，不行再用 type，像联合类型，vue 3 的源码中也是这样，type 常用于联合类型

## 2021.08.19 周四

今天的目标是 vue3 源码理解，加油了。下一个目标是 react 源码初探和初步掌握 react hook

先官方文档->再结合 MDN 了解 Proxy 和 Reflect->再看解析

响应式系统的依赖收集看完了，看组合式 API，整理下 api，以及看看文档

## 2021.08.20 周五

### 昨日回响

**vue3 响应式系统**

- 用`Proxy`替换了`Object.defindProperty`来实现响应性，提升了性能
- 依赖收集方面，用`track()`/`trigger()` 取代了` Dep.depend()``Dep.notify() `
- 用副作用(effect)取代了之前的`Watcher`
- 一样有 Dep，不过从类变成了 effect 的 Array

reactive -> createReactiveObject -> baseHandles -> createGetter -> Reflect.get/track
... -> createSetter -> Reflect.set/trigger

10:00 今天主要还是看组合式 api setup
16:51 进入没事改改简历环节

## 2021.08.21 周六

准备简历，投简历

## 2021.08.22 周日
