## 本周计划

本周是查漏补缺的最后一周，下周就要开始出门了，所以要抓紧时间了。

## 2021.08.09 周一

话说人怎么经常这么困呢，看来是睡不够，今天早点睡。

今天的目标，捋清楚 Vue 的运行机制，就这一点。因为这是大纲，也是终点

## 2021.08.10 周二

早上久违的工作了，下午可以学习了。昨天整理了 Vue 的运行机制，算是吧 Vue 的核心原理掌握了，现在把周边 API 学一学。

15:26 突然学起深拷贝，也挺好。

## 2021.08.11 周三

今天周三了！

9:00
先总结下昨天学到的深拷贝

- 首先是**迭代**
- 接着是判断类型，先**判断是否为引用类型**(isObject: typeof 来判断)，不是的话直接返回值，是的话再**判断是否可迭代**(object, array, map, set, typedArray, args 等), (利用 toString 来判断)可迭代的话通过对应构造函数构建新的实例，
- 另外需要针对**循环引用**作处理，利用 weakMap 维护一个数组用来存放拷贝的值，用 weakMap 跟 Map 相比是弱引用，更方便垃圾回收，性能提升
- 针对**函数**的话，lodash 的深拷贝是直接引用的，如果想更进一步的话，先判断箭头函数还是普通函数(箭头函数没有 prototype)，**箭头函数**的话直接 eval 函数的字符串。普通函数，利用正则获取参数和函数本体，再通过 new Fucntion 构建一个 Function 实例
- 进一步优化的话，可以**优化遍历**的方法。从性能上，while 好与 for 好于 for...in...

10:00
深入一些 lodash 的深拷贝是怎么实现的

- Buffer 篇之：ArrayBuffer, TypedArray, DataView

11：00
跟我现在实现的相比，会针对更多的类型进行处理（例如 Buffer 相关的，以及更多的参数来配置）

好了，深拷贝篇可以结束了，接下来该干嘛呢？懂了，先画画思维导图，整理下现在学过的。

14：30
整理了 vue 的，感觉还是缺了点扩展的，看看 Vue.js 技术揭秘补一补，今天下午能补上就差不多了。vue 篇就过了

- event
- keep-alive
- v-model
- slot
- transition
- vuex
- vue router

**event**
说到 event，我对 e.target/e.currentTarget 的理解还比较肤浅诶。见[Vue 事件修饰符 native 和 self](https://juejin.cn/post/6844903885916618759)

- [vue3 移除 v-on.native 修饰符](https://v3.cn.vuejs.org/guide/migration/v-on-native-modifier-removed.html8)

## 2021.08.12 周四

先总结昨天学到的
- event
- v-model
- slot
- keep-alive

**v-model**
- v-model就是一个vue的语法糖。从源码上看，通过给组件实例增加一个prop和一个handle来实现，可以通过model选项来配置prop名和handle event名
- vue 3的v-model跟vue2有两点不兼容，一是prop和event的默认名称变了，二是移除了.sync 修饰符和组件model选项
- React中就没有v-model这种语法糖

**slot**
- 插槽分为普通插槽和作用域插槽，区别在于**作用域**，一个是父组件实例，一个是子组件实例，从源码上看，普通插槽在父组件编译渲染的时候就生成了vnode，而作用域插槽在父组件编译渲染的时候不生成vnode，而是在父组件vnode下保留一个scopedSlots对象，存储着插槽和对应的渲染函数。

**keep-alive**
- <keep-alive> 组件内部有个cache数组来缓存vnode
- 已经缓存的组件不会执行mounted，但是有activated 和 deactivated 两个钩子函数
- <keep-alive> 只处理第一个子元素，所以一般和它搭配使用的有 component 动态组件或者是 router-view

**event**
- 在编译阶段处理指令的时候将事件分为原生事件和自定义事件
- 原生事件的添加删除是调用原生的 addEventListener 和 removeEventListener
- 自定义事件是利用了Vue的事件中心$on,$emit,$off,$once
- 组件节点才能添加自定义事件，并且添加原生事件需要使用native修饰符

10:25 好了，今天继续往下看。

今天的学习重点
- transition
- Vue router
- Vuex

早上告诉略过transition，知道个大概，下午重点攻坚Vue router Vuex

18:00 学完了，但是感觉没怎么记在脑子上

## 2021.08.13 周五
应该是整理JS基础

网络请求之阻塞，css的时候顺便学一学

14:36 今天的核心是JS，早上整理了vue router，下来开始整理学过的JS基础

## 2021.08.14 周六
预计学习typeScript

## 2021.08.15 周日
