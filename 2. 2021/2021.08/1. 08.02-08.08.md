## 本周计划
- Vue 原理
- Vue api
- TS相关
- CSS相关

## 2021.08.02 周一
- 下午才上班呢，看看nextTick 的实现
- 还有整理Vue api，最好结合Vue 3对比

周一学习总结: 
- **nextTick 的实现**，从Promise.then->MutationObserver->setImmediate->setTimeout, Vue 3不考虑兼容性问题，所以直接Promise.then
- 为什么能做到？虽然在**event loop中更新渲染**在微任务之后，但是UI rendering是宏任务，DOM虽然还没有更新，但是浏览器已经计算好了，所以在nextTick中可以获取最新的DOM元素
- **Promise的静态方法**有`all`, `race`, `reslove`, `reject`, `any`, `allSettled`。**Promise的实例方法**有`then`, `catch`, `finally`。实例方法挂在Promise.prototype上，这涉及到js的原型问题。
- 静态方法和实例方法的区别是实例方法需要内部属性？而静态方法不需要，直接传参调用即可。

## 2021.08.03 周二
- Vue api是要好好学一学的。工作可以继续摸鱼度过

今日计划：该去看Vue原理了

## 2021.08.04 周三
剖析Vue内部运行机制
- Vue运行机制全局概览
- 响应式系统的基本原理
- 响应式系统的依赖收集追踪原理
- 实现Virtual DOM下的一个VNode节点
- template模板是怎样通过Compile编译的
- 数据状态更新的差异diff及patch机制
- 批量异步更新策略以及nextTick原理
- Vuex状态管理的工作原理
- 总结

今天的任务是读书，把这部分能说明白就算过关了。可以说是本周的核心目标了。


## 2021.08.05 周四
9:52 昨天读了那个小册，感觉很吃亏，讲了但是没讲深，导致很多东西一知半解。但是它的目录是不错的。

今天的目标是<1> 总结依赖收集追踪和<2> vdom 和 diff，如果这两个目标都完成了，考虑完成昨天目录里提到的内容

16:33 目标完成，接下来结合小册的目录去看还有什么要学的（哦，至少还有Vue API结合源码学习）

Vue.install
Vue.use
是怎么实现的


## 2021.08.06 周五
确定了，不续签，8月末自动离职。所以我更要抓紧时间学习了。今天依旧跟vue大战。试着用自己的话去解释那些概念

今日学习安排：
- vdom和diff的自我总结
- vuex是怎么实现的
- vue router是怎么实现的
- vue 的组件通信
- 常见的vue api原理（nextTick, watch, computed, mixin, computed）
- 指令的本质
- 简单实现一个mvvm模型

16:40 暂时停止摸鱼行为，去工作（本周久违的工作）

## 2021.08.07 周六

## 2021.08.08 周日
