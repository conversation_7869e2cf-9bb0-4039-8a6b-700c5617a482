## 本周工作计划

- JS 基础
  - 闭包（完成）
  - 原型和原型链（完成）
  - 异步（完成）
  - 类型（60%）
  - ES6 特性
- Vue
  - 核心
  - vuex
  - vue router

这周上半周要完成JS基础部份，下半周（周四）要开始Vue。每天完成基本工作，没安排到的工作就少做。抓紧时间学习是正道。

## 2021.07.26 周一

今日主题：异步
- event loop(基本懂了)
- async await 的原理
- generator
- 手写Promise
- 手写Promise.all
- 手写Promise.race
- 手写Promise.allSettled
- 手写Promise.finally

## 2021.07.27 周二

今日主题：类型

早上异步，下午类型，争取明天开始看es6

- 异步要求做到手写实现
- 类型
  - 类型种类
  - 类型判断
  - 类型转换
  - 隐式类型转换

## 2021.07.28 周三

ES6 特性

早上把类型给结了，下午开始看es6

## 2021.07.29 周四

估计还是es6特性，但是应该开坑vue了

9:00 早上光速修复bug，接着继续摸鱼。
10:15 做些布置之后开始摸鱼学习
14:35 es6可以学的东西太多了，见好就收得了，还不如找些面试题确认下考点

## 2021.07.30 周五
开始学习Vue，从问的问题开始学吧

- Vue的生命周期
- $nexttick的底层实现
- 依赖跟踪的底层实现
- vdom和diff算法

Vue 部份看看整理文档

## 2021.07.31 周六

## 2021.08.01 周日
