## 本周工作计划

- 微应用落地方案

## 2021.07.12 周一

- 修bug
- 远程升级（剩余的工作，悄悄地补上）
- 空白页问题（会话过期）

16:29 剩下的bug明天再修，完成功能点更重要

远程升级
- index UI修正(完成)
- massUpgradeDialog UI修正(完成)
- singleUpgradeDialog 补充(完成)
- detail 补充(60%)
  - index 和 detail 之间切换(完成)
  - UI

## 2021.07.13 周二

- 修bug
- 打疫苗

## 2021.07.14 周三

- 布置对象相关修正(已完成)
- 远程升级 UI的剩余工作




近期工作难点
- 后台管理平台 重构
  - 3人小队
  - 集成首页
  - 路由权限控制
  - 子应用
- 青小鹿课堂
  - webpack升级
  - wasm
- ...
  - React

## 2021.07.15 周四

- 点阵笔 开发新功能


9:19 早上完成剩余的远程升级剩余的UI工作，还有点阵笔接口
17:36 光速修bug，剩下的时间才好拿来学习

## 2021.07.16 周五

- 9:11 
没有积压的bug，可以考虑简历上的事情了。
今天工作上真没有什么工作任务
所以目标是改简历，知识体系的构建可以晚一点，为简历服务。

- 11:12 脑子倦了，但是继续狂写，现在能写的东西可比第一版多多了（骄傲），
连不成句子的话，就写写关键字好了
- 13:56 继续写简历，简历上可以写的东西还挺多的。同时，面向简历学习，补齐简历中的短板

## 2021.07.17 周六

## 2021.07.18 周日

