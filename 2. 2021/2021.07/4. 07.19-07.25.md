## 本周工作计划
周一完善简历，周二发文俊

## 2021.07.19 周一

早上：
- 对接志坚的第三方登录
- logout 的bug
- 修理bug

13:30 修简历，差不多了
17:06 简历写完了，该准备打印了，还有面试题准备（先从吃瘪的开始）

## 2021.07.20 周二

9:01 今天很忙，因为明天早上就面试了啊。这天要做的事情非常多。

今日安排
- 光速修复bug(9:00-10:00)
- 面试题思维导图(10:00-12:00)
- 简历思维导图(13:30-14:00)
- 口语化(14:00-14:30)
- 针对项目的问题()

10:18 bug已经修复
15:11 面试题知识点过了一遍了，开始针对简历对思维导图和口语化

## 2021.07.21 周三

早上面试
下午上班

15:34 翻车了，狠抓基础，继续投。基础分为2个方面：js Vue。现在的我热心到不行！我就不信了，我还过不了技术面。

冷静下来，现在先整理知识库，系统性整理，系统性学习，不至于下次翻车，都面试了4次了，套路或多或少都掌握了

针对面试中常问的问题整理专题出来！

17:45 按照大纲进行整理，发现问的问题还真的就是跟大纲的差不多，很是神奇。看来真得好好整理了，下周再投一波

## 2021.07.22 周四

11:28 进入学习模式，今天的目标是JS基础中的闭包

17:36 

- 词法作用域
- 执行上下文栈
- 变量对象
- 作用域链
- this
- 执行上下文
- 闭包

呼，基本上就把js的核心代码给打通了，可怕

明天看原型和复习今天的闭包

还有接下来的异步

## 2021.07.23 周五
9:28 昨天把闭包捋了一遍。等会做下整理，做做题
14:00 闭包相关整理完毕，开始看异步


## 2021.07.24 周六

## 2021.07.25 周日
