# WMS 项目研究

第一个问题，路由：

尝试去理解单个页面的文件结构

- jsx(页面组件)
- me.json(react-redux-component-loader)
- reducers.js(存放 reducers 的)
- server.js
- view.jsx

shein 内部 pageage:

- [react-redux-component-loader](https://www.npmjs.com/package/react-redux-component-loader)
- [rrc-loader-helper](https://npmjs.sheincorp.cn/-/web/detail/rrc-loader-helper)
- [sheinq](https://npmjs.sheincorp.cn/-/web/detail/sheinq)

但路由还是没搞清楚，技术选型：

- react（但版本是 16.2.0，没有 fiber，也没有 react hook）
- redux（状态管理）
- redux-saga（异步管理）

redux-saga 会被逐步淘汰掉，但还是要学一学。
