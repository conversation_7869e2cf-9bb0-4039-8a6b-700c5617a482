# 9 月份 第 5 周

正式入职 shein！入职的第一周，忙起来！

## 本周计划

新人培训计划 第一周：

- 配置环境和软件，阅读开发规范
- 申请和分配需要的系统权限
- clone 并运行 wms、mot 系统
- 对 wms 系统进行 demo 开发，并 code review

新人培训计划 第一周：
- 配置环境和软件，阅读开发规范
- 申请和分配需要的系统权限
- clone 并运行 wms 系统
- 对 wms 系统进行 demo 开发
- 对 业务组件 TableSorter，SearchArea 等进行开发

其他周计划见**新人培训计划**

## 2021.09.27 周一

入职第一天，走人事流程 -> 领新电脑 M1 -> 装软件，搭环境 -> 旁听周会 -> 装软件，搭环境

## 2021.09.28 周二

电脑鼓捣得差不多了，该干正事了：跑项目，熟悉代码

见**安装列表**

### todo

- work：阅读开发规范(差不多，规范不多，看着项目来读更好)
- oa：下半年绩效
- work：运行 wms 系统
- work：运行 mot 系统
- oa：银行卡（明天处理）

5:29 距离下班还有 1 个小时，现在的我有点困。但是有个好消息，wms 系统跑起来了！可喜可贺，接下来就是去看代码了，系统好大啊，光是看看左手边的 nav 我的大脑就触发了保护机制了，可怕。需要摸鱼来恢复精神

草稿：

- 提升自身对仓储业务的理解，能对需求有准确到位的认知，准时完成任务，降低测试阶段的 bug 率
- 提升自身 React 技术栈相关的技术水平，提升工作效率，能对发现的性能瓶颈进行有效优化

### 后日谈

结果昨天加班听业务分享去了，也不错，对业务的理解提升了。见**分享会笔记**

## 2021.09.29 周三

今日任务：

- oa：银行卡（10:00 完成）
- 对 wms 系统进行 demo 开发，并 code review

任务细节：
@郑智彬
1.shineout 基础组件：https://shine.wiki/1.4.x/cn/components/Select#heading-01-multiple
2.shineout 业务组件：https://ue.dev.sheincorp.cn/component/Icon/2.1.8

可以看看公司的组件库
然后昨天拉取的项目 自己拉取一个新分支
之后在路径下 src/component/examples
自己写一个 demo 页面,仿照 demo-search2(这里面封装了许多页面组件,可以看看里面的具体用了什么东西)

14:40 看代码看得有点困，大型项目，人都有点看傻了。得跳出来捋一捋才行，
16:52 准备去问问路由的问题，完全 get 到了，现在就随便看看代码

## 2021.09.30 周四

本周上班最后一天，原则上要干的事情：
- 运行 mot 系统
- 对 wms 系统进行 demo 开发，并 code review
- 对 业务组件SearchArea，TableSorter

todo：

业务组件了解
- SearchArea
- TableSorter
- OssUpload

React

fiber redux-saga generation async/await 之间的关系