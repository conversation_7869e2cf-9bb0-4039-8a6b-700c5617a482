# 页面URL与页面名映射处理脚本 - 使用说明

## 🎉 脚本已成功创建并测试完成！

### 📁 文件清单

在 `temp_06` 文件夹中，我已经为您创建了以下文件：

1. **`page_mapping_processor.py`** - 主处理脚本
2. **`permission_nodes.json`** - 权限节点JSON数据文件（示例）
3. **`create_sample_excel.py`** - 创建示例Excel文件的脚本
4. **`run_demo.py`** - 完整流程演示脚本
5. **`view_result.py`** - 查看处理结果的脚本
6. **`README.md`** - 详细的技术文档
7. **`input_data.xlsx`** - 示例输入Excel文件
8. **`output_data.xlsx`** - 处理后的输出Excel文件

### ✅ 测试结果

脚本已经成功运行并测试通过：

- ✅ 成功加载JSON权限节点数据
- ✅ 构建了247条映射关系
- ✅ 成功处理Excel数据
- ✅ 正确移除了 "/new-pda" 前缀
- ✅ 成功匹配了4条记录：
  - `goods-in-manager` → `NEW-PDA/入库管理`
  - `put-shelves` → `NEW-PDA/入库管理/上架`
  - `put-shelves/putaway-task-new` → `NEW-PDA/入库管理/上架/领取上架任务`
  - `put-shelves/put-away` → `NEW-PDA/入库管理/上架/入库上架`
- ✅ 生成了包含页面名列的新Excel文件

### 🚀 快速开始

#### 方法一：使用您自己的数据

1. **准备您的Excel文件**：
   - 将您的Excel文件重命名为 `input_data.xlsx`
   - 确保第二列是页面别名列

2. **准备您的JSON文件**：
   - 将完整的权限节点JSON数据保存为 `permission_nodes.json`
   - 替换现有的示例文件

3. **运行处理脚本**：
   ```bash
   cd temp_06
   python3 page_mapping_processor.py
   ```

4. **查看结果**：
   - 输出文件：`output_data.xlsx`
   - 在页面别名列后会新增"页面名"列

#### 方法二：先测试示例数据

```bash
cd temp_06

# 创建示例Excel文件
python3 create_sample_excel.py

# 运行处理脚本
python3 page_mapping_processor.py

# 查看结果
python3 view_result.py
```

### 📊 处理逻辑说明

1. **JSON数据处理**：
   - 递归遍历树形结构
   - 只处理 `typeName` 为 "菜单" 的节点
   - 去除 `rule` 字段开头的 "/" 符号
   - **🆕 去除 `rule` 字段中的 "/new-pda" 前缀**
   - 用 "/" 连接各级菜单的 `title` 构建页面名

2. **Excel数据处理**：
   - 读取原始Excel数据
   - 在页面别名列后插入新的"页面名"列
   - 根据映射关系填充页面名
   - 保存为新的Excel文件

3. **映射示例**：
   ```
   原始rule: "/new-pda/goods-in-manager" → 处理后: "goods-in-manager" → 页面名: "NEW-PDA/入库管理"
   原始rule: "/new-pda/put-shelves" → 处理后: "put-shelves" → 页面名: "NEW-PDA/入库管理/上架"
   原始rule: "/new-pda/put-shelves/putaway-task-new" → 处理后: "put-shelves/putaway-task-new" → 页面名: "NEW-PDA/入库管理/上架/领取上架任务"
   ```

4. **特殊处理规则**：
   - 如果 `rule` 是 "/new-pda"，处理后为空字符串
   - 如果 `rule` 以 "/new-pda/" 开头，移除 "new-pda/" 前缀
   - 其他情况只移除开头的 "/" 符号

### 🔧 自定义配置

如果需要修改文件名或其他配置，请编辑 `page_mapping_processor.py` 中的 `main()` 函数：

```python
def main():
    processor = PageMappingProcessor()

    # 修改这些文件路径
    excel_file = "your_excel_file.xlsx"      # 输入Excel文件
    json_file = "your_json_file.json"        # JSON权限节点文件
    output_file = "your_output_file.xlsx"    # 输出Excel文件

    # ... 其余代码保持不变
```

### 📋 输入数据格式要求

#### Excel文件格式：
```
序号  页面别名                      浏览次数(PV)  PV占比  用户数(UV)  UV占比
1     qms/receipt-management/receipt  30925      0.073   356       0.219
2     index                          31856      0.075   1549      0.954
...
```

#### JSON文件格式：
```json
[
  {
    "title": "入库管理",
    "rule": "/qms",
    "typeName": "菜单",
    "children": [
      {
        "title": "收货管理",
        "rule": "/qms/receipt-management",
        "typeName": "菜单",
        "children": [...]
      }
    ]
  }
]
```

### 📈 输出结果格式

处理后的Excel文件会在页面别名列后新增"页面名"列：

```
序号  页面别名                      页面名                    浏览次数(PV)  PV占比  用户数(UV)  UV占比
1     qms/receipt-management/receipt  入库管理/收货管理/收货单管理  30925      0.073   356       0.219
2     index                          [未匹配]                31856      0.075   1549      0.954
...
```

### ❗ 注意事项

1. **依赖库**：确保已安装 `pandas` 和 `openpyxl`
   ```bash
   pip3 install pandas openpyxl
   ```

2. **文件编码**：确保所有文件使用UTF-8编码

3. **文件权限**：确保脚本有读写文件的权限

4. **数据完整性**：确保JSON数据结构完整，包含必要的字段

### 🆘 故障排除

- **"未找到匹配"**：检查页面别名是否与JSON中的rule字段匹配
- **"文件不存在"**：检查文件名和路径是否正确
- **"加载失败"**：检查文件格式和权限

### 📞 技术支持

如果遇到问题，请检查：
1. 控制台输出的详细错误信息
2. 文件格式是否正确
3. 数据是否完整

---

**脚本创建完成！您现在可以使用这个工具来处理您的页面映射数据了。** 🎊
