#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看处理结果的脚本
"""

import pandas as pd

def view_result():
    """查看处理结果"""
    try:
        # 读取输出文件
        df = pd.read_excel('output_data.xlsx')
        
        print("=" * 60)
        print("处理结果预览")
        print("=" * 60)
        
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print(f"列名: {list(df.columns)}")
        
        print("\n详细内容:")
        print("-" * 60)
        
        # 逐行显示，确保中文显示正常
        for index, row in df.iterrows():
            print(f"行 {index + 1}:")
            for col in df.columns:
                print(f"  {col}: {row[col]}")
            print()
        
        print("=" * 60)
        print("处理完成！")
        
    except Exception as e:
        print(f"读取文件失败: {e}")

if __name__ == "__main__":
    view_result()
