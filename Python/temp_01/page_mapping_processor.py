#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
页面URL与页面名映射处理脚本

功能：
1. 读取Excel表格数据（包含页面别名、浏览次数等）
2. 读取JSON权限节点数据（树形结构）
3. 构建页面别名到页面名的映射关系
4. 在Excel数据中新增页面名列
5. 输出处理后的Excel文件

作者：AI Assistant
日期：2025-01-28
"""

import pandas as pd
import json
import os
from typing import Dict, List, Optional


class PageMappingProcessor:
    """页面映射处理器"""
    
    def __init__(self):
        self.rule_to_title_map: Dict[str, str] = {}
    
    def build_rule_title_mapping(self, nodes: List[dict], parent_titles: List[str] = None) -> None:
        """
        递归构建rule到title路径的映射
        
        Args:
            nodes: JSON节点列表
            parent_titles: 父级标题列表
        """
        if parent_titles is None:
            parent_titles = []
        
        for node in nodes:
            # 只处理typeName为"菜单"的节点
            if node.get('typeName') == '菜单':
                title = node.get('title', '')
                rule = node.get('rule', '')
                
                # 去除rule前面的"/"
                if rule.startswith('/'):
                    rule = rule[1:]
                
                # 构建当前节点的完整标题路径
                current_titles = parent_titles + [title]
                title_path = '/'.join(current_titles)
                
                # 添加到映射字典
                if rule:
                    self.rule_to_title_map[rule] = title_path
                
                # 递归处理子节点
                children = node.get('children', [])
                if children:
                    self.build_rule_title_mapping(children, current_titles)
            else:
                # 对于非菜单节点，仍需要递归处理其子节点
                children = node.get('children', [])
                if children:
                    self.build_rule_title_mapping(children, parent_titles)
    
    def load_json_data(self, json_file_path: str) -> List[dict]:
        """
        加载JSON权限节点数据
        
        Args:
            json_file_path: JSON文件路径
            
        Returns:
            JSON数据列表
        """
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"成功加载JSON文件: {json_file_path}")
            return data
        except Exception as e:
            print(f"加载JSON文件失败: {e}")
            return []
    
    def load_excel_data(self, excel_file_path: str) -> pd.DataFrame:
        """
        加载Excel数据
        
        Args:
            excel_file_path: Excel文件路径
            
        Returns:
            DataFrame对象
        """
        try:
            df = pd.read_excel(excel_file_path)
            print(f"成功加载Excel文件: {excel_file_path}")
            print(f"数据行数: {len(df)}")
            print(f"列名: {list(df.columns)}")
            return df
        except Exception as e:
            print(f"加载Excel文件失败: {e}")
            return pd.DataFrame()
    
    def process_excel_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        处理Excel数据，添加页面名列
        
        Args:
            df: 原始DataFrame
            
        Returns:
            处理后的DataFrame
        """
        if df.empty:
            print("Excel数据为空，无法处理")
            return df
        
        # 复制数据框避免修改原始数据
        result_df = df.copy()
        
        # 假设页面别名在第二列（索引为1）
        page_alias_column = df.columns[1] if len(df.columns) > 1 else None
        
        if page_alias_column is None:
            print("未找到页面别名列")
            return result_df
        
        print(f"页面别名列: {page_alias_column}")
        
        # 创建页面名列
        page_names = []
        matched_count = 0
        
        for index, row in df.iterrows():
            page_alias = str(row[page_alias_column]).strip()
            
            # 查找对应的页面名
            page_name = self.rule_to_title_map.get(page_alias, '')
            page_names.append(page_name)
            
            if page_name:
                matched_count += 1
                print(f"匹配成功: {page_alias} -> {page_name}")
            else:
                print(f"未找到匹配: {page_alias}")
        
        # 在页面别名列后插入页面名列
        page_alias_index = df.columns.get_loc(page_alias_column)
        
        # 创建新的列顺序
        new_columns = list(df.columns)
        new_columns.insert(page_alias_index + 1, '页面名')
        
        # 重新构建DataFrame
        result_df = df.copy()
        result_df.insert(page_alias_index + 1, '页面名', page_names)
        
        print(f"处理完成，共匹配 {matched_count}/{len(df)} 条记录")
        return result_df
    
    def save_excel_data(self, df: pd.DataFrame, output_file_path: str) -> bool:
        """
        保存处理后的Excel数据
        
        Args:
            df: 处理后的DataFrame
            output_file_path: 输出文件路径
            
        Returns:
            是否保存成功
        """
        try:
            df.to_excel(output_file_path, index=False)
            print(f"成功保存Excel文件: {output_file_path}")
            return True
        except Exception as e:
            print(f"保存Excel文件失败: {e}")
            return False
    
    def process(self, excel_file_path: str, json_file_path: str, output_file_path: str) -> bool:
        """
        主处理流程
        
        Args:
            excel_file_path: Excel输入文件路径
            json_file_path: JSON权限节点文件路径
            output_file_path: Excel输出文件路径
            
        Returns:
            是否处理成功
        """
        print("开始处理页面映射...")
        
        # 1. 加载JSON数据并构建映射
        json_data = self.load_json_data(json_file_path)
        if not json_data:
            return False
        
        self.build_rule_title_mapping(json_data)
        print(f"构建映射完成，共 {len(self.rule_to_title_map)} 条映射关系")
        
        # 打印部分映射关系用于调试
        print("\n部分映射关系:")
        for i, (rule, title) in enumerate(self.rule_to_title_map.items()):
            if i < 5:  # 只显示前5条
                print(f"  {rule} -> {title}")
        
        # 2. 加载Excel数据
        df = self.load_excel_data(excel_file_path)
        if df.empty:
            return False
        
        # 3. 处理Excel数据
        result_df = self.process_excel_data(df)
        
        # 4. 保存结果
        return self.save_excel_data(result_df, output_file_path)


def main():
    """主函数"""
    processor = PageMappingProcessor()
    
    # 文件路径配置
    excel_file = "input_data.xlsx"  # 输入Excel文件
    json_file = "permission_nodes.json"  # 权限节点JSON文件
    output_file = "output_data.xlsx"  # 输出Excel文件
    
    # 检查文件是否存在
    if not os.path.exists(excel_file):
        print(f"Excel文件不存在: {excel_file}")
        print("请将Excel文件命名为 'input_data.xlsx' 并放在同一目录下")
        return
    
    if not os.path.exists(json_file):
        print(f"JSON文件不存在: {json_file}")
        print("请将JSON文件命名为 'permission_nodes.json' 并放在同一目录下")
        return
    
    # 执行处理
    success = processor.process(excel_file, json_file, output_file)
    
    if success:
        print(f"\n处理完成！输出文件: {output_file}")
    else:
        print("\n处理失败！")


if __name__ == "__main__":
    main()
