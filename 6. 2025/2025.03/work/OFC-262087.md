# OFC-262087 账单切换中台供应商数据一阶段

文档：<https://doc.weixin.qq.com/sheet/e3_AVgAzgZsAEsvhQXHgqXR1Krb5YgaY?scode=APMA2QcuAAkXEGeVK3AZUAagYXAIA&tab=BB08J2>
dcp: <https://dcp.sheincorp.cn/jira/browse/OFC-260023>
prd: <https://ax.sheincorp.cn/unpkg/ax/XYMR4D/newest/start.html#id=soklu6&g=1>

- 应付单管理
- 应付单明细
- 应付日明细
- 应付日计提
- 系统暂估明细
- 盘点明细单
- 子报账单
- 报账单

- [✅] 1 应付单管理 /dwc-manage/bill-management/payable-order-manage
  - [✅] 1.应付单管理-筛选项【服务商】变更名称【服务商简称】，数据源变更：对账项目-服务商简称 前端复用（/std_common/get_service_provider_id_list）
  - [✅] 2.筛选项【子仓】数据源调整：对账项目-服务子仓 前端复用（/std_common/get_three_pl_sub_warehouse_house）
  - [✅] 3.筛选项【业务子类】数据源排查：中台费用项业务子类字典 WBMS_COST_ITEM_BUSINESS_SUB_TYPE
  - [✅] 4.列表字段-【服务商】变更名称【服务商简称】
  - [-] 5.列表数据根据供应商授权区分不同用户展示 后端修改（/charge_payable_order/query_list）
  - [-] 6.列表数据根据创建时间倒序排序 后端修改（/charge_payable_order/query_list）
  - [-] 7.应付单管理支持BBL翻译 后端修改（/charge_payable_order/query_list）
  - [-] 8.状态栏数据展示全部，参考补扣款单 后端修改（/charge_payable_order/query_list）
  - [-] 列表字段取值切换 后端修改（/charge_payable_order/query_list）

- [✅] 2 应付单明细 /dwc-manage/bill-management/payable-order-detail
  - 筛选项
  - [✅] 1.应付单明细-筛选项【服务商】变更名称【服务商简称】，数据源变更：对账项目-服务商简称 前端复用（/std_common/get_service_provider_id_list）
  - [✅] 2.筛选项【子仓】数据源调整：对账项目-服务子仓 前端复用（/std_common/get_three_pl_sub_warehouse_house）
  - [✅] 3.筛选项【仓库】数据源调整：对账项目-服务仓库 前端复用（/std_common/get_warehouse_list）
  - [✅] 4.筛选项【费用项】名称变更为【费用项名称】数据源排查：应付明细表内费用项字段（去重） 前端复用+后端修改（/wbms/front/std_common/get_cost_item_list 前端增加传参menu=1）
  - [✅] 5.新增筛选项【业务子类】数据源：中台费用项业务子类字典 WBMS_COST_ITEM_BUSINESS_SUB_TYPE
  - 列表字段
  - [✅] 6.列表字段-【服务商】变更名称【服务商简称】
  - [✅] 7.列表字段【费用类型】变更名称【支付类型】
  - [✅] 8.列表字段【费用项】变更名称【费用项名称】
  - [✅] 10.列表字段：【暂估金额（CNY）】变更名称【暂估金额】
  - [✅] 11.列表字段：【结算费用（CNY）】变更名称【结算费用】
  - [-] 9.列表数据根据供应商授权区分用户展示 后端修改（/charge_payable_order_detail/query_list）
  - [-] 9.按照创建时间倒序排序 后端修改（/charge_payable_order_detail/query_list）
  - [-] 12.应付单明细支持BBL翻译 后端修改（/charge_payable_order_detail/query_list）
  - [-] 13.状态栏数据展示全部，参考补扣款单 后端修改（/charge_payable_order_detail/query_list）
  - [-] 列表字段取值切换 后端修改（/charge_payable_order_detail/query_list）

- [✅] 3 应付日明细 /dwc-manage/bill-management/charge-payable-daily-detail
  - [✅] 1.筛选条件：【服务商】变更名称【服务商简称】，数据来源变更为"对账项目-服务商简称" 前端复用（/std_common/get_service_provider_id_list）
  - [✅] 2.筛选条件：【子仓】数据来源变更：对账项目-服务子仓 前端复用（/std_common/get_three_pl_sub_warehouse_house）
  - [✅] 3.【费用项名称】数据源核对：3PL应付日明细表内费用项字段（去重） 前端复用+后端修改（/wbms/front/std_common/get_cost_item_list 前端增加传参menu=2）
  - [✅] 4.新增筛选项【业务子类】数据来源：中台费用项业务子类字典 WBMS_COST_ITEM_BUSINESS_SUB_TYPE
  - [✅] 5.列表字段【暂估金额（CNY）】变更名称【暂估金额】
  - [✅] 8.列表字段【费用类型】变更名称【支付类型】
  - [-] 6.列表数据根据供应商授权区分用户展示 后端修改（/charge_payable_daily_detail/query_list）
  - [-] 6.按照创建时间倒序排序 后端修改（/charge_payable_daily_detail/query_list）
  - [-] 7.应付日明细支持BBL翻译 后端修改（/charge_payable_daily_detail/query_list）
  - [-] 9.状态栏数据展示全部，参考补扣款单 后端修改（/charge_payable_daily_detail/query_list）
  - [-] 列表字段取值切换 后端修改（/charge_payable_daily_detail/query_list）

- [✅] 4 应付日计提 /dwc-manage/bill-management/charge-payable-daily-plan
  - [✅] 1.筛选项【服务商简称】数据来源切换为对账项目-服务商简称 前端复用（/std_common/get_service_provider_id_list）
  - [✅] 2.筛选项【业务子类】数据来源切换为中台费用项业务子类字典 WBMS_COST_ITEM_BUSINESS_SUB_TYPE
  - [-] 3.列表数据根据供应商授权区分用户展示 后端修改（/charge_payable_daily_plan/query_list）
  - [-] 3.按照创建时间倒序排序 后端修改（/charge_payable_daily_plan/query_list）
  - [-] 4.应付日计提支持BBL翻译 后端修改（/charge_payable_daily_plan/query_list）
  - [-] 5.状态栏数据展示全部，参考补扣款单 后端修改（/charge_payable_daily_plan/query_list）
  - [-] 列表字段取值切换 后端修改（/charge_payable_daily_plan/query_list）

- [✅] 5 系统暂估明细 /dwc-manage/bill-management/charge-estimate-details
  - [✅] 1.筛选项【服务商】变更名称【服务商简称】数据来源：对账项目-服务商简称 前端复用（/std_common/get_service_provider_id_list）
  - [✅] 2.筛选项【子仓】数据来源：对账项目-服务子仓 前端复用（/std_common/get_three_pl_sub_warehouse_house）
  - [✅] 3.筛选项【仓库】数据来源：对账项目-服务仓库 前端复用（/std_common/get_warehouse_list）
  - [✅] 4.筛选项【费用项】变更名称【费用项名称】数据来源：费用项配置管理-费用项名称 前端复用+后端修改（/wbms/front/std_common/get_cost_item_list 前端增加传参menu=3）
  - [✅] 5.列表字段【服务商】变更名称【服务商简称】
  - [✅] 6.列表字段【费用类型】变更名称【支付类型】
  - [✅] 7.列表字段【费用项】变更名称【费用项名称】
  - [✅] 10.列表字段【暂估费用（CNY）】变更名称【暂估费用】
  - [-] 8.列表数据根据供应商授权区分用户展示 后端修改（/charge_estimate/query_list）
  - [-] 8.按照创建时间倒序排序 后端修改（/charge_estimate/query_list）
  - [-] 9.系统暂估明细支持BBL翻译 后端修改（/charge_estimate/query_list）
  - [-] 列表字段取值切换 后端修改（/charge_estimate/query_list）

- [✅] 6 盘点明细单 /dwc-manage/bill-management/inventory-details
  - [✅] 1.筛选项【费用项】变更名称【费用项名称】，数据来源：盘点明细列表-费用项名称（去重） 前端复用+后端修改（/wbms/front/std_common/get_cost_item_list 传参menu=）
  - [✅] 2.筛选项【服务商】变更名称【服务商简称】，数据来源：对账项目-服务商简称 前端复用（/std_common/get_service_provider_id_list）
  - [✅] 3.筛选项【费用类型】变更名称【支付类型】枚举：应付&扣款，列表增加【支付类型】字段 修改query_list接口
  - [✅] 4.列表字段【服务商】变更名称【服务商简称】，展示服务商简称，原展示供应商名称 修改query_list接口
  - [✅] 5.列表字段【费用项】变更名称【费用项名称】
  - [✅] 6.前端隐藏【结算编码】列
  - [✅] 9.列表字段-【账单类型】更换名称【账期类型】
  - [-] 7.列表数据根据供应商授权区分用户展示，按照创建时间倒序排序 修改query_list接口
  - [-] 8.系统暂估明细支持BBL翻译 修改query_list接口

- [✅] 7 补扣款对账单 /dwc-manage/supplement-deduct-verify-order
  - [✅] 1.筛选条件【服务商】变更名称【服务商简称】，数据来源由"3PL服务商管理"变更为"对账项目-服务商简称" 前端复用（/std_common/get_service_provider_id_list）
  - [✅] 2.筛选条件【子仓】数据来源变更：对账项目-服务子仓 前端复用（/std_common/get_three_pl_sub_warehouse_house）
  - [✅] 3.筛选条件【补扣类型】名称变更为【支付类型】；枚举变更为应付 扣款
  - [✅] 4.筛选条件【费用项名称】数据来源变更："补扣款对账单-费用项名称"去重 前端复用+后端修改（/wbms/front/std_common/get_cost_item_list 传参menu=）
  - [✅] 5.列表字段【服务商】变更名称【服务商简称】
  - [✅] 6.列表字段【补扣类型】变更名称【支付类型】
  - [✅] 8.列表字段，新增账单币种/合同币种 修改query_list接口
  - [✅] 7.结算编码展示调整为供应商账户信息 修改query_list接口
  - [✅] 9.作废触发器，弹出作废弹框没录入作废原因，确定后写入 修改新增接口（修改表结构）
  - [-] 10.列表数据根据供应商授权区分用户展示，按照创建时间倒序排序 修改query_list接口
  - [-] 11.支持BBL翻译 修改query_list接口
  - [-] 2.状态栏数据展示全部，参考补扣款单 修改query_list接口

- [✅] 8 补扣款对账单-新增盘点对账单
  - [✅] 1.新增下拉筛选【服务仓库】；数据来源：对账项目-服务仓库 前端复用（/std_common/get_warehouse_list）
  - [✅] 2.【服务商】名称变更为【服务商简称】；对账项目-服务商简称 前端复用（/std_common/get_service_provider_id_list）
  - [✅] 3.【子仓】数据来源由"服务商管理基础数据"变更为"根据服务商简称-找到对账项目-服务子仓，支持选择下拉数据展示" 前端复用（/std_common/get_three_pl_sub_warehouse_house）
  - [✅] 4.结算编码取值源变更由【服务商管理基础数据】变更为【根据服务商简称-找到对账项目-结算编码，支持修改】 修改query_list接口
  - [-] 5.支持BBL翻译 修改query_list接口

- [✅] 9 补扣款对账单-新增KPI对账单
  - [✅] 1. 新增下拉筛选【服务仓库】；数据来源：对账项目-服务仓库 前端复用（/std_common/get_warehouse_list）
  - [✅] 2.【服务商】名称变更为【服务商简称】；取值源：对账项目-服务商简称 前端复用（/std_common/get_service_provider_id_list）
  - [✅] 3.【子仓】数据来源由"服务商管理基础数据"变更为"根据服务商简称-找到对账项目-服务子仓，支持选择下拉数据展示" 前端复用（/std_common/get_three_pl_sub_warehouse_house）
  - [✅] 4.结算编码取值源变更由【服务商管理基础数据】变更为【根据服务商简称-找到对账项目-结算编码，支持修改】 前端复用（/std_common/add/get_service_provider_cost_info）

- [✅] 10 子报账单 /dwc-manage/sub-bill-management
  - [-] 1.菜单地址名称变更原【仓储费用管理/国内3PL仓储费用管理/ 3PL服务商报账单管理】变更为【全球仓储费用管理中台  / 中台服务商报账单管理】，菜单：【3PL子报账单管理】变更为【服务商子报账单管理】
  - [✅] 2.筛选项-服务商，名称由【服务商】变更为【服务商简称】，数据来源由【服务商管理-服务商名称】变更为【对账项目-服务商简称】 前端复用（/std_common/get_service_provider_id_list）
  - [✅] 3.筛选项-子仓，数据来源由【WMS-子仓管理】变更为【对账项目-服务子仓】 前端复用（/std_common/get_three_pl_sub_warehouse_house）
  - [✅] 4.筛选项-费用项名称，数据来源由【计费规则-相关联费用项】变更为【服务商子报账单管理-费用项名称（去重）】 前端复用+后端修改（/wbms/front/std_common/get_cost_item_list 前端增加传参menu=4）
  - [✅] 5.筛选项-仓库，数据来源由【WMS-仓库管理】变更为【对账项目-服务仓库】 前端复用（/std_common/get_warehouse_list）
  - [✅] 6.筛选条件-报账币种，默认为null，原CNY
  - [✅] 7.筛选条件-补扣类型，名称由【补扣类型】变更为【支付类型】，字典由【补款】变更为【应付】
  - [✅] 13.列表字段-服务商名称变更，由【服务商】变更为【服务商简称】
  - [✅] 14.列表字段-结算费用项名称变更，由【结算费用项】变更为【费用项名称】
  - [✅] 15.列表字段-隐藏【费用项类型】字段
  - [✅] 16.列表字段-报账币种，名称变更由【报账币种】变更为【账单币种/合同币种】
  - [✅] 17.列表字段-补扣类型，名称变更由【补扣类型】变更为【支付类型】，数据取值变更；数据由补款变更为应付
  - [✅] 8.子报账单状态在创建中，待报账，已驳回，财务驳回的状态下作废，其他状态置灰
  - [✅] 9.触发器-撤回修改，子报账单状态在待报账，已驳回，财务驳回的状态下作废，其他状态置灰
  - [✅] 10.页面底部增加金额汇总展示
  - [-] 18.子报账单-补扣款单逆向流程，批量作废&撤回修改，操作后计入操作日志 修改作废&撤回接口
  - [-] 22.补扣款单备注写入子报账单备注 修改新增补扣款单接口/修改提交子报账单接口
  - [-] 11.状态栏由当前页面数据变更为全部数据检索 后端修改（/charge_pay_order_detail/query_list）
  - [-] 19.新计费规则，结算编码弹出展示【供应商账户信息查看】 后端修改（/charge_pay_order_detail/query_list）
  - [-] 20.前端新增创建时间字段进行展示，列表页根据创建时间倒序排列 后端修改（/charge_pay_order_detail/query_list）
  - [-] 20.列表页根据创建时间倒序排列 后端修改（/charge_pay_order_detail/query_list）
  - [-] 21.列表页根据供应商管理授权账户展示对应供应商信息 后端修改（/charge_pay_order_detail/query_list）
  - [-] 列表字段取值切换 后端修改（/charge_pay_order_detail/query_list）

- [✅] 11 报账单 /dwc-manage/main-bill-manage
  - [✅] 1.搜索条件【服务商】变更名称为【服务商简称】，数据来源由WMS-服务商管理变更为对账项目-服务商简称 前端复用（/std_common/get_service_provider_id_list）
  - [✅] 2.搜索条件【子仓】数据来源变更为对账项目-服务子仓 前端复用（/std_common/get_three_pl_sub_warehouse_house）
  - [✅] 3.搜索条件【仓库】数据来源变更为对账项目-服务仓库 前端复用（/std_common/get_warehouse_list）
  - [✅] 4.搜索条件【账单类型】变更名称为【账期类型】；枚举：月结 半月结 周结变更为月结 周结 单次结算
  - [✅] 5.列表字段【服务商】变更名称为【服务商简称】
  - [✅] 6.列表字段【报账币种】变更名称为【账单币种/合同币种】
  - [-] 7.列表数据根据供应商授权区分用户展示 后端修改（/charge_pay_order/query_list）
  - [-] 8.支持BBL翻译 后端修改（/charge_pay_order/query_list）
  - [-] 9.状态栏由当前页面数据变更为全部数据检索 后端修改（/charge_pay_order/query_list）
  - [-] 列表字段取值切换 后端修改（/charge_pay_order/query_list）

## prompt

帮忙改造这个页面src/component/dwc-manage/sub-bill-management , 可以参考 /dwc-manage/bill-management/payable-order-detail 的改动方式

前端复用数据来源接口统一在这里 src/server/wbms/server.js ，reducers.js 要改为引用这里的接口

不要改动src/component/dwc-manage/sub-bill-management/server.js，要改动的是src/component/dwc-manage/sub-bill-management/reducers.js 和src/component/dwc-manage/sub-bill-management/jsx 下的文件

完成下面的需求改动点：

- [] 2.筛选项-服务商，名称由【服务商】变更为【服务商简称】，数据来源由【服务商管理-服务商名称】变更为【对账项目-服务商简称】 前端复用（/std_common/get_service_provider_id_list）
  - [] 3.筛选项-子仓，数据来源由【WMS-子仓管理】变更为【对账项目-服务子仓】 前端复用（/std_common/get_three_pl_sub_warehouse_house）
  - [] 4.筛选项-费用项名称，数据来源由【计费规则-相关联费用项】变更为【服务商子报账单管理-费用项名称（去重）】 前端复用+后端修改（/wbms/front/std_common/get_cost_item_list 前端增加传参menu=4）
  - [] 5.筛选项-仓库，数据来源由【WMS-仓库管理】变更为【对账项目-服务仓库】 前端复用（/std_common/get_warehouse_list）
  - [] 6.筛选条件-报账币种，默认为null，原CNY
  - [] 7.筛选条件-补扣类型，名称由【补扣类型】变更为【支付类型】，字典由【补款】变更为【应付】
  - [] 13.列表字段-服务商名称变更，由【服务商】变更为【服务商简称】
  - [] 14.列表字段-结算费用项名称变更，由【结算费用项】变更为【费用项名称】
  - [] 15.列表字段-隐藏【费用项类型】字段
  - [] 16.列表字段-报账币种，名称变更由【报账币种】变更为【账单币种/合同币种】
  - [] 17.列表字段-补扣类型，名称变更由【补扣类型】变更为【支付类型】，数据取值变更；数据由补款变更为应付

## 数据字典整理

CHARGE_COST_ITEM_DEDUCTION_COST_TYPE ->	WBMS_STANDARD_COST_ITEM_DEDUCTION_COST_TYPE
  - 
ACCOUNT_PERIOD_TYPE -> WBMS_STANDARD_ACCOUNT_PERIOD_TYPE
DictCatType.WBMS_CHARGE_PRICING_TYPE ->	WBMS_STANDARD_PRICE_TYPE
DictCatType.CHARGE_COST_ITEM_BUSINESS_SUB_TYPEE -> WBMS_COST_ITEM_BUSINESS_SUB_TYPE
CHARGE_ONE_COST_ITEM_TYPE -> WBMS_ONE_COST_ITEM_TYPE
CHARGE_TWO_COST_ITEM_TYPE -> WBMS_TWO_COST_ITEM_TYPE

- [✅] 1 应付单管理 /dwc-manage/bill-management/payable-order-manage
- [✅] 2 应付单明细 /dwc-manage/bill-management/payable-order-detail
- [✅] 3 应付日明细 /dwc-manage/bill-management/charge-payable-daily-detail
- [✅] 4 应付日计提 /dwc-manage/bill-management/charge-payable-daily-plan
- [✅] 5 系统暂估明细 /dwc-manage/bill-management/charge-estimate-details
- [] 6 盘点明细单 /dwc-manage/bill-management/inventory-details
  CHARGE_COST_ITEM_DEDUCTION_COST_TYPE
- [✅] 7 补扣款对账单 /dwc-manage/supplement-deduct-verify-order
  CHARGE_COST_ITEM_DEDUCTION_COST_TYPE
- [✅] 10 子报账单 /dwc-manage/sub-bill-management
  CHARGE_COST_ITEM_DEDUCTION_COST_TYPE
- [✅] 11 报账单 /dwc-manage/main-bill-manage 
  ACCOUNT_PERIOD_TYPE -> WBMS_STANDARD_ACCOUNT_PERIOD_TYPE

