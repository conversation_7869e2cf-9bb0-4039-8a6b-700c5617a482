# OFC-263677 沙特退货仓拒收作业模式变更需求

- pc
  - [✅] (1h)入库管理 / 特殊入库管理 / 收货单
    - /inbound/reject-order/list
    - 加筛选条件/列表字段
  - [✅] (1h)其他入库装箱扫描
    - /inbound/other-storage-manage/inbound-packing-scan
    - 加声效
  - [](3h)扫描来源单号
    - /inbound/other-storage-manage/inbound-packing-scan
    -
  - [](1h)退货收包扫描
    - /inbound/reject-order/return-package-scan
    - 等待迁移
- mot
  - [](1h)退货收包扫描
    - /standard-receive-goods/new-return-package-scan
    - 开发完成，待验证

本地调试扫描来源单号的时候，可以切到沙特仓， 来源单号用： THDN25021900400024

在集合whole_package_qc_details里面拿到bar_code 和 字段store_type循环调扫校验码逻辑（按正品扫校验码）

- [] 扫描来源单号
  - [✅] /qc_in_special/scan_source_code
  - scanSourceCodeAPI
  - handleContainerCodeScan
  - 判断正品/次品 whole_package_qc_details.container_quality 商品品质-1 正品，2 非正品（扫描校验码使用）
  - info写入进去了，但箱子的逻辑还没确定
  -
- [] 扫描箱子
  - 扫描正品箱 /qc_in_special/scan_genuine_box_code
  - scanGenuineBoxCodeAPI
  - handleGenuineCodeScan
  <!-- - 扫描次品箱 /qc_in_special/scan_non_genuine_box_code
  - scanNonGenuineBoxCodeAPI
  - handleNonGenuineCodeScan(不用改动) -->
  -
- [] 扫描商品条码(不用改动)
  - handleGoodsScan
  -
- [] 扫描校验码
  - [✅] 基本处理了，除了箱子的逻辑还是没看懂
  - /qc_in_special/scan_check_code
  - scanCheckCodeAPI
  - handleCheckCodeScan

THDN25021900400026
THDN25021900400025

THDN25032700202672