# 接口变更

## 前后端沟通 v1

1. 除了wis入库字典管理页面，有没有其他业务调用到下面wis拿字典的接口，有的话需要切wmd的字典接口

wis旧的字典查询
查询字典分类                    /wis/dict/dictcat/query(保留)
查询字典明细                    /wis/dict/details/query
根据字典分类查询明细     /wis/dict/dictcat/query_details(保留)
查询所属类别                    /wis/dict/dictcat/querybelongcat(✅)
查询单据子类型字典明细    /wis/dict/dictcat/query_dict_sub_type(保留)
查询单据子类型字典明细    /wis/dict/dictcat/query_dict_select(✅)
字典明细select查询          /wis/dict/dict/select/list(✅)

2. 除了wis入库字典管理页面，入参belongCat有值的保持不变，继续走wis字典查询接口

1. 字典页面的查wis字典接口不改
2. 查wis字典带了 belongCat且有值的(belong_cat）的不改，涉及接口
	wis/dict/dictcat/query
	wis/dict/dictcat/query_details
3. /wis/dict/dictcat/query_dict_sub_type 接口不改，之前有硬编码逻辑，继续走wis调用，不切wmd

## 前后端沟通 v2
1. 字典页面的查wis字典接口不改
2. 查wis字典带了 belongCat且有值的(belong_cat）的不改，涉及接口
	wis/dict/dictcat/query
	wis/dict/dictcat/query_details
3. /wis/dict/dictcat/query_dict_sub_type 接口不改，之前有硬编码逻辑，继续走wis调用，不切wmd

wis后端字典接口只保留 
wis/dict/dictcat/query
wis/dict/dictcat/query_details
/wis/dict/dictcat/query_dict_sub_type



## 接口变更

查询字典分类 /wmd/front/dictcat/query
https://soapi.sheincorp.cn/application/5706/routes/post_wmd_front_dictcat_query/doc

查询字典明细 /wmd/front/dict/query
https://soapi.sheincorp.cn/application/5706/routes/post_wmd_front_dict_query/doc

## 改动点
- packages/base/src/component/basic/in-storage-configuration


base 


micro-inbound
- 查询字典分类                    /wis/dict/dictcat/query
  - 2处，但都是belongCat: 4，不改
  - 移除出了公共方法
- 查询字典明细                    /wis/dict/details/query
  - 1个，但跟belongCat: 4有关系，不改
  - 移除出了公共方法
- [✅] 根据字典分类查询明细     /wis/dict/dictcat/query_details
  - 一个，是belongCat: 4，不改
- [✅]查询所属类别                    /wis/dict/dictcat/querybelongcat
  - 无
- 查询单据子类型字典明细    /wis/dict/dictcat/query_dict_sub_type 
  - 不改，是联动接口
- 查询单据子类型字典明细    /wis/dict/dictcat/query_dict_select
  - 无
- 字典明细select查询          /wis/dict/dict/select/list
  - 无


packages/micro-inbound/src/component/inbound/warehouse-packing/reducers.js

micro-outbound

micro-la
