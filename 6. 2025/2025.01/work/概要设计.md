# 概要设计

我今天看了下边拣边分的详设；我发现在后端设计阶段存在严重的问题，针对问题点2个点：
1、拿设计不当回事，现象：所有概设，评审，大多数都是模板，复制抄模板跟本身需求无关；
2、缺少监督审核，负责人对此设计过程不重视，执行随意，可做可不做态度；
3、对组织过程资产，态度不端正，没有严明的纪律；
我对这些不能接受，接下来将会处置，所有后端，甚至前端要重视；对于历史已有过往限期整改（9月份开始的内容）；有意识的同学抓紧自己找时间补起来（涉及10月份前老员工过去这么干的什么时候补齐，什么时候新年前按期准假，要么整改完毕，要么你离开这个团队）———— @Jayer Xu 统计下定计划后端找我逐一检查；我从国内拉TL来检查；

这个晚点我拉下9月份开始到现在上线的需求出来哈，你们该补的补

@所有人 我们当时做这个事项的是，所有的需求都复制同一份模板，可能你的需求会比较简单，所以只写了部分内容，但是模板还在这种在给后面新来的同学看的时候很不友好，改这里的内容，先按照不必要的内容删除这方面来改，然后如果存在遗漏的内容，就是本来你这个需求确实很大，但是里面压根没写，这个东西之前我没有检查过，所以会存在这种情况，这些要补起来

@所有人 这个我看了下，大概涉及70个需求，这两天就把这里改了，限制下时间点，周四下班前改完，周五我统一跟安久那边过下

OFC-216595 PDA功能按照仓库类型进行展示 ✅
OFC-216581	WMS - Block copý function
OFC-216445	【前端需求】「urgent」-PDA弹窗确认后聚焦不升起键盘
OFC-205217	PDA操作页面统一增加扫描条码成功后的提示音【WMS-LA】

amigo zheng 36 
sam huang 35