# 2025 规划

明年规划目标一定要具体，你在这里面做了啥（围绕团队，公司、系统），smart原则，1月15号前组内过一下okr规划；自身不足的改进，实事求是

SMART原则（S=Specific、M=Measurable、A=Attainable、R=Relevant、T=Time-bound）

1. 绩效指标必须是具体的（Specific）
2. 绩效指标必须是可以衡量的（Measurable）
3. 绩效指标必须是可以达到的（Attainable）
4. 绩效指标是要与其他目标具有一定的相关性(Relevant)
5. 绩效指标必须具有明确的截止期限（Time-bound）

## amigo 

### 2024年okr
- O1 保障系统稳定性
  - 8月上旬完成稳定性周报的js错误率监控，10月前完善外部服务源前端侧监控
  - 前端应用对LCP和LCPS的TP75进行跟进提升，LCP全年周平均不高于2.5s,LCPS不高于800ms
  - 保证所负责的各级应用可用性：二级系统99.95%,三级系统99.9% 
  - 拉美前端的A2、A3预案有效率99%
  - A2、A3响应达标率100%
  - 推进建设前端白屏报警监控机制
- O2 提升前端研发质量
  - 測试阶段开发单位人日缺陷低于14%，bug日清率大于等于90%，bug平均修复时效小于6H
  - 生产环境P4/P5及以上故障数量<=1个，故障止损时效MTTR不超过3H
  - 所产生故障待办延期率为0
  - 所负责承接任务方案设计，技术落地返工次数不得超过3次
- O3 提升前端研发时效
  - 前端组涉及的所有业务需求均值不超过10d，研测阶段超15d的需求完成在占比10%以内
  - 前端组涉及的所有日常需求研测阶段超15d完成占比5%以内，延期率控制在1%以内
  - 全年不出现由前端研发导致的不能按时交付
- O4 提升拉美系统安全合规能力
  - 继续推进安全左移，落地不低于少于3项安全防护措施
  - 每月参与安全考试，考试全部通过并在90分以上 
  - 全年不出现高危以上漏洞
  - 所有漏洞平均时效不超过1d；危急，高级不超过0.5d
  - 代码漏洞xcheck上漏洞数量为0
  - 不出现代码泄露，敏感信息泄露，账号共享等行为安全违规问题
- O5 支撑业务可持续发展与系统信息化建设
  - 所有涉及前端的项目用户体验评分不低于3.5分
  - BBL：维护拉美履约BBL管理体系，确保自闭环。包括：维护规范文档，开发辅助工具
  - DCP：维护jira 定时推送，提升团队的时效和项目管理效率
  - Q4完成主动优化用户体验措施不低于2个方面
  - 完成对新人的带教，成长到可以开发日常需求

### 2025年okr

上半年okr

- O1 保障系统稳定性
  - 建立「拉美前端研发治理」指标监控体系，将拉美仓储系统评分达到FTIC指标分数
  - 推进「2025年度 前端架构演进规划」落地完成
- O2 提升前端研发质量
  - 
- O3 提升前端研发时效
  - 增强技能培训和发展：
  - 定期反馈和沟通
- O4 提升拉美系统安全合规能力
  - 继续推进安全左移，落地不低于少于3项安全防护措施
- O5 支撑业务可持续发展与系统信息化建设
  - BBL：推进BBL ，测试质效工具
  - Q4完成主动优化用户体验措施不低于2个方面
  - 维护拉美团队BBL/DCP相关事项，推进事项落地完成
- O6 用户体验优化
  - Q2前完成主动优化用户体验措施不低于X个方面，包括：页面错误提示优化,apm 全链路监控等
  - 推进「拉美仓储架构诉求闭环流程」落地，完成不少于3项的诉求闭环
  - 
- O7 团队建设
  - 带教新人技术成长，提高工作产出：落地不低于2项团队成员技能提升方案
  - 定期反馈和沟通：建立和完善「过程管理」机制，提升团队工作效率
  - 带教新人技术成长，新人能承担值班任务

培养新人能承担值班任务，提升团队整体工作效率

架构演进






















