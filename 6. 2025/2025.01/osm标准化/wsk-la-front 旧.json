[{"_math_route_content_id": "wsk-me-front_8dd11048-89c6-4441-b319-1ee78400be66", "_route_content_id": "wsk-me-front_9bffd319-d7df-4b9a-a2e8-fcc52a013170", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/**.js", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "SHEIN_ABGROUP", "values": "^[0-9]$|^[1-9][0-9]$|^100$"}]}], "priority": 1, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-432", "rule_model": "normal", "type": "http"}, {"_math_route_content_id": "wsk-me-front_9bffd319-d7df-4b9a-a2e8-fcc52a013170", "_route_content_id": "wsk-me-front_8dd11048-89c6-4441-b319-1ee78400be66", "advance_route_fail_strategy": "request_others_service_instances", "detail_type": "normal", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/**.js", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "SHEIN_ABGROUP", "values": "^x$"}]}], "priority": 2, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-433", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wsk-me-front_a9e180fd-5058-435a-b1d5-e187b40132d9", "advance_route_fail_strategy": "request_others_service_instances", "detail_type": "normal", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/**.js", "predicates": [], "priority": 3, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-434", "rule_model": "normal", "type": "http"}, {"_math_route_content_id": "wsk-me-front_854e4463-6337-4abb-93b3-fe6b127aef5c", "_route_content_id": "wsk-me-front_8580cb57-f515-4f49-8e1c-7fa29d6beef4", "advance_route_fail_strategy": "request_others_service_instances", "detail_type": "normal", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "SHEIN_ABGROUP", "values": "^[0-9]$|^[1-9][0-9]$|^100$"}]}], "priority": 4, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-435", "rule_model": "normal", "type": "http"}, {"_math_route_content_id": "wsk-me-front_8580cb57-f515-4f49-8e1c-7fa29d6beef4", "_route_content_id": "wsk-me-front_854e4463-6337-4abb-93b3-fe6b127aef5c", "advance_route_fail_strategy": "request_others_service_instances", "detail_type": "normal", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "SHEIN_ABGROUP", "values": "^x$"}]}], "priority": 5, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-436", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wsk-me-front_163ffd02-e879-4c64-87f8-eb45ab903be2", "advance_route_fail_strategy": "request_others_service_instances", "detail_type": "normal", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/", "predicates": [], "priority": 6, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-437", "rule_model": "normal", "type": "http"}, {"_math_route_content_id": "wsk-me-front_3a7f66bd-0f3d-4390-a4c1-e5d5202aa3ea", "_route_content_id": "wsk-me-front_f3b8d1d4-ebca-4362-ab65-31e6b23e0235", "advance_route_fail_strategy": "request_others_service_instances", "detail_type": "normal", "fail_over_enable": false, "filters": {"addRequestHeaderFilter": {"filterName": "addRequestHeaderFilter", "value": [{"key": "uberctx-blue-green", "values": "blue"}]}}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/**", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "SHEIN_ABGROUP", "values": "^[0-9]$|^[1-9][0-9]$|^100$"}]}], "priority": 7, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "wgw-la-pc", "targetType": "service", "weight": 100}, {"loadbalancer": "LEAST_ACTIVE", "root": false, "sub": 1, "target": "blue_green=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-438", "rule_model": "normal", "type": "http"}, {"_math_route_content_id": "wsk-me-front_f3b8d1d4-ebca-4362-ab65-31e6b23e0235", "_route_content_id": "wsk-me-front_3a7f66bd-0f3d-4390-a4c1-e5d5202aa3ea", "advance_route_fail_strategy": "request_others_service_instances", "detail_type": "normal", "fail_over_enable": false, "filters": {"addRequestHeaderFilter": {"filterName": "addRequestHeaderFilter", "value": [{"key": "uberctx-blue-green", "values": "green"}]}}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/**", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "SHEIN_ABGROUP", "values": "^x$"}]}], "priority": 8, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "wgw-la-pc", "targetType": "service", "weight": 100}, {"loadbalancer": "LEAST_ACTIVE", "root": false, "sub": 1, "target": "blue_green=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-439", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wsk-me-front_3d7420ad-d313-4aff-80e1-d2b9be34dab9", "advance_route_fail_strategy": "request_others_service_instances", "detail_type": "normal", "fail_over_enable": false, "filters": {"addRequestHeaderFilter": {"filterName": "addRequestHeaderFilter", "value": [{"key": "uberctx-blue-green", "values": "green"}]}}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/**", "predicates": [], "priority": 9, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "wgw-la-pc", "targetType": "service", "weight": 100}, {"loadbalancer": "LEAST_ACTIVE", "root": false, "sub": 1, "target": "blue_green=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-440", "rule_model": "normal", "type": "http"}]