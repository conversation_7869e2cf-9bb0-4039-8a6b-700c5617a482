[{"_route_content_id": "wsk-me-front_a9e180fd-5058-435a-b1d5-e187b40132d9", "advance_route_fail_strategy": "request_others_service_instances", "detail_type": "normal", "fail_over_enable": false, "filters": {"addRequestHeaderFilter": {"filterName": "addRequestHeaderFilter", "value": [{"key": "Host", "values": "wsk-la-front.com"}]}}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/**.js", "predicates": [], "priority": 1, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targetType": "service", "weight": 100}, {"loadbalancer": "LEAST_ACTIVE", "root": false, "sub": 1, "target": "gateway_group=browser", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-6182", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wsk-me-front_3f436b37-d278-4db1-974f-14c27bb38965", "advance_route_fail_strategy": "request_others_service_instances", "detail_type": "normal", "fail_over_enable": false, "filters": {"addRequestHeaderFilter": {"filterName": "addRequestHeaderFilter", "value": [{"key": "Host", "values": "wsk-la-front.com"}]}}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/", "predicates": [], "priority": 2, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targetType": "service", "weight": 100}, {"loadbalancer": "LEAST_ACTIVE", "root": false, "sub": 1, "target": "gateway_group=browser", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-6183", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wsk-me-front_bcd77538-852c-464e-b28b-b79a51e75d62", "advance_route_fail_strategy": "request_others_service_instances", "detail_type": "normal", "fail_over_enable": false, "filters": {"addRequestHeaderFilter": {"filterName": "addRequestHeaderFilter", "value": [{"key": "Host", "values": "wsk-la-front.com"}]}}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/**", "predicates": [], "priority": 3, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targetType": "service", "weight": 100}, {"loadbalancer": "LEAST_ACTIVE", "root": false, "sub": 1, "target": "gateway_group=browser", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-6184", "rule_model": "normal", "type": "http"}]