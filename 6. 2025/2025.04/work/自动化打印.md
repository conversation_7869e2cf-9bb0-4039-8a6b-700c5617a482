# 自动化打印


## prompt

参考下面提供的材料，完成WACS箱贴打印模板的开发（提供一个generateSheet函数），可以参考资料1中的。

资料1: 其他模板generateSheet函数的实现
```javascript
export const generateSheet = (info) => {
  /* eslint-disable */
  const defectiveBoxContent = `
        ! 0 200 200 400 1\r\n
        LEFT\r\n
        TEXT 7 0 60 20 退供箱\r\n
        TEXT 7 0 180 20 ${info.isSingleBox}\r\n
        B 128 1 1 60 60 50 ${info.outBoxNo}\r\n
        TEXT 7 0 60 120 ${info.outBoxNo}\r\n
        TEXT 7 0 60 150 供应商：${info.supplierName}\r\n
        TEXT 7 0 60 180 退供原因：${info.defectiveReason}\r\n
        TEXT 7 0 60 210 原始发货单号：${info.sourceDeliveryCode}\r\n
        TEXT 7 0 60 240 发货方式：${info.deliveryTypeDesc}\r\n
        TEXT 7 0 60 270 打包仓库：${info.packageWarehouseName}\r\n
        TEXT 7 0 60 300 打包时间：${info.packageTime}\r\n
        PRINT\r\n
        `;

  /* eslint-disable */
  const valueAddBoxContent = `
        ! 0 200 200 400 1\r\n
        LEFT\r\n
        TEXT 7 0 60 20 增值箱\r\n
        B 128 1 1 80 60 50 ${info.outBoxNo}\r\n
        TEXT 7 0 60 140 ${info.outBoxNo}\r\n
        TEXT 7 0 60 180 供应商：${info.supplierName}\r\n
        TEXT 7 0 60 220 原始发货单号：${info.sourceDeliveryCode}\r\n
        TEXT 7 0 60 260 打包仓库：${info.packageWarehouseName}\r\n
        TEXT 7 0 60 300 打包时间：${info.packageTime}\r\n
        PRINT\r\n
        `;

  /* eslint-disable */

  const genuineBoxContent = `
        ! 0 200 200 400 1\r\n
        LEFT\r\n
        TEXT 7 0 60 10 正品箱\r\n
        TEXT 7 0 180 10 ${info.isSingleBox}\r\n
        TEXT 7 0 250 10 ${info.splitFlagDesc}\r\n
        TEXT 7 0 60 50 原始发货单号：${info.sourceDeliveryCode}\r\n
        TEXT 7 0 60 90 原发箱唛：${info.printSourceBoxCode} ${info.printSourceBoxCodeText} \r\n
        TEXT 7 0 60 130 发货方式：${info.deliveryTypeDesc}\r\n
        TEXT 7 0 60 170 运单号：${info.expressNo}\r\n
        B 128 1 1 80 60 210 ${info.outBoxNo}\r\n
        TEXT 7 0 60 300 ${info.outBoxNo}\r\n
        FORM\r\n
        PRINT\r\n
        `

  /* eslint-disable */
  // 如果没有运单号的话就不展示对应字段及条码
  const genuineNoExpressBoxContent = `
        ! 0 200 200 400 1\r\n
        LEFT\r\n
        TEXT 7 0 60 10 正品箱\r\n
        TEXT 7 0 180 10 ${info.isSingleBox}\r\n
        TEXT 7 0 250 10 ${info.splitFlagDesc}\r\n
        TEXT 7 0 60 50 原始发货单号：${info.sourceDeliveryCode}\r\n
        TEXT 7 0 60 90 原发箱唛：${info.printSourceBoxCode} ${info.printSourceBoxCodeText}\r\n
        TEXT 7 0 60 130 发货方式：${info.deliveryTypeDesc}\r\n
        B 128 1 1 80 60 210 ${info.outBoxNo}\r\n
        TEXT 7 0 60 300 ${info.outBoxNo}\r\n
        FORM\r\n
        PRINT\r\n
        `
  // 75 * 130 非佛山仓
  const returnBoxContentTpl1 = `
          ! 0 200 200 800 1
    LEFT
    TEXT 4 0 60 20 ${info.outBoxTypeName}
    TEXT 4 0 440 20 ${info.isSingleBox}
    B 128 1 1 80 60 70 ${info.outBoxNo}
    TEXT 4 0 60 160 ${info.outBoxNo}
    TEXT 4 3 440 160 ${info.deliveryWarehouseType}
    BOX 50 220 600 620 1
    LINE 50 275 600 275 1
    LINE 50 330 600 330 1
    LINE 50 385 600 385 1
    LINE 50 440 600 440 1
    LINE 50 495 600 495 1
    LINE 50 550 600 550 1
    LINE 250 220 250 620 1
    LINE 425 440 425 620 1
    
    TEXT 4 0 65 240 FACTORY:
    TEXT 4 0 260 240 ${info.supplierName}
    TEXT 4 0 65 285 ORIGIN:
    TEXT 4 0 260 285 ${info.originName}
    TEXT 4 0 65 340 P.O:
    TEXT 4 0 260 340 ${info.purchaseCode}
    TEXT 4 0 65 395 STYLE NO:
    TEXT 4 0 260 395 ${info.styleNoStr}
    TEXT 4 0 65 450 QTY:
    TEXT 4 0 260 450 ${info.goodsCount}
    TEXT 4 0 435 450 PCS
    TEXT 4 0 65 505 G.W:
    TEXT 4 0 260 505 ${info.boxWeight}
    TEXT 4 0 435 505 KGS
    TEXT 4 0 65 560 N.W:
    TEXT 4 0 260 560 ${info.netWeight}
    TEXT 4 0 435 560 KGS
    TEXT 4 0 60 640 Packing Time ${info.packageTime}
            FORM\r\n
            PRINT\r\n
            `;
  // 100*100 非佛山仓
  const returnBoxContentTpl2 = `
          ! 0 200 200 800 1
    LEFT
    TEXT 4 0 60 20 ${info.outBoxTypeName}
    TEXT 4 0 600 20 ${info.isSingleBox}
    B 128 1 1 80 60 70 ${info.outBoxNo}
    TEXT 4 0 60 160 ${info.outBoxNo}
    TEXT 4 3 600 140 ${info.deliveryWarehouseType}
    BOX 50 220 700 620 1
    LINE 50 275 700 275 1
    LINE 50 330 700 330 1
    LINE 50 385 700 385 1
    LINE 50 440 700 440 1
    LINE 50 495 700 495 1
    LINE 50 550 700 550 1
    LINE 280 220 280 620 1
    LINE 500 440 500 620 1
    
    TEXT 4 0 65 240 FACTORY:
    TEXT 4 0 290 230 ${info.supplierName}
    TEXT 4 0 65 285 ORIGIN:
    TEXT 4 0 290 285 ${info.originName}
    TEXT 4 0 65 340 P.O:
    TEXT 4 0 290 340 ${info.purchaseCode}
    TEXT 4 0 65 395 STYLE NO:
    TEXT 4 0 290 395 ${info.styleNoStr}
    TEXT 4 0 65 450 QTY:
    TEXT 4 0 290 450 ${info.goodsCount}
    TEXT 4 0 510 450 PCS
    TEXT 4 0 65 505 G.W:
    TEXT 4 0 290 505 ${info.boxWeight}
    TEXT 4 0 510 505 KGS
    TEXT 4 0 65 560 N.W:
    TEXT 4 0 290 560 ${info.netWeight}
    TEXT 4 0 510 560 KGS
    TEXT 4 0 60 640 Packing Time ${info.packageTime}
            FORM\r\n
            PRINT\r\n
            `;
  // 佛山仓
  if (info.displayType === 1) {
    if (info?.outBoxType === GENUINE_BOX) {
      // 打印机即使没有运单号也会生成一个二维码 所以此处需要区分一下正品箱对应的打印模板
      return info.expressNo ? genuineBoxContent : genuineNoExpressBoxContent
    } else if (info?.outBoxType === DEFECTIVE_BOX) {
      return defectiveBoxContent
    } else {
      return valueAddBoxContent
    }
  } else {
    // 非佛山仓
    // printSpecType 打印规格 '1': 100*100 '2': 75*130 '3': 75*50
    if (info?.printSpecType === '1') {
      // 100 * 100
      return returnBoxContentTpl2
    } else {
      // 75 * 130
      return returnBoxContentTpl1
    }
  }
}
```

资料2：CPCL编程手册（见附件）

资料3: prd 页面的html
```html
<body>﻿

  
    <title>WACS箱贴打印</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <link href="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/css/axure_rp_page.css" type="text/css" rel="stylesheet">
    <link href="/unpkg/ax/TRPE1R/1550/data/styles.css" type="text/css" rel="stylesheet">
    <link href="/unpkg/ax/TRPE1R/1550/files/wacs箱贴打印_ltcpeo/styles.css" type="text/css" rel="stylesheet">
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/axQuery.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/globals.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axutils.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/annotation.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/axQuery.std.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/doc.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/messagecenter.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/events.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/recording.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/action.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/expr.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/geometry.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/flyout.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/model.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/repeater.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/sto.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/utils.temp.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/variables.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/drag.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/move.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/visibility.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/style.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/adaptive.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/tree.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/init.temp.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/legacy.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/viewer.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/math.js"></script>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="/unpkg/ax/TRPE1R/1550/data/document.js"></script>
    <script src="/unpkg/ax/TRPE1R/1550/files/wacs箱贴打印_ltcpeo/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  
  <div id="meiliTitle" style="display:none"><h1>WCS系统基线文档</h1><h2>WACS箱贴打印</h2></div>
    <div id="base" class="">

      <!-- Unnamed (Rectangle) -->
      <div id="u3735" class="ax_default paragraph">
        <div id="u3735_div" class=""></div>
        <div id="u3735_text" class="text ">
          <p><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;">打印纸尺寸：</span><span style="font-family:'Arial Normal', 'Arial', sans-serif;">100*160 cm</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3736" class="ax_default box_1">
        <div id="u3736_div" class=""></div>
        <div id="u3736_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3737" class="ax_default paragraph">
        <div id="u3737_div" class=""></div>
        <div id="u3737_text" class="text ">
          <p><span>分拣批次号：</span></p><p><span><br></span></p><p><span>滑槽编号：</span></p><p><span><br></span></p><p><span>容器号：</span></p><p><span><br></span></p><p><span>系统记录包裹数量：</span></p><p><span><br></span></p><p><span>关箱时间：</span></p><p><span><br></span></p><p><span>关箱操作员：</span></p><p><span><br></span></p><p><span>打印原因：</span></p><p><span><br></span></p><p><span>渠道代码：</span></p><p><span><br></span></p><p><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (Vertical Line) -->
      <div id="u3738" class="ax_default line1">
        <img id="u3738_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3738.svg">
        <div id="u3738_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3739" class="ax_default heading_3">
        <div id="u3739_div" class=""></div>
        <div id="u3739_text" class="text ">
          <p><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;font-weight:700;">NOTICE</span><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;font-weight:650;">！！！</span></p><p><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;font-weight:700;"><br></span></p><p><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;font-weight:700;">&nbsp;</span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u3740" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u3741" class="ax_default table_cell2">
          <img id="u3741_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3741.png">
          <div id="u3741_text" class="text ">
            <p><span>序号</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3742" class="ax_default table_cell2">
          <img id="u3742_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3742.png">
          <div id="u3742_text" class="text ">
            <p><span>字段名称</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3743" class="ax_default table_cell2">
          <img id="u3743_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3743.png">
          <div id="u3743_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3744" class="ax_default table_cell2">
          <img id="u3744_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3744.png">
          <div id="u3744_text" class="text ">
            <p><span>取数来源（数据触发生成、数据触发修改等）</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3745" class="ax_default table_cell2">
          <img id="u3745_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3745.png">
          <div id="u3745_text" class="text ">
            <p><span>数据说明（小数位数/计算逻辑/是否需要支持正倒序排列等）</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3746" class="ax_default table_cell2">
          <img id="u3746_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3746.png">
          <div id="u3746_text" class="text ">
            <p><span>备注</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3747" class="ax_default table_cell2">
          <img id="u3747_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3747.png">
          <div id="u3747_text" class="text ">
            <p><span>数据示例</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3748" class="ax_default table_cell2">
          <img id="u3748_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3748.png">
          <div id="u3748_text" class="text ">
            <p><span>1</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3749" class="ax_default table_cell2">
          <img id="u3749_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3749.png">
          <div id="u3749_text" class="text ">
            <p><span>标题</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3750" class="ax_default table_cell2">
          <img id="u3750_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3750.png">
          <div id="u3750_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3751" class="ax_default table_cell1">
          <img id="u3751_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3751.png">
          <div id="u3751_text" class="text ">
            <p style="font-size:13px;"><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;font-weight:400;color:#000000;">固定值：</span></p><p style="font-size:18px;"><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;font-weight:700;">NOTICE</span><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;font-weight:650;">！！！</span></p><p style="font-size:18px;"><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;font-weight:650;">This container's packages need to be scanned individually</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3752" class="ax_default table_cell2">
          <img id="u3752_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3752.png">
          <div id="u3752_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3753" class="ax_default table_cell2">
          <img id="u3753_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3753.png">
          <div id="u3753_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3754" class="ax_default table_cell2">
          <img id="u3754_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3754.png">
          <div id="u3754_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3755" class="ax_default table_cell2">
          <img id="u3755_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3755.png">
          <div id="u3755_text" class="text ">
            <p><span>2</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3756" class="ax_default table_cell2">
          <img id="u3756_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3756.png">
          <div id="u3756_text" class="text ">
            <p><span>分拣批次号</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3757" class="ax_default table_cell2">
          <img id="u3757_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3757.png">
          <div id="u3757_text" class="text ">
            <p><span>sort_batch</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3758" class="ax_default table_cell1">
          <img id="u3758_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3758.png">
          <div id="u3758_text" class="text ">
            <p><span>PDA滑槽绑定容器、接口入参绑定时写入</span></p><p><span>&lt;滑槽容器绑定表&gt;</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3759" class="ax_default table_cell2">
          <img id="u3759_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3759.png">
          <div id="u3759_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3760" class="ax_default table_cell2">
          <img id="u3760_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3760.png">
          <div id="u3760_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3761" class="ax_default table_cell2">
          <img id="u3761_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3761.png">
          <div id="u3761_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3762" class="ax_default table_cell2">
          <img id="u3762_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3762.png">
          <div id="u3762_text" class="text ">
            <p><span>3</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3763" class="ax_default table_cell1">
          <img id="u3763_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3763.png">
          <div id="u3763_text" class="text ">
            <p><span>滑槽编号</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3764" class="ax_default table_cell1">
          <img id="u3764_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3764.png">
          <div id="u3764_text" class="text ">
            <p><span>chute_code</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3765" class="ax_default table_cell1">
          <img id="u3765_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3765.png">
          <div id="u3765_text" class="text ">
            <p><span>PDA滑槽绑定容器、接口入参绑定时写入</span></p><p><span>&lt;滑槽容器绑定表&gt;</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3766" class="ax_default table_cell1">
          <img id="u3766_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3766.png">
          <div id="u3766_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3767" class="ax_default table_cell1">
          <img id="u3767_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3767.png">
          <div id="u3767_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3768" class="ax_default table_cell1">
          <img id="u3768_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3768.png">
          <div id="u3768_text" class="text ">
            <p><span>GK05-1001</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3769" class="ax_default table_cell2">
          <img id="u3769_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3769.png">
          <div id="u3769_text" class="text ">
            <p><span>4</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3770" class="ax_default table_cell1">
          <img id="u3770_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3770.png">
          <div id="u3770_text" class="text ">
            <p><span>容器号</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3771" class="ax_default table_cell1">
          <img id="u3771_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3771.png">
          <div id="u3771_text" class="text ">
            <p><span>Container_code</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3772" class="ax_default table_cell1">
          <img id="u3772_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3772.png">
          <div id="u3772_text" class="text ">
            <p><span>PDA滑槽绑定容器、接口入参绑定时写入</span></p><p><span>&lt;滑槽容器绑定表&gt;</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3773" class="ax_default table_cell1">
          <img id="u3773_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3773.png">
          <div id="u3773_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3774" class="ax_default table_cell1">
          <img id="u3774_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3774.png">
          <div id="u3774_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3775" class="ax_default table_cell1">
          <img id="u3775_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3775.png">
          <div id="u3775_text" class="text ">
            <p><span>112234345（自动生成）</span></p><p><span>T00021</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3776" class="ax_default table_cell2">
          <img id="u3776_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3769.png">
          <div id="u3776_text" class="text ">
            <p><span>5</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3777" class="ax_default table_cell1">
          <img id="u3777_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3770.png">
          <div id="u3777_text" class="text ">
            <p><span>数量</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3778" class="ax_default table_cell1">
          <img id="u3778_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3778.png">
          <div id="u3778_text" class="text ">
            <p><span>PKG</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3779" class="ax_default table_cell1">
          <img id="u3779_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3772.png">
          <div id="u3779_text" class="text ">
            <p><span>&lt;滑槽容器绑定表&gt;&lt;分拣任务表&gt;该分拣批次对应的包裹数量</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3780" class="ax_default table_cell1">
          <img id="u3780_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3773.png">
          <div id="u3780_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3781" class="ax_default table_cell1">
          <img id="u3781_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3774.png">
          <div id="u3781_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3782" class="ax_default table_cell1">
          <img id="u3782_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3775.png">
          <div id="u3782_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3783" class="ax_default table_cell2">
          <img id="u3783_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3783.png">
          <div id="u3783_text" class="text ">
            <p><span>6</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3784" class="ax_default table_cell1">
          <img id="u3784_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3784.png">
          <div id="u3784_text" class="text ">
            <p><span>关箱时间</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3785" class="ax_default table_cell1">
          <img id="u3785_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3785.png">
          <div id="u3785_text" class="text ">
            <p><span>Box closing time</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3786" class="ax_default table_cell1">
          <img id="u3786_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3786.png">
          <div id="u3786_text" class="text ">
            <p><span>PDA滑槽解绑容器、接口入参解绑时写入</span></p><p><span>&lt;滑槽容器绑定表&gt;</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3787" class="ax_default table_cell1">
          <img id="u3787_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3787.png">
          <div id="u3787_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3788" class="ax_default table_cell1">
          <img id="u3788_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3788.png">
          <div id="u3788_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3789" class="ax_default table_cell1">
          <img id="u3789_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3789.png">
          <div id="u3789_text" class="text ">
            <p><span>2024-04-12 02:55:00</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3790" class="ax_default table_cell2">
          <img id="u3790_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3783.png">
          <div id="u3790_text" class="text ">
            <p><span>7</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3791" class="ax_default table_cell1">
          <img id="u3791_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3784.png">
          <div id="u3791_text" class="text ">
            <p><span>关箱操作员</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3792" class="ax_default table_cell1">
          <img id="u3792_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3785.png">
          <div id="u3792_text" class="text ">
            <p><span>Operator</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3793" class="ax_default table_cell1">
          <img id="u3793_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3786.png">
          <div id="u3793_text" class="text ">
            <p><span>解绑操作对应的PDA系统登录员</span></p><p><span>&lt;滑槽容器绑定表&gt;</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3794" class="ax_default table_cell1">
          <img id="u3794_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3787.png">
          <div id="u3794_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3795" class="ax_default table_cell1">
          <img id="u3795_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3788.png">
          <div id="u3795_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3796" class="ax_default table_cell1">
          <img id="u3796_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3789.png">
          <div id="u3796_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3797" class="ax_default table_cell2">
          <img id="u3797_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3797.png">
          <div id="u3797_text" class="text ">
            <p><span>8</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3798" class="ax_default table_cell1">
          <img id="u3798_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3798.png">
          <div id="u3798_text" class="text ">
            <p><span>打印原因</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3799" class="ax_default table_cell1">
          <img id="u3799_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3799.png">
          <div id="u3799_text" class="text ">
            <p><span>Print reason</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3800" class="ax_default table_cell1">
          <img id="u3800_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3800.png">
          <div id="u3800_text" class="text ">
            <p><span>中文&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 英文</span></p><p><span>1、系统配置WACS打印&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; 1、System configured for WACS printing</span></p><p><span>2、容器内存在非物流面单包裹&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; 2、The container contains packages with non-compliant shipping labels.</span></p><p><span>3、TMS打印失败</span><span style="text-decoration:line-through ;">（error_type:error info）</span><span>&nbsp;&nbsp; 3、TMS printing failed </span></p><p><span>4、TMS接口超时&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; 4、TMS interface timeout</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3801" class="ax_default table_cell1">
          <img id="u3801_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3801.png">
          <div id="u3801_text" class="text ">
            <p><span>error_type、error info对应值从出参接口对应获取</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3802" class="ax_default table_cell1">
          <img id="u3802_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3802.png">
          <div id="u3802_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3803" class="ax_default table_cell1">
          <img id="u3803_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3803.png">
          <div id="u3803_text" class="text ">
            <p><span>1、系统配置WACS打印&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; 1、System configured for WACS printing</span></p><p><span>2、存在非物流面单包裹&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2、TMS printing failed</span></p><p><span>3、TMS打印失败&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; 3、TMS printing failed</span></p><p><span>4、TMS接口超时&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; 4、TMS interface timeout</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3804" class="ax_default table_cell2">
          <img id="u3804_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3804.png">
          <div id="u3804_text" class="text ">
            <p><span>9</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3805" class="ax_default table_cell1">
          <img id="u3805_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3805.png">
          <div id="u3805_text" class="text ">
            <p><span>渠道</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3806" class="ax_default table_cell1">
          <img id="u3806_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3806.png">
          <div id="u3806_text" class="text ">
            <p><span>No. of channel</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3807" class="ax_default table_cell1">
          <img id="u3807_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3807.png">
          <div id="u3807_text" class="text ">
            <p><span>该箱包裹对应渠道，超过2个，只随机显示2个，省略号显示</span></p><p><span><br></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3808" class="ax_default table_cell1">
          <img id="u3808_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3808.png">
          <div id="u3808_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3809" class="ax_default table_cell1">
          <img id="u3809_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3809.png">
          <div id="u3809_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3810" class="ax_default table_cell1">
          <img id="u3810_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3810.png">
          <div id="u3810_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3811" class="ax_default _二级标题1">
        <div id="u3811_div" class=""></div>
        <div id="u3811_text" class="text ">
          <p><span style="font-family:'Arial Normal', 'Arial', sans-serif;">3. WACS</span><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;">箱贴字段定义与描述</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3812" class="ax_default paragraph">
        <div id="u3812_div" class=""></div>
        <div id="u3812_text" class="text ">
          <p><span>支持国际化语言</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3813" class="ax_default _二级标题1">
        <div id="u3813_div" class=""></div>
        <div id="u3813_text" class="text ">
          <p><span style="font-family:'Arial Normal', 'Arial', sans-serif;">1. WACS</span><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;">箱贴打印流程图</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3814" class="ax_default _二级标题1">
        <div id="u3814_div" class=""></div>
        <div id="u3814_text" class="text ">
          <p><span style="font-family:'Arial Normal', 'Arial', sans-serif;">2. WACS</span><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;">箱贴打印模板</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3815" class="ax_default label1">
        <div id="u3815_div" class=""></div>
        <div id="u3815_text" class="text ">
          <p><span>1、下发失败</span><span style="text-decoration:line-through ;">-附带原因</span></p><p><span>2、下发超时</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3816" class="ax_default box_1">
        <div id="u3816_div" class=""></div>
        <div id="u3816_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3817" class="ax_default paragraph">
        <div id="u3817_div" class=""></div>
        <div id="u3817_text" class="text ">
          <p><span>sort_batch：MM202504080000</span></p><p><span><br></span></p><p><span>chute_code：GK03-1002</span></p><p><span><br></span></p><p><span>Container_code：123455555</span></p><p><span><br></span></p><p><span>PKG：55</span></p><p><span><br></span></p><p><span>Box closing time：2025-04-08 10:29:21</span></p><p><span><br></span></p><p><span>Operator：Anna</span></p><p><span><br></span></p><p><span>Print reason：</span><span style="color:#000000;">TMS interface timeout</span></p><p><span><br></span></p><p><span>No. of channel：</span></p><p><span>UHeYFJ-LHR3-HSS-PB-CBN-aL-Zpen</span></p><p><span>…</span></p>
        </div>
      </div>

      <!-- Unnamed (Vertical Line) -->
      <div id="u3818" class="ax_default line1">
        <img id="u3818_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3738.svg">
        <div id="u3818_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3819" class="ax_default heading_3">
        <div id="u3819_div" class=""></div>
        <div id="u3819_text" class="text ">
          <p style="font-size:18px;"><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;font-weight:700;font-size:22px;">NOTICE</span><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;font-weight:650;font-size:22px;">！！！</span></p><p style="font-size:18px;"><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;font-weight:650;">This container's packages need to be scanned individually</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3820" class="ax_default paragraph">
        <div id="u3820_div" class=""></div>
        <div id="u3820_text" class="text ">
          <p><span>样例：字段以系统为准</span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u3821" class="ax_default">

        <!-- Unnamed (Table Cell) -->
        <div id="u3822" class="ax_default table_cell1">
          <img id="u3822_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3822.png">
          <div id="u3822_text" class="text ">
            <p><span>修订版本</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3823" class="ax_default table_cell1">
          <img id="u3823_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3823.png">
          <div id="u3823_text" class="text ">
            <p><span>需求编号</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3824" class="ax_default table_cell1">
          <img id="u3824_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3824.png">
          <div id="u3824_text" class="text ">
            <p><span>修改内容</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3825" class="ax_default table_cell1">
          <img id="u3825_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3825.png">
          <div id="u3825_text" class="text ">
            <p><span>修订类型</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3826" class="ax_default table_cell1">
          <img id="u3826_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3826.png">
          <div id="u3826_text" class="text ">
            <p><span>修订人</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3827" class="ax_default table_cell1">
          <img id="u3827_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3827.png">
          <div id="u3827_text" class="text ">
            <p><span>时间</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3828" class="ax_default table_cell1">
          <img id="u3828_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3828.png">
          <div id="u3828_text" class="text ">
            <p><span>V1.0.0</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3829" class="ax_default table_cell1">
          <img id="u3829_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3829.png">
          <div id="u3829_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3830" class="ax_default table_cell1">
          <img id="u3830_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3830.png">
          <div id="u3830_text" class="text ">
            <p><span>新增</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3831" class="ax_default table_cell1">
          <img id="u3831_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3831.png">
          <div id="u3831_text" class="text ">
            <p><span>创建</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3832" class="ax_default table_cell1">
          <img id="u3832_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3832.png">
          <div id="u3832_text" class="text ">
            <p><span>Louie Yang</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3833" class="ax_default table_cell1">
          <img id="u3833_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3833.png">
          <div id="u3833_text" class="text ">
            <p><span>2025-04-08</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3834" class="ax_default _形状1">
        <div id="u3834_div" class=""></div>
        <div id="u3834_text" class="text ">
          <p><span>版本改动记录</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3835" class="ax_default _形状1">
        <div id="u3835_div" class=""></div>
        <div id="u3835_text" class="text ">
          <p><span>背景说明</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3836" class="ax_default paragraph">
        <div id="u3836_div" class=""></div>
        <div id="u3836_text" class="text ">
          <p><span>波兰大P3项目-包裹分拣，在关箱后，根据系统配置，会有以下场景触发WACS箱贴打印：</span></p><p><span><br></span></p><p><span>1、系统配置WACS打印关箱箱贴</span></p><p><span>2、系统配置TMS打印关箱箱贴，但是箱包裹明细存在非物流面单包裹</span></p><p><span>3、系统配置TMS打印关箱箱贴，但TMS无法给出箱贴</span><span style="text-decoration:line-through ;">（附带原因）</span></p><p><span>4、系统配置TMS打印关箱箱贴，但TMS超时未给出箱贴</span></p><p><span><br></span></p><p><span>在上述场景，打印出箱贴，后续现场会转移对应箱子，进行后续逐件包裹预发货扫描处理</span></p>
        </div>
      </div>

      <!-- Unnamed (Vertical Line) -->
      <div id="u3837" class="ax_default line1">
        <img id="u3837_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3837.svg">
        <div id="u3837_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3838" class="ax_default box_1">
        <div id="u3838_div" class=""></div>
        <div id="u3838_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3839" class="ax_default paragraph">
        <div id="u3839_div" class=""></div>
        <div id="u3839_text" class="text ">
          <p><span>sort_batch：MM202504080000</span></p><p><span><br></span></p><p><span>chute_code：GK03-1002</span></p><p><span><br></span></p><p><span>Container_code：123455555</span></p><p><span><br></span></p><p><span>PKG：55</span></p><p><span><br></span></p><p><span>Box closing time：2025-04-08 10:29:21</span></p><p><span><br></span></p><p><span>Operator：Anna</span></p><p><span><br></span></p><p><span>Print reason：</span><span style="color:#000000;">TMS printing failed （error_type值：error info值）</span></p><p><span><br></span></p><p><span>No. of channel：</span></p><p><span>UHeYFJ-LHR3-HSS-PB-CBN-aL-Zpen</span></p><p><span>…</span></p>
        </div>
      </div>

      <!-- Unnamed (Vertical Line) -->
      <div id="u3840" class="ax_default line1">
        <img id="u3840_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3738.svg">
        <div id="u3840_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3841" class="ax_default heading_3">
        <div id="u3841_div" class=""></div>
        <div id="u3841_text" class="text ">
          <p style="font-size:18px;"><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;font-weight:700;font-size:22px;">NOTICE</span><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;font-weight:650;font-size:22px;">！！！</span></p><p style="font-size:18px;"><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;font-weight:650;">This container's packages need to be scanned individually</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3842" class="ax_default box_1">
        <img id="u3842_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3842.svg">
        <div id="u3842_text" class="text ">
          <p><span>TMS箱贴打印</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3843" class="ax_default box_1">
        <img id="u3843_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3843.svg">
        <div id="u3843_text" class="text ">
          <p><span>调用接口，下发关箱信息给TMS</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3844" class="ax_default connector">
        <img id="u3844_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3844_seg0.svg" alt="u3844_seg0">
        <img id="u3844_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3844_seg1.svg" alt="u3844_seg1">
        <div id="u3844_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3845" class="ax_default connector">
        <img id="u3845_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3845_seg0.svg" alt="u3845_seg0">
        <img id="u3845_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3845_seg1.svg" alt="u3845_seg1">
        <div id="u3845_text" class="text ">
          <p><span>Y</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3846" class="ax_default box_1">
        <img id="u3846_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3843.svg">
        <div id="u3846_text" class="text ">
          <p><span>TMS 关箱箱贴反馈</span></p><p><span>（出参）</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3847" class="ax_default box_1">
        <img id="u3847_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3842.svg">
        <div id="u3847_text" class="text ">
          <p><span>下发打印机</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3848" class="ax_default connector">
        <img id="u3848_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3848_seg0.svg" alt="u3848_seg0">
        <img id="u3848_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3848_seg1.svg" alt="u3848_seg1">
        <div id="u3848_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3849" class="ax_default connector">
        <img id="u3849_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3849_seg0.svg" alt="u3849_seg0">
        <img id="u3849_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3849_seg1.svg" alt="u3849_seg1">
        <div id="u3849_text" class="text ">
          <p><span>Y</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3850" class="ax_default box_1">
        <img id="u3850_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3843.svg">
        <div id="u3850_text" class="text ">
          <p><span>WACS按兜底模板</span></p><p><span>打印</span></p>
        </div>
      </div>

      <!-- Unnamed (Diamond) -->
      <div id="u3851" class="ax_default flow_shape">
        <img id="u3851_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3851.svg">
        <div id="u3851_text" class="text ">
          <p><span>是否未下发箱贴</span></p><p><span>（下发失败或超时）</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3852" class="ax_default box_1">
        <img id="u3852_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3852.svg">
        <div id="u3852_text" class="text ">
          <p><span>WACS打印箱贴</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3853" class="ax_default connector">
        <img id="u3853_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3853_seg0.svg" alt="u3853_seg0">
        <img id="u3853_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3853_seg1.svg" alt="u3853_seg1">
        <img id="u3853_seg2" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3853_seg2.svg" alt="u3853_seg2">
        <img id="u3853_seg3" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3853_seg3.svg" alt="u3853_seg3">
        <div id="u3853_text" class="text ">
          <p><span>N</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3854" class="ax_default connector">
        <img id="u3854_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3854_seg0.svg" alt="u3854_seg0">
        <img id="u3854_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3854_seg1.svg" alt="u3854_seg1">
        <div id="u3854_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3855" class="ax_default connector">
        <img id="u3855_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3855_seg0.svg" alt="u3855_seg0">
        <img id="u3855_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3855_seg1.svg" alt="u3855_seg1">
        <div id="u3855_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Diamond) -->
      <div id="u3856" class="ax_default flow_shape">
        <img id="u3856_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3856.svg">
        <div id="u3856_text" class="text ">
          <p><span>是否推送TMS</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3857" class="ax_default connector">
        <img id="u3857_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3857_seg0.svg" alt="u3857_seg0">
        <img id="u3857_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3857_seg1.svg" alt="u3857_seg1">
        <img id="u3857_seg2" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3857_seg2.svg" alt="u3857_seg2">
        <div id="u3857_text" class="text ">
          <p><span>N</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3858" class="ax_default connector">
        <img id="u3858_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3858_seg0.svg" alt="u3858_seg0">
        <img id="u3858_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3858_seg1.svg" alt="u3858_seg1">
        <img id="u3858_seg2" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3858_seg2.svg" alt="u3858_seg2">
        <div id="u3858_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3859" class="ax_default box_1">
        <img id="u3859_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3843.svg">
        <div id="u3859_text" class="text ">
          <p><span>下发打印机</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3860" class="ax_default box_1">
        <img id="u3860_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3843.svg">
        <div id="u3860_text" class="text ">
          <p><span>WACS兜底箱贴打印</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3861" class="ax_default connector">
        <img id="u3861_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3861_seg0.svg" alt="u3861_seg0">
        <img id="u3861_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3861_seg1.svg" alt="u3861_seg1">
        <div id="u3861_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3862" class="ax_default label1">
        <div id="u3862_div" class=""></div>
        <div id="u3862_text" class="text ">
          <p><span>出参</span></p><p><span>1、打印出来，会给出箱贴相关信息</span></p><p><span>2、打印不出来，给出打印不出来的结果</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3863" class="ax_default box_1">
        <img id="u3863_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3843.svg">
        <div id="u3863_text" class="text ">
          <p><span>PDA关箱成功</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3864" class="ax_default connector">
        <img id="u3864_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3864_seg0.svg" alt="u3864_seg0">
        <img id="u3864_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3864_seg1.svg" alt="u3864_seg1">
        <div id="u3864_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Diamond) -->
      <div id="u3865" class="ax_default flow_shape">
        <img id="u3865_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3856.svg">
        <div id="u3865_text" class="text ">
          <p><span>包裹数据校验：</span></p><p><span>箱包裹明细是否不存在</span></p><p><span>是否物流面单=‘否’的包裹</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3866" class="ax_default connector">
        <img id="u3866_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3866_seg0.svg" alt="u3866_seg0">
        <img id="u3866_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3866_seg1.svg" alt="u3866_seg1">
        <div id="u3866_text" class="text ">
          <p><span>Y</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3867" class="ax_default connector">
        <img id="u3867_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3867_seg0.svg" alt="u3867_seg0">
        <img id="u3867_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3867_seg1.svg" alt="u3867_seg1">
        <img id="u3867_seg2" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3867_seg2.svg" alt="u3867_seg2">
        <div id="u3867_text" class="text ">
          <p><span>N</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3868" class="ax_default paragraph">
        <div id="u3868_div" class=""></div>
        <div id="u3868_text" class="text ">
          <p style="font-size:13px;"><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;font-weight:650;font-size:14px;color:rgba(0, 0, 0, 0.8745098039215686);">WACS:关箱数据是否下发TMS</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">apollo</span><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;font-weight:400;">参数</span><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">-</span><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;font-weight:400;">设备层级</span></p><p style="font-size:13px;"><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;font-weight:400;">默认值：关</span></p>
        </div>
      </div>

      <!-- Unnamed (Diamond) -->
      <div id="u3869" class="ax_default flow_shape">
        <img id="u3869_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3856.svg">
        <div id="u3869_text" class="text ">
          <p><span>是否开启</span></p><p><span>包裹数据校验</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3870" class="ax_default connector">
        <img id="u3870_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3870_seg0.svg" alt="u3870_seg0">
        <img id="u3870_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3870_seg1.svg" alt="u3870_seg1">
        <div id="u3870_text" class="text ">
          <p><span>Y</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3871" class="ax_default connector">
        <img id="u3871_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3871_seg0.svg" alt="u3871_seg0">
        <img id="u3871_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3871_seg1.svg" alt="u3871_seg1">
        <img id="u3871_seg2" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3871_seg2.svg" alt="u3871_seg2">
        <img id="u3871_seg3" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3871_seg3.svg" alt="u3871_seg3">
        <div id="u3871_text" class="text ">
          <p><span>N</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3872" class="ax_default label1">
        <div id="u3872_div" class=""></div>
        <div id="u3872_text" class="text ">
          <p style="font-size:14px;"><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;font-weight:650;">WACS:TMZ箱贴超时参数：</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">apollo</span><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;font-weight:400;">参数</span><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">-</span><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;font-weight:400;">设备层级</span></p><p style="font-size:13px;"><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;font-weight:400;">默认值2（单位min）</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3873" class="ax_default paragraph">
        <div id="u3873_div" class=""></div>
        <div id="u3873_text" class="text ">
          <p><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;font-weight:400;">接口</span><span style="font-family:'Nunito Sans', sans-serif;font-weight:400;">: </span><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;font-weight:400;">下发关箱分拣结果（</span><span style="font-family:'Nunito Sans', sans-serif;font-weight:400;">WCS</span><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;font-weight:400;">系统</span><span style="font-family:'Nunito Sans', sans-serif;font-weight:400;">--&gt;TMS</span><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;font-weight:400;">）</span></p>
        </div>
      </div>

      <!-- Unnamed (Diamond) -->
      <div id="u3874" class="ax_default flow_shape">
        <img id="u3874_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3856.svg">
        <div id="u3874_text" class="text ">
          <p><span><br></span></p><p><span>分拣批次对应包裹明细</span></p><p><span>是否不为空</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3875" class="ax_default connector">
        <img id="u3875_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3875_seg0.svg" alt="u3875_seg0">
        <img id="u3875_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3875_seg1.svg" alt="u3875_seg1">
        <div id="u3875_text" class="text ">
          <p><span>N</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3876" class="ax_default box_1">
        <img id="u3876_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3843.svg">
        <div id="u3876_text" class="text ">
          <p><span>关箱不进行接口推送</span></p><p><span>关箱不进行箱贴打印</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3877" class="ax_default connector">
        <img id="u3877_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3877_seg0.svg" alt="u3877_seg0">
        <img id="u3877_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3877_seg1.svg" alt="u3877_seg1">
        <div id="u3877_text" class="text ">
          <p><span>Y</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3878" class="ax_default box_1">
        <img id="u3878_img" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3843.svg">
        <div id="u3878_text" class="text ">
          <p><span>关箱不推送TMS</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3879" class="ax_default paragraph">
        <div id="u3879_div" class=""></div>
        <div id="u3879_text" class="text ">
          <p style="font-size:13px;"><span style="font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;font-weight:650;font-size:14px;color:rgba(0, 0, 0, 0.8745098039215686);">WACS:关箱包裹是否包含物流面单数据校验参数：</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">apollo</span><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;font-weight:400;">参数</span><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">-</span><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;font-weight:400;">设备层级</span></p><p style="font-size:13px;"><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;font-weight:400;">默认值：关</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3880" class="ax_default connector">
        <img id="u3880_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3880_seg0.svg" alt="u3880_seg0">
        <img id="u3880_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3880_seg1.svg" alt="u3880_seg1">
        <img id="u3880_seg2" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3880_seg2.svg" alt="u3880_seg2">
        <div id="u3880_text" class="text ">
          <p><span>N</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u3881" class="ax_default connector">
        <img id="u3881_seg0" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3881_seg0.svg" alt="u3881_seg0">
        <img id="u3881_seg1" class="img " src="/unpkg/ax/TRPE1R/1550/images/wacs箱贴打印_ltcpeo/u3881_seg1.svg" alt="u3881_seg1">
        <div id="u3881_text" class="text ">
          <p><span>N</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u3882" class="ax_default _二级标题1">
        <div id="u3882_div" class=""></div>
        <div id="u3882_text" class="text ">
          <p><span style="font-family:'Arial Normal', 'Arial', sans-serif;">4. </span><span style="font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;">将生成箱贴 下发PDA匹配的打印机，进行箱贴打印</span></p>
        </div>
      </div>
    </div>
    <script src="https://assets.dotfashion.cn/webassets/axure-cloud-html/TRPE1R/1550/resources/scripts/axure/ios.js"></script>
  <script type="text/javascript">
            window.onload = function (){
            document.getElementById('meiliTitle').innerHTML = "<h1>WCS系统基线文档</h1><h2>" + document.title + "</h2>"
            }
           </script>
        

</body>
```