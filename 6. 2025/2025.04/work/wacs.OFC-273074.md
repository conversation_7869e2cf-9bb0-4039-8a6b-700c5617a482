# OFC-273074

OFC-273074 【项目-波兰包裹分拣】.WCS.分拣方案、分拣规则定义，支持新增‘是否面单前置’、‘是否重量差异’系统配置

## 需求改动点
### ✅ 一、分拣方案维护
分拣方案维护 /sys-management/sorting-arrangement/sorting-scheme-maintenance
1、分拣方案-新增/编辑/查看，增加2个条件：
是否面单前置
是否重量差异

2、分拣方案列表，其他方案条件，支持对应显示上述新增2个条件

### ✅ 二、分拣规则定义
二、分拣规则定义 /sys-management/sorting-arrangement/sorting-rule-definition
1、分拣条件-新增/编辑/查看，增加2个条件：
是否面单前置
是否重量差异

2、分拣规则列表，分拣条件，支持对应显示上述新增2个条件

### 三、物料数据
#### 需求改动点
物料数据 /sys-management/sorting-management/material-data
1、物料数据-物料详情：
‘物料类型’ 字段 页面位置，调整到‘物料主条码’字段前

2、物料数据-分拣条件页面：新增字段显示
是否物流面单（取代 原目的仓库）
是否重量差异（取代 原目的园区）

#### 接口文档
1.物料数据
列表接口：/wacs/front/sort_goods_data/list
soapi: https://soapi.sheincorp.cn/application/3894/routes/post_wacs_front_sort_goods_data_list/doc
列表数据的sorted_condition_list 会返回多两个条件，展示详情用列表的数据
是否重量差异：is_weight_dif
是否物流面单：is_unique_code

### ✅ 四、分拣计划
#### 需求改动点
四、分拣计划 /sys-management/sorting-management/sorting-plan
1、分拣计划-计划详情：
‘物料类型’ 字段 页面位置，调整到‘物料主条码’字段前

2、分拣计划-计划详情页面：新增字段显示
是否物流面单（取代 原目的仓库）
是否重量差异（取代 原目的园区）

3、分拣条件，支持上述2个新增字段显示
