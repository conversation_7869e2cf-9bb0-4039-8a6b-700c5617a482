# prompt

帮我改动这个mcp项目，基于下面这个接口来实现接口详细信息查询
 
获取接口详细信息（供外部调用）

/api/application/1/flattenRoute

接口请求和接口响应
```typescript
interface IQueryStrings {
  /** soapi接口详情页面的链接，传这个参数就不用传name，aid随便填 */
  url?:string;
}

interface IData {
  /** name */
  name:string;
  /** chinese name */
  title:string;
  /** tag */
  tag?:string;
  /** path */
  path:string;
  /** route method */
  method:string;
  /** route description */
  description?:string;
  /** route Params */
  routeParams?:string;
  /** query String */
  queryStrings?:string;
  /** requestHeaders */
  requestHeaders?:string;
  /** requestBody */
  requestBody?:string;
  /** responseHeader */
  responseHeaders?:string;
  /** responseBody */
  responseBody?:string;
  /** route status */
  status?:number;
  appName?:string;
  mockExpects?:string;
  mockScript?:string;
  id:string;
}

interface IResponseBody {
  data:IData;
  code:number;
  message?:string;
}
```

接口header参考
```typescript
fetch("https://soapi.sheincorp.cn/api/application/31/route/58852", {
  "headers": {
    "authorization": "Bearer ****",
    "content-type": "application/json; charset=utf-8",
    "language": "zh",
    "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"macOS\"",
    "Referer": "https://soapi.sheincorp.cn/application/31/routes/58852/doc",
    "Referrer-Policy": "strict-origin-when-cross-origin"
  },
  "body": null,
  "method": "GET"
});
```

