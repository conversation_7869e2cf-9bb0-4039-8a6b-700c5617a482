/* eslint-disable @shein-bbl/bbl/translate-i18n-byT */
const fs = require('fs');
const { glob } = require('glob'); // 修改为解构导入
const path = require('path');

// 匹配颜色代码的正则表达式
const colorRegex = /#([A-F0-9]{3,6})/gi;

// 统计数据
let filesProcessed = 0;
let filesChanged = 0;
let colorCodesChanged = 0;

// 转换颜色代码为小写
function convertColorCase(content) {
  let newContent = content;
  let matches = 0;

  newContent = content.replace(colorRegex, (match) => {
    matches++;
    return match.toLowerCase();
  });

  colorCodesChanged += matches;
  return {
    newContent,
    changed: matches > 0 && newContent !== content,
  };
}

// 处理单个文件
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const { newContent, changed } = convertColorCase(content);

    if (changed) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      filesChanged++;
      console.log(`已修改: ${filePath}`);
    }

    filesProcessed++;
  } catch (error) {
    console.error(`处理文件 ${filePath} 时出错:`, error);
  }
}

// 异步包装glob - 修改为直接使用 glob 的 Promise API
function globAsync(pattern) {
  return glob(pattern); // glob v8+ 已经返回 Promise
}

// 主函数
async function main() {
  const patterns = [
    'src/**/*.css',
    'src/**/*.less',
  ];

  console.log('开始处理文件...');

  try {
    // 获取所有匹配的文件
    const fileArrays = await Promise.all(patterns.map(pattern => globAsync(pattern)));
    const files = fileArrays.flat();

    console.log(`找到 ${files.length} 个文件需要处理`);

    // 处理所有文件
    for (const file of files) {
      processFile(file);
    }

    console.log('\n处理完成!');
    console.log(`总计处理文件数: ${filesProcessed}`);
    console.log(`修改的文件数: ${filesChanged}`);
    console.log(`转换的颜色代码数: ${colorCodesChanged}`);
  } catch (error) {
    console.error('执行过程中发生错误:', error);
  }
}

main();
