# ips

## 目标
68.89-65.48=3.41

目标：57.45*1.2=68.89
差值：68.89-57.41=11.48
共优化：1.5+1+5.5+3=11.5

## 治理细节
- [✅ +1.5?] 研发规范(21.94/31)
  - ES代码规范合规(13.04/18)
  - [✅ +1?]ES缺陷波及检测(1.8/4)
    - 增加注释
  - [✅ +0.5?] 文件命名缺陷检测(2.27/3) 
    - 处理了前5个文件命名(5/14)
    - src/createHashHistory.js
    - src/createStore.js
    - src/public-component/dateRangePicker2Wrapper.jsx
- [✅ +1?]代码可读(14.6/21)
  - [✅ +1?]Dom API调用收敛(2.1/4)
    - window.open
- [✅ +5.5?]工程配置(9.48/18)
  - 9.5 -> 15 = +5.5
- [✅ +3?] 可维护性(11.43/20) 
  - [✅ +1?]最长重复行检测(2.07/4)
    - 改重复代码列表前5的页面
  - [✅ +1]依赖包相似检测(0/1)
    - 移除date-fns（无使用）
  - [✅ +1?]依赖包风险检测（1.2/3）, +1?
    - copy-list
    - lodash
    - lodash.clonedeep
    - env-cmd