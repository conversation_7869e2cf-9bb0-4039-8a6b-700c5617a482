# ips-w

## 目标
目标：54.56*1.2=65.47

之前：54.56
上一次：62.61（+8.05）
差值：65.47-62.61=2.86

## 治理细节
- [+4?] 研发规范(31) 22.38-20.88=1.5
  - [+3?]CSS代码规范合规(0/4) 3.83-0=3.83
  - [+1?]CSS缺陷波及检测(0.44/2) 1.78-0.44=1.34
  - [+0.5?]文件命名缺陷检测(2.53/3) 2.97-2.53=0.44 
- [] 代码可读(21) 12.47-11.86=0.61
  - [] DOM API调用收敛(0/4) 0-0=0?
- [+7?] 工程配置(18) 13-9=4
- [+2?] 可维护性(20) 14.76-12.82=1.94
  - [✅+2?] 依赖包可升级检测(0/2) 2-0=2
  
上期优化：1.5+0.61+4+1.94=8.05

本次需要优化 2.86+2=4.86

工程配置+4

还差0.86

这次重点放在dom api

这样就留足提分空间了