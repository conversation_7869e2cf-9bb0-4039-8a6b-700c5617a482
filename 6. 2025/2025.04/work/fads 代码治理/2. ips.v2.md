# ips.v2

## 目标
目标：57.45*1.2=68.89

之前：57.41
上一次：65.48（+8.07）
差值：68.89-65.48=3.41

## 治理细节
- [+1.5?] 研发规范(31) 22.95-21.94=1.01
  - [] ES代码规范合规(13.04/18) 13.07-13.04=0.03 
  - [+1?]ES缺陷波及检测(1.8/4) 1.8->2.1=0.3
  - [+0.5?] 文件命名缺陷检测(2.27/3) 2.27->2.6=0.33
- [+1?]代码可读(21) 16.66-15.63=1.03
  - [+1?]Dom API调用收敛(2.1/4) 2.1->2.75=0.65
- [+5.5?] 工程配置(18) 13.24-9.48=3.76
- [+3?]可维护性(20) 15.89-13.66=2.23
  - [+1?]最长重复行检测(2.07/4) 2.07->1.89=-0.18
  - [✅+1?]依赖包相似检测(0/1) 0->1
  - [✅+1]依赖包风险检测（1.2/3）1.2->2.4=1.2

上期优化：1.01+1.03+3.76+2.23=8.03

本期优化：3.41+2=5.41

工程配置+4

还差1.41

css 缺陷修复：0.3+0.52=0.82
eslint 补注释：？
dom api 收敛：2.75 -> ?



