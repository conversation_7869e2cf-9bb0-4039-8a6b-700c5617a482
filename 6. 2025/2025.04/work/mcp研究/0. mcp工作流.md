# mcp 工作流

mcp: shadowCookie

## 需求侧
需求侧：dcp/web search
根据dcp的需求号获取到需求改动点
实现：dcp->描述中有prd链接->prd: axure

但是axure 没有api方法，会遇到很多问题。

测试侧：暂无

需求侧：

但我的工作流可以拆分为：
复制所有的axure html-> 给ai做需求分析 -> 输出需求分析的NOTEPADS

怎么利用

怎么cursor 的NOTEPADS

我想利用ai来分析需求，标记出这次需求的改动点，并且注明涉及前端的改动点，以及前端需要跟后端联调的部分

公司的需求软件是Axure RP，我打算将需求的每一页html发给ai做分析，应该怎么规范ai的输出格式

打算粘贴到cursor 的NOTEPADS中，方便后续利用ai完成代码开发

## 开发侧
[应该可行]联调侧：soapi
实现：soapi的MCP server，方便自行嗅探接口

[应该可行] 开发侧：gitlab 
实现：提交代码，创建合并到test分支的MR

先从gitlab 入手吧，至少有正规api

