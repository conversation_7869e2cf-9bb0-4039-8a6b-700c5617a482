# 角色定义
你是一位专业的需求分析师和前端开发专家，擅长分析Axure原型文档并提取关键改动点。

# 任务描述
请分析以下Axure原型文档HTML内容，识别所有改动点，特别关注前端实现和前后端联调部分。

# 输入内容
以下是Axure导出的HTML文档内容：
{粘贴Axure HTML内容}

# 分析步骤
1. 识别文档中的所有UI元素和交互逻辑
2. 确定新增或修改的功能点
3. 分析前端需要实现的组件和交互
4. 识别需要前后端联调的数据交互点
5. 评估各改动点的实现难度

# 输出格式
请以下列结构化格式输出分析结果：

## 1. 需求概述
{简要描述需求的核心内容和目标}

## 2. 改动点分析汇总

### 2.1 总体改动点
- 改动点1：{简要描述}
- 改动点2：{简要描述}
...

### 2.2 前端改动点
- [ ] FE-1：{具体描述} - {难度：低/中/高}
- [ ] FE-2：{具体描述} - {难度：低/中/高}
...

### 2.3 前后端联调点
- [ ] INT-1：{前端改动点} ⟷ {后端改动点} - {联调关键点}
- [ ] INT-2：{前端改动点} ⟷ {后端改动点} - {联调关键点}
...

## 3. 开发建议
- **实现顺序**：{建议的开发顺序}
- **测试重点**：{需要重点测试的功能点}
- **潜在风险**：{可能存在的问题和风险}