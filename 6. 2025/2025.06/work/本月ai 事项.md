# 本月ai 事项

知道了██████之后，我更加急迫了，现在有了目标

目标：我一定要赶在██████之前，完成“ai写出业务代码”这件事儿

ai how to work with large codebases

要在ips，lpmp，wms，mot中落地

## 资料

- [Large Codebases](https://docs.cursor.com/guides/advanced/large-codebases)
  - 先ask再agent
- [Working with Documentation](https://docs.cursor.com/guides/advanced/working-with-documentation)
  - 创建内部文档MCP
    - sheinout
    - sheinout mobile
    - lego
  - ftic 代码规范
- openMemory


```
- create a plan for how we shoud create a new feature (just like @existingfeature.ts)
- ask me questions (max 3) if anything is unclear
- make sure to search the codebase

here's some more context from [project management tool]:
[pasted ticket description]
```

```
- create a plan for how we shoud create a new feature
- ask me questions (max 3) if anything is unclear
- make sure to search the codebase
- 请整理一个plan.md，用于后续agent 生成代码

new feature:
```


## 日志
# 2025.06.19
尝试了以plan的形式完成需求开发，见"Ai/3. plan/滑槽作业信息查询 - 新增"波次->批次"页签开发计划.md"

总结，查缺补漏
- t函数要引入`import { t } from '@shein-bbl/react';`

生成出来的代码要调整，集中在
1. 接口命名规范（改成I
2. 缺失的 t 函数导入
3. InputMore 组件属性修复(实际上不用修复)
4. 缺失了用户数据联动的useEffect

第1，3点我认为是内部代码规范问题，这几天想办法开展"内部文档mcp"计划

第2点，如果有规范文档的话，估计会好一点

第4点，呃，代码漏了这点没啥办法

