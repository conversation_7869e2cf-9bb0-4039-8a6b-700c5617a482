现在有个需求：支撑系统后端long字段改造，排查前端有无影响和改造

能不能写个nodejs脚本，输出json

举个例子
字段名称,逗号分隔	接口URL
id	/wts/front/box_storage/query

我需要搜索/box_storage/query，获取到各项目中使用的地方

为什么是搜索box_storage/query，而不是/wts/front/box_storage/query。因为实际项目中，我们是这样使用的

```
// 查询
export const getListAPI = (param) => sendPostRequest({
  url: 'box_storage/query',
  param,
}, process.env.WTS_URI);
```

用box_storage/query来搜索，是为了避免遗漏

我搜索出的结果：
/Users/<USER>/Documents/3. Worktree/all-project/wms-eu/src/component/transfer-save-manage/box-storage/server.js
  5,9:   url: '/box_storage/query',

/Users/<USER>/Documents/3. Worktree/all-project/wms-inbound/packages/wms-inbound/src/component/transfer-save-manage/box-storage/server.js
  5,9:   url: '/box_storage/query',

/Users/<USER>/Documents/3. Worktree/all-project/wms-la/packages/micro-inbound/src/component/transfer-save-manage/box-storage/server.js
  5,9:   url: '/box_storage/query',

/Users/<USER>/Documents/3. Worktree/all-project/wms-me/src/component/transfer-save-manage/box-storage/server.js
  7,13:       url: '/box_storage/query',

/Users/<USER>/Documents/3. Worktree/all-project/wms-na/packages/wms/src/component/transfer-save-manage/box-storage/server.js
  5,9:   url: '/box_storage/query',

我希望数据的结果整理为json

```json
{
  'url': '/wts/front/box_storage/query',
  'key': 'id',
  'searchKey': 'box_storage/query',
  'results': [
    {
      'project': 'wms-eu',
      'path': '/Users/<USER>/Documents/3. Worktree/all-project/wms-eu/src/component/transfer-save-manage/box-storage/server.js',
      'line': 5,
      'column': 9,
    },
    {
      'project': 'wms-inbound',
      'path': '/Users/<USER>/Documents/3. Worktree/all-project/wms-inbound/packages/wms-inbound/src/component/transfer-save-manage/box-storage/server.js',
      'line': 5,
      'column': 9,
    },
    {
      'project': 'wms-la',
      'path': '/Users/<USER>/Documents/3. Worktree/all-project/wms-la/packages/micro-inbound/src/component/transfer-save-manage/box-storage/server.js',
      'line': 5,
      'column': 9,
    },
    {
      'project': 'wms-me',
      'path': '/Users/<USER>/Documents/3. Worktree/all-project/wms-me/src/component/transfer-save-manage/box-storage/server.js',
      'line': 7,
      'column': 13,
    },
    {
      'project': 'wms-na',
      'path': '/Users/<USER>/Documents/3. Worktree/all-project/wms-na/packages/wms/src/component/transfer-save-manage/box-storage/server.js',
      'line': 5,
      'column': 9,
    }
  ],
}
```
