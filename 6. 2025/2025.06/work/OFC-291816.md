# OFC-291816 需求开发

- ips
- ips-w

- [✅] 1、送货车辆看板新增“是否仓储卸货”筛选条件
- [✅] 2、车辆数据看板新增“送/提货子类型”筛选条件
- [✅] 3、签到页面新增纯文字提醒
- [✅] 4、新增调仓记录导出功能
- [✅] 7、停靠车位管理优化
- [✅] 8、到货入园管理搜索优化

## temp
【配合WMS项目】“卸货工项目”IPS配合增加是否仓储卸货的筛选功能

送货车辆看板新增是否仓储卸货列表+导出
https://soapi.sheincorp.cn/application/3090/routes/post_ips_front_pc_vehicle_board_delivery_car/doc
https://soapi.sheincorp.cn/application/3090/routes/post_ips_front_pc_vehicle_board_delivery_car_export_new/doc
车辆数据看板新增子类型列表+导出
https://soapi.sheincorp.cn/application/3090/routes/post_ips_front_pc_vehicle_board_process_board/doc
https://soapi.sheincorp.cn/application/3090/routes/post_ips_front_pc_vehicle_board_process_board_new_export/doc

调仓记录导出 是新接口，异步接口
送货车辆看板新增是否仓储卸货列表+导出  新增入参字段 is_warehouse_unload
车辆数据看板新增子类型列表+导出   新增入参字段 function_detail_type

## 1、送货车辆看板新增“是否仓储卸货”筛选条件
送货车辆看板新增是否仓储卸货列表+导出
https://soapi.sheincorp.cn/application/3090/routes/post_ips_front_pc_vehicle_board_delivery_car/doc
https://soapi.sheincorp.cn/application/3090/routes/post_ips_front_pc_vehicle_board_delivery_car_export_new/doc

## 2、车辆数据看板新增“送/提货子类型”筛选条件
https://ips-test01.dotfashion.cn/#/board/car-data-board
由单选选择 变更为 级联选择器
一级选项为送货车辆和提货车辆，二级选项分别对应为旗下的子类型，关系如下：
送货车辆：提货、补货、其他
提货车辆：退供、发货、其他
关系取自数据字典
修改后搜索项目由“送货/提货车辆”，变更为“送/提货子类型”

function_detail_type的是读字典 DeliverFunctionDetailType

车辆数据看板新增子类型列表+导出
https://soapi.sheincorp.cn/application/3090/routes/post_ips_front_pc_vehicle_board_process_board/doc
https://soapi.sheincorp.cn/application/3090/routes/post_ips_front_pc_vehicle_board_process_board_new_export/doc

## 3、签到页面新增纯文字提醒
1、签到页面，如图，新增“温馨提醒：每月累计取消超5次/过号超3次，将限制入园7天” 文本提示，不区分状态

## 4、新增调仓记录导出功能
2、点击按钮后由用户选择导出的范围：
- 时间范围选择框
- 最小跨度为1天，最大跨度为31天，不可选择未来时间

调仓记录导出:https://soapi.sheincorp.cn/application/3090/routes/post_ips_front_pc_vehicle_board_adjust_export/doc


## 7、停靠车位管理优化
/management-center/carport-information-maintain

添加停靠停靠车位
1、物流园区选项，自动带出右上角选择的园区
2、车位号输入限制解除，允许输入0-10位任意字符
3、编辑弹窗，车位号禁用限制解除，允许编辑车位号；点击确认后新增车位号校验逻辑，逻辑和新增保持一致即可

## 8、到货入园管理搜索优化
物流公司搜索框逻辑变更：
原逻辑：枚举值取自数据字典的 编码，且中文名字段不去重，搜索时采用数据字典 编码 匹配
现逻辑：枚举值取数据字典的中文字段，需去重，搜索逻辑采用中文进行匹配。（原因是上游系统一个物流公司会有多个编码，使用编码匹配会出现重复）

