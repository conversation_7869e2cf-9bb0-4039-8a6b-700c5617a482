# prompt 

## 原始数据
字段名称,逗号分隔	接口URL	后端应用	搜索值	前端应用	内容	前端确认(前端无使用/前端无影响/前端需改动) 使用场景



## prompt v2
任务：
请帮我制定一个可分批按步骤进行的计划，帮我输出一个plan.md，用来后续ai agent执行计划

核心验证要求（必须严格执行）
1. URL路径精确匹配验证
关键要求：必须验证原始接口路径与前端实际请求路径是否完全一致
验证方法：
明确原始接口的完整路径（如：/wgs/front/common_qa_config/query）
查找前端代码中的实际请求路径，注意环境变量替换（config/process-env.ts）
严格比较两个完整路径是否完全匹配
重要：不同路径的接口视为完全不同的接口，不能假设功能相同
2. 功能实际启用状态验证
关键要求：区分"配置存在"与"功能启用"
验证方法：
检查相关import语句是否被注释
检查函数调用是否被注释或禁用
验证URL映射/路由转换功能是否真正生效
追踪完整调用链路确认功能实际运行
3. 逐步验证分析假设
关键要求：每个分析结论都要有明确验证依据
验证方法：
在分析开始前明确列出关键假设
逐一验证每个假设的正确性
发现矛盾时立即停止并重新分析
不要基于未验证的假设继续深入分析
4. 交叉验证检查
关键要求：从多个角度验证同一结论
验证方法：
代码搜索确认接口是否被调用
文件路径对比确认接口定义位置
运行时路径确认（环境变量拼接结果）
功能开关状态确认
具体执行要求
阶段1：接口路径真实性验证
目标：确认todo.md中的原始接口是否真的被前端使用

执行步骤：

解析config/process-env.ts中的环境变量
对每个原始接口路径，查找前端代码中对应的请求代码
计算前端实际请求的完整URL路径
严格对比原始接口路径与实际请求路径是否完全一致
如发现不匹配，标记为"前端未使用该接口"
阶段2：URL映射功能验证
目标：如果存在URL映射配置，验证是否真正启用

执行步骤：

检查是否存在URL路由映射配置文件
查找映射功能的import和调用代码
确认映射功能是否被注释或禁用
验证映射功能在实际请求中是否生效
阶段3：接口使用情况分析
目标：仅对确认被使用的接口进行深入分析

前置条件：只有通过阶段1和阶段2验证的接口才进入此阶段

执行步骤：

确认接口函数定义和调用位置
分析接口在业务场景中的使用方式
检查数据处理中的风险点（数值运算、比较等）
评估类型变更的影响
阶段4：测试场景制定
目标：仅为真正受影响的接口制定测试场景

阶段5：文档更新
目标：基于真实验证结果更新todo.md

特别要求：

如果发现接口路径不匹配，在表格中明确标记"❌不匹配"
如果发现功能未启用，标记"❌未使用"
只有确认使用的接口才标记为需要进一步分析
关键检查点
在每个阶段结束时，必须验证：

分析的接口是否真的被前端使用？
得出的结论是否有充分的代码证据支持？
是否存在被忽略的配置或开关状态？
如果发现问题，是否立即修正了后续分析方向？
如果任何阶段发现接口未被实际使用，应立即标记并停止对该接口的进一步分析。

请确保todo.md中的每个原始数据都进行检查，并根据真实验证结果更新表格数据。


## prompt

背景：
后端接口字段从long类型转为string，前端需要检查风险点

任务：
请帮我制定一个可分批按步骤进行的计划，帮我输出一个plan.md，用来后续ai agent执行计划

1. 确认接口url与搜索到内容是否匹配。如果接口中包含环境变量，环境变量保存在config/process-env.ts
2. 确认接口函数是否有使用
3. 确认接口的使用场景
4. 如果是ts 的类型转换，不进行代码改动；如果进行了改动，要在文档中补充改动点测试场景，并标记对应的接口
5. 请确保todo.md 中的每个原始数据都有进行检查，并更新todo.md 中的表格数据