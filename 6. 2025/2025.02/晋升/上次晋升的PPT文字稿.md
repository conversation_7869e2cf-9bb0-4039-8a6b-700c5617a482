# 2024 上半年晋升答辩

## 1. 个人履历概述

2021.9 - 2023.9

担任仓储 库内业务线前端负责人
负责wms(仓储管理系统)、mot(仓储移动作业终端)、wsk(观沧海) 等系统的开发维护工作
主导多个项目的前端开发工作
主动优化系统并立项：「WMS打印优化」项目

2023.10 - 至今

担任拉美履约研发部的前端负责人
管理组内项目需求和日常需求的前端开发工作
主导拉美前端组的架构设计、建立和完善代码规范、开发自动化提效工具等工作

## 2. 业绩贡献和问题解决

### 2.1 业绩贡献
当前职责：拉美履约 - 前端主负责人
当前负责系统数：3  （ 仓储管理系统-拉美站 wms-la、仓储移动作业终端-拉美站 mot-la、观仓海-拉美 wsk-la）
23年参与项目数 11个，业务需求数 	120，开发时效控制在 1.46d；

关键产出总览

1. 主导拉美履约BBL管理体系
https://wiki.dotfashion.cn/pages/viewpage.action?pageId=1294421691
2. 拉美前端代码规范化
https://wiki.dotfashion.cn/pages/viewpage.action?pageId=1295361377
3. 拉美履约研发团队的Jira自动化推送 
https://wiki.dotfashion.cn/pages/viewpage.action?pageId=1295377813
4. 自主研发自动化发版工具 dutyRobot
https://gitlab.sheincorp.cn/div/group_biz/wms-extensions/wms-script/-/tree/main/dutyRobot
5. 独立部署：wsk-na、wsk-la应用的系统独立部署
6. 架构建设：mot、wsk 应用的paas ci配置标准化和网关发布等
7. 业务组件 StretchMenu 拉伸菜单：
https://ue.dev.sheincorp.cn/component/StretchMenu/0.1.9

### 2-2 专业技能和问题解决

#### 1. 拉美履约BBL管理体系
1. 拉美履约BBL管理体系
背景：拉美履约研发部门成立初期，业务反馈系统中存在词条未翻译、翻译流程混乱、系统告警缺失词条过多、后端未开启缺失词条上报等问题。

目的：规范流程，提升效率，优化用户体验
方式：1. 规划化工作流程：与产品、后端、测试多方共同协定了一套标准化的工作流程，确保了多语言翻
 译管理的高效性和准确性。
   1. 跨部门沟通与合作：积极与架构组BBL团队进行沟通协调，就功能迭代方向和优先级达成一致。
   2. 提效工具开发：主导开发了一系列的赋能工具，包括Chrome插件和VSCode编辑器插件。这些
 工具能够自动导出上线需求中的未翻译词条以及系统中的全量词条，大幅减少手工操作的时间和
 出错率，提升了团队时效。
结果：整合流程优化和赋能工具的方案，形成一套完整的拉美履约BBL管理体系，有效支撑业务发展。

关键产出：1. 输出规范文档
https://wiki.dotfashion.cn/pages/viewpage.action?pageId=1294421691
   2. 开发Chrome插件
https://gitlab.sheincorp.cn/ofla/la-front-end/broswer_tools   
   3. 开发VSCode编辑器插件
https://gitlab.sheincorp.cn/ofla/la-front-end/vscode-toolkit

#### 2. 自动化发版工具 dutyRobot
2. 自动化发版工具 dutyRobot
背景：国内仓储前端团队经过微前端改造和海外区独立部署后，导致项目数量从原有的7个激增到18个，造成了值班
   发版人员的工作增加，以及存在需求漏合的风险。
目的：自动化辅助，减少人力投入，降低人为操作风险
方式：1. 痛点分析：确定当前发版流程中存在哪些效率低下和容易出错的环节，分析造成这些问题的根本、
原因。
   1. 技术调研与架构设计：调研技术栈（如Node.js、GitLab API、Jira API 等），设计架构，确保
其易于集成和扩展。
   1. 敏捷开发：增量开发和迭代改进，小步快跑，分阶段交付产品功能，定期收集反馈，在持续优化
过程中灵活应对变化，支撑海内外仓储前端团队的发版需求。

结果：自动化发版工具的上线，降低了发版工作的人力成本，同时提前暴露分支漏合的风险

关键产出：项目地址 https://gitlab.sheincorp.cn/div/group_biz/wms-extensions/wms-script/-/blob/main/dutyRobot/readme.md

## 3 三能案例阐述
### 3.1. 能辨
场景: 一仓多包结构的wms-la和mot-la 应用，包之间的代码规范化配置互相冲突，导致原有配置失效

分析: 不同规范化工具（commitlint/lint-staged/husky/ESLint/Prettier等）对Monorepo 的支持度不同，需要针对性进行各工具配置

行为：
1. （能辨：分析力）灵活配置：梳理通用配置和定制化配置，确保了不同包之间可以共享大部分配	  置，同时也保留了据具体包需求进行调整的灵活性；
2. （能辨：学习力）自动化代码检查: 通过集成gitlab API、企业微信机器人推送和babel AST的	  自定义代码检查机制，能够更深层次的业务逻辑检查。通过企微推送，确保及时有效。

结果：实现了代码规范与自动化工具链的深度融合，显著提升了开发效率和代码质量，减少codeReview 的人力成本。

### 3.2. 能为
场景：部门成立初期缺乏有效的jira任务监控方案，导致研发时效上升。

分析：借鉴国内仓储团队的push-robot 公共服务，结合我们的特定需求，定制一个高效的任务监
   控和实时推送机制。

行为：
1. （能为）分析规划：通过分析团队需求，我们规划了一个简单而有效的解决方案：通过集
成Jira API来自动捕捉需求的状态，并通过企微机器人实时推送给团队成员。
2. （能为）快速执行：通过敏捷开发的开发形式，设置定时任务触发脚本，随即通过企微API推相关信息给指定的群组。

结果：自动化jira 推送机制的实施，显著优化了团队的开发时效，并减少了沟通成本。

### 3.3. 能燃
场景：在负责拉美履约仓储BBL管理体系的过程中，面对的挑战主要集中在如何提升多语言翻译的管理效率，以及如何保证翻译内容的准确性和及时性。

分析：(能辨) 通过与后端、测试、产品、实施等多方的沟通协作，重新规划BBL管理的工作流程。

行为：
1. (能燃) 跨部门合作和功能迭代：与架构组的BBL团队进行了深入沟通，共同商议后续的功能
迭代计划。
2. (能为) 工具赋能：开发了Chrome插件和VSCode编辑器插件，提升团队的效率。

结果：
1. 管理效率和翻译质量双重提升
2. 技术创新和团队能力提升
3. 跨部门合作和影响力扩大

## 4.晋升后岗位的设想及目标

1. 系统性能优化
推出拉美前端系統LCP优化体系，前端系统整体指标优化20%以上。

2. 稳定性建设
建立和完善拉美前端系统的稳定性建设体系，包括预案演练、告警梳理、巡检机制等。

3. 团队建设
新人带教：制定一套前端侧的新人带教流程，包括但不限于技术栈培训、项目实战演练、文化融入等，确保新人能够快速融入团队。
培训考核体系：建立前端侧的新人培训考核体系，通过阶段性的考核和评估，确保新人在技术能力、项目执行、团队协作等方面达到团队要求。

4. 技术探索
赋能工具开发：ai与前端工作流的结合，探索ai对前端研发效能的提升。
