# 晋升举证材料

## 1. 个人履历介绍
2021.9 - 2023.9

担任仓储 库内业务线前端负责人
负责wms(仓储管理系统)、mot(仓储移动作业终端)、wsk(观沧海) 等系统的开发维护工作
主导多个项目的前端开发工作
主动优化系统并立项：「WMS打印优化」项目

2023.10 - 2025.02

担任拉美履约研发部的前端负责人
管理组内项目需求和日常需求的前端开发工作
主导拉美前端组的架构设计、建立和完善代码规范、开发自动化提效工具等工作

2025.02 - 至今

担任仓储 园区业务线前端负责人


## 2. 我在过去一年的业绩贡献


1. 主导完成了「PDA虚拟键盘禁用」技术方案落地，该方案收益：每月/5.2w人民币。
2. 主导完成了「电子秤称重输入限制」技术方案落地，该方案收益：每年/3w人民币。
3. 2024年负责开发的需求中，无P5级别以上的故障，全年Bug21个，全年负责需求127个，平均每个需求的Bug数量为0.16个,代码交付质量高。
4. 作为拉美前端负责人，在日常工作中，主动进行系统优化，包括LCP&CLPS优化，加载资源优化等，不断提升系统的性能和用户体验。
5. 作为「标准化项目」的拉美前端负责人，主导WPM/WIS/WMD/OSM等系统的标准化开发流程制定与实施，保障项目稳定落地，推动标准化体系在跨区域部署中的稳定运行。
6. 2024年，在仓储研发团队效能工作中表现优异，获「效能先锋奖」。
7. 2024年所参与需求，每月累计至少节省15.9w人民币。


1. 主导完成了「电子秤称重输入限制」技术方案落地，该方案收益：每月/3w人民币。

2. 主导完成了「PDA虚拟键盘禁用」技术方案落地，该方案收益：每月/5.2w人民币。

3. 2024年负责开发的需求中，无P5级别以上的故障，全年Bug21个，全年负责需求127个，平均每个需求的Bug数量为0.16个,代码交付质量高。

4. 作为拉美前端负责人，在日常工作中，主动进行系统优化，包括LCP&CLPS优化，加载资源优化等，不断提升系统的性能和用户体验。

5. 「标准化项目」中，主导WPM/WIS/WMD/OSM等系统在拉美的前端开发工作，成功保障项目在拉美数据中心的完整落地与稳定上线。

6. 2024年所参与需求，每月累计至少节省15.9w人民币。

作为「标准化项目」的拉美前端负责人，主导WPM/WIS/WMD/OSM等系统的标准化开发流程制定与实施，保障项目按时落地，推动标准化体系在跨区域部署中的稳定运行

## 3. 我的方法和我的能力

### 3.1 「电子秤称重输入限制」

OFC-184579	巴西3P系统在打包环节限制只允许计重秤输入重量【GZSCC-23473-1】
OFC-190443	电子秤输入 埋点日志和兼容优化
OFC-191926	1P/SFS仓库 电子秤输入限制
OFC-238504	巴西wms系统禁止打包称重重量手动录入【南美履约大区】【Rebekka Pang】

315 万元/月

针对巴西仓库操作员随意称重的问题，产品希望通过技术手段限制只能通过称重机输入重量，禁止手动输入。Amigo深入分析了称重机的串口协议，成功实现了称重机与WPM系统的串口通信，实现了重量数据的实时传输，彻底解决了仓库操作员手动修改重量的问题。该方案两仓在落地后，每月节省1人月，1年节省3w人民币。

「电子秤称重输入限制」

1. 定义问题：从业务漏洞到技术治理
背景与痛点：
巴西仓库因操作员手动输入称重数据，存在人为篡改风险，导致包裹重量异常，影响仓库运营效率。业务团队希望通过技术手段限制只能通过称重机输入重量，禁止手动输入。

2. 分析问题：能辨——构建MECE治理框架
系统性拆解：

设备层：与厂商协同解析电子秤通信协议
控制层：设计动态开关（强制模式/降级模式），应对硬件兼容性等异常场景；
监控层：埋点日志，确保操作溯源能力；
治理层：建立“数据异常检测→企微告警→配置修复”的闭环运维机制。

MECE验证：
通过设备接入、数据流控制、行为监控、治理响应四层架构设计，覆盖从数据采集到业务治理的全链路风险点。

3. 解决问题：能燃——点燃跨域协作链条
核心突破点：

1. 跨域协作：跨团队协同
2. 开关设计：功能开关设计，实现「强制锁定电子秤/临时开放手动输入」模式切换，保障极端情况业务连续性；
3. 埋点与根因分析：通过UEM的日志采集模块，结构化记录设备通信状态（响应延迟、数据校验失败等4类事件）；
4. 主动治理体系：开发企微机器人告警功能，实时推送未配置设备清单至仓管人员，大幅减少配置修复时效；

专业能力：高可用架构设计（动态降级）、全链路监控体系搭建、数据驱动业务治理；

1. 成果：


算了，写

场景：团队成员数量逐步增加，但开发质量上不去，测试bug不断增长，需要对开发质量进行提升
分析：业务不熟悉导致的bug数不少，
行为：1. (能为) 规范强化：与FTIC研发规范对齐；持续迭代前端组内规范，例如：开发规范、code review规范等
     1. (能燃) 组织业务学习和输出巡检操作手册
     2. (能辩) 加强codereview工作：每周建立代码评审会议纪要
     3. (能为) 完善需求评审规范，补充了前端反讲环节，保障交付质量
结果：前端新人的测试阶段代码缺陷率从9月的60.30%，下降到11月的15.54%，有显著提升。





场景：巴西仓库因操作员手动输入称重数据，存在人为篡改风险。业务团队希望通过技术手段限制只能通过电子秤输入重量，禁止手动输入。
分析：(跨部门协作)与厂商协同解析电子秤通信协议；
     (技术调研) WebUSB API 的浏览器支持与电子秤串口设备通讯格式分析；
解决：系统性拆解为4层设计
   1. (设备层)通信设计：电子秤与WPM系统的串口通信与识别； 
   2. (控制层)开关设计：功能开关设计，保障极端情况业务连续性；
   3. (监控层)埋点与分析：通过UEM 埋点，分析操作数据，迭代优化；
   4. (治理层)主动治理体系：开发企微机器人告警功能，实时推送未配置设备清单至仓管人员，大幅减少配置修复时效；
收益：方案在巴西两个仓库实施后，每月节省1人月，年节省3万元人民币；该技术方案将推广至其他仓库。










### 3.2 「PDA虚拟键盘禁用」

OFC-216445【前端需求】「urgent」-PDA弹窗确认后聚焦不升起键盘
OFC-241331	PDA-去掉页面键盘自动拉起配置选项/页面是否允许复制通过权限控制
5.2 万元/月

在巴西3P转1P过程中，业务团队在巴西现场发现部分PDA设备无法设置禁用虚拟键盘，导致界面显示信息受限，影响了操作员的工作效率。面对PDA硬件不提供直接支持的情况，Amigo为业务团队提供了前端技术方案，成功从软件层面弥补了硬件缺陷，为业务团队提供了有力支持。



场景：巴西仓3P转1P过程中，操作员使用PDA设备时因虚拟键盘无法禁用（硬件限制），界面信息遮挡率高达40%

分析：
行为：
结果：

场景：下半年进入标准化阶段中，面对各功能数万个BBL词条，跨数据中心的部署应用，为了弥补人员逐条梳理遗漏，复杂的检查缺陷。实现自动化导出词条。
方式：
1. 规划化工作流程：与产品、后端、测试多方共同协定了一套标准化的工作流程，确保了	多语言翻译管理的高效性和准确性。
2. 提效工具开发：主导开发了一系列的赋能工具，包括Chrome插件和VSCode编辑器插件。	 这些工具能够自动导出上线需求中的未翻译词条以及系统中的全量词条，大幅减少手工操作的时间和出错率，提升了团队时效。
收益：节省人力成本，在标准化阶段中各数据中心应用（国内/北美/欧洲/拉美/欧洲）累计节省8人天。
产出：
1. 输出规范文档
2. 开发Chrome插件 
3. 开发VSCode编辑器插件


### 3.3 带教

场景：团队成员数量逐步增加，但开发质量上不去，测试bug不断增长，需要对开发质量进行提升
分析：绝大部分是由于部分团队成员在业务流程不熟悉，相关规定或规范不严格遵守，导致各类问题
行为：1. (能为) 规范强化：与FTIC研发规范对齐；持续迭代前端组内规范，例如：开发规范、code review规范等；
   2. (能燃) 组织业务学习和输出巡检操作手册；
   3. (能辩) 加强codereview工作：每周建立代码评审会议纪要；
   4. (能为) 完善需求评审规范，补充了前端反讲环节，保障交付质量
结果：前端新人的测试阶段代码缺陷率从9月的60.30%，下降到11月的15.54%，有显著提升。


### 3.4 BBL工具
效能工具：BBL词条梳理校准工具
案例介绍：下半年进入标准化阶段中，面对各功能数万个BBL词条，跨数据中心的部署应用，为了弥补人员逐条梳理遗漏，复杂的检查缺陷，实现前端代码库中的BBL词条，与BBL系统中的词条做对比，快速整理出缺失词条给产品，节省人力成本；在标准化各数据中心应用累计节省8人天；

场景：下半年进入标准化阶段中，面对各功能数万个BBL词条，跨数据中心的部署应用。
分析：
行为：
结果：节省人力成本，在标准化各数据中心应用累计节省8人天；

### 3.3 lvp


## 4. 我对SHEIN的承诺

1. 适应变化，拥抱变化
快速响应组织架构变更和业务需求，保障园区业务线的稳定运行。

1. 稳定性建设
完善园区前端系统的稳定性建设体系，包括预案演练、告警梳理、巡检机制等。

1. 用户体验
用户体验优化常态化，提高用户满意度。

1. 技术探索
赋能工具开发：ai与前端工作流的结合，探索ai对前端研发效能的提升。
