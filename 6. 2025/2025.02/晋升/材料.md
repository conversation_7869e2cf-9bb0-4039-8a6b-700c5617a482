## 飞跃之星申报

飞跃之星申报人员 - Amigo Zheng

突出贡献：
1. 针对巴西仓库操作员随意称重的问题，产品希望通过技术手段限制只能通过称重机输入重量，禁止手动输入。Amigo深入分析了称重机的串口协议，成功实现了称重机与WPM系统的串口通信，实现了重量数据的实时传输，彻底解决了仓库操作员手动修改重量的问题。该方案两仓在落地后，每月节省1人月，1年节省3w人民币。
2. 在巴西3P转1P过程中，业务团队在巴西现场发现部分PDA设备无法设置禁用虚拟键盘，导致界面显示信息受限，影响了操作员的工作效率。面对PDA硬件不提供直接支持的情况，Amigo为业务团队提供了前端技术方案，成功从软件层面弥补了硬件缺陷，为业务团队提供了有力支持。

这项的收益：减少mot用户查询耗时2s/人次，子包裹查询周均查询接口3.8w。38000 * 2* 365/60/60，减少7704h。每小时工时14元（2500/(22*8)），全年大概节省10w人民币。

3. 2024年负责开发的需求中，无P5级别以上的故障，全年Bug21个，全年负责需求127个，平均每个需求的Bug数量为0.16个,代码交付质量高。
4. 作为拉美前端负责人，在日常工作中，主动进行系统优化，包括LCP&CLPS优化，加载资源优化等，不断提升系统的性能和用户体验。
5. 在WPM北美本地化部署期间，独立完成了前端项目相关事项的推进，确保系统顺利部署上线，为业务拓展提供了有力支持。
6. WPM标准化过程中，主动承担项目推进工作，负责整个技术方案的落地，成功实现系统的标准化部署上线，有效提升了系统的整体性能和稳定性。
7. 2024年所参与需求，每月累计至少节省15.9w人民币。


## WMS效能先锋奖
效能工具：BBL词条梳理校准工具
案例介绍：下半年进入标准化阶段中，面对各功能数万个BBL词条，跨数据中心的部署应用，为了弥补人员逐条梳理遗漏，复杂的检查缺陷，实现前端代码库中的BBL词条，与BBL系统中的词条做对比，快速整理出缺失词条给产品，节省人力成本；在标准化各数据中心应用累计节省8人天；

@Anjou Wang 安久哥，前端这边有一个

vscode 插件：bbl-toolkit

gitlab 仓库：
https://gitlab.sheincorp.cn/ofla/la-front-end/vscode-toolkit

功能点：
该插件用于扫描前端代码库中的BBL词条，与BBL系统中的词条做对比，快速整理出缺失词条给产品，节省人力成本

收益：
目前已在仓储前端中推广，在标准化项目中国内和海外各区均有使用，预估一年节省8个人日