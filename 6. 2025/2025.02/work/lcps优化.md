//*[@id='parcelPackage']/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1] (7400)
<div class="style__bulkSeriesNo--auEYtYBq">篮子序号:<span class="style__bulkSeriesNoColorYellow--iQjDm9h8"></span></div>

//*[@id='parcelPackage']/div[1]/div[1]/div[1]/div[1]/div[1] (596)
<div class="style__commonHeaderLeft--_IL7AC8v"><div class="style__commonHeaderLeftInput--Q2LJ02Ey"><label class="so-input so-input-disabled so-input-inline currentSubPackageNo style__commonHeaderItem--drFvVJgR" style="width: 200px;"><div class="so-input-title-box"><div class="so-input-title-box-title so-input-title-box-top">子包裹号/拣货容器号</div><div class="so-input-title-box-content"><input disabled="" class="so-input-title-box-hidable so-input-title-box-item" type="text" value=""></div><div class="so-input-title-box-place"><div class="so-input-title-box-title">子包裹号/拣货容器号</div></div></div></label><label class="so-input so-input-disabled so-input-inline weight style__commonHeaderItem--drFvVJgR" style="width: 200px;"><div class="so-input-title-box"><div class="so-input-title-box-title so-input-title-box-top">称重</div><div class="so-input-title-box-content"><input disabled="" class="so-input-title-box-hidable so-input-title-box-item" type="text" value=""></div><div class="so-input-title-box-place"><div class="so-input-title-box-title">称重</div></div></div></label><label class="so-input so-input-disabled so-input-inline checkCode style__commonHeaderItem--drFvVJgR" style="width: 200px;"><div class="so-input-title-box"><div class="so-input-title-box-title so-input-title-box-top">扫描校验码</div><div class="so-input-title-box-content"><input disabled="" class="so-input-title-box-hidable so-input-title-box-item" type="text" value=""></div><div class="so-input-title-box-place"><div class="so-input-title-box-title">扫描校验码</div></div></div></label><button disabled="" type="button" class="so-button so-button-primary so-button-disabled"><span>提交异常</span></button></div><div class="style__btnDivStyle--hOpoXQaY"><button class="so-button so-button-primary style__upOrDownStyle--w4BP5XVS" type="button"><span>上机</span></button></div></div>

//*[@id='parcelPackage']/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1] (164)


// 
1. 导出下载 取消默认搜索 [✅]
2. 无头浏览器判断
3. 合并打包 缓存

1. 工作成果展示

apollo 参数
TODAY_WORDLOAD_CONTROL_SWITCH 

参数管理
LIMIT_SCALE_WAREHOUSE_LIST

改动后，“下次一定”生效
/outbound-mgt/package/parcel-package

ZY98