[{"_math_route_content_id": "wms-std-front_af091538-e781-4716-ac5d-734de93e2a0c", "_route_content_id": "wms-std-front_4a7ca33a-394e-48d6-8cb1-5d814032025c", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/wms-standard.html", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "SHEIN_ABGROUP", "values": "^x$"}]}], "priority": 1, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-5250", "rule_model": "normal", "type": "http"}, {"_math_route_content_id": "wms-std-front_4a7ca33a-394e-48d6-8cb1-5d814032025c", "_route_content_id": "wms-std-front_af091538-e781-4716-ac5d-734de93e2a0c", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/wms-standard.html", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "SHEIN_ABGROUP", "values": "^[0-9]$|^[1-8][0-9]$|^9[0-9]$|^100$"}]}], "priority": 2, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-5251", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-std-front_06a24211-ccef-4138-b55e-26ae0a1207e7", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/wms-standard.html", "predicates": [], "priority": 3, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-5252", "rule_model": "normal", "type": "http"}, {"_route_alias_name": "后端 蓝", "_route_content_id": "wms-std-front_d8b4c797-8cb9-408c-a4a5-34bdfe5bd4a8", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {"addRequestHeaderFilter": {"filterName": "addRequestHeaderFilter", "value": [{"key": "uberctx-blue-green", "values": "blue"}]}}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/osm/**", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "SHEIN_ABGROUP", "values": "^[0-9]$|^[1-9][0-9]$|^100$"}]}], "priority": 4, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "wgw-la-pc", "targetType": "service", "weight": 100}, {"loadbalancer": "LEAST_ACTIVE", "root": false, "sub": 1, "target": "blue_green=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-5253", "rule_model": "normal", "type": "http"}, {"_route_alias_name": "后端 绿", "_route_content_id": "wms-std-front_e20974c6-aa92-410d-a337-fa643a19859c", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {"addRequestHeaderFilter": {"filterName": "addRequestHeaderFilter", "value": [{"key": "uberctx-blue-green", "values": "green"}]}}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/osm/**", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "SHEIN_ABGROUP", "values": "^x$"}]}], "priority": 5, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "wgw-la-pc", "targetType": "service", "weight": 100}, {"loadbalancer": "LEAST_ACTIVE", "root": false, "sub": 1, "target": "blue_green=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-5254", "rule_model": "normal", "type": "http"}, {"_route_alias_name": "后端 兜底", "_route_content_id": "wms-std-front_496cc7a8-88b8-48fc-9af0-96384057c483", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {"addRequestHeaderFilter": {"filterName": "addRequestHeaderFilter", "value": [{"key": "uberctx-blue-green", "values": "green"}]}}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/osm/**", "predicates": [], "priority": 6, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "wgw-la-pc", "targetType": "service", "weight": 100}, {"loadbalancer": "LEAST_ACTIVE", "root": false, "sub": 1, "target": "blue_green=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-5255", "rule_model": "normal", "type": "http"}]