[{"_route_content_id": "wms-la-front_7e1122e9-bb08-4934-99c0-b80a661aadeb", "advance_route_fail_strategy": "request_others_service_instances", "detail_type": "normal", "fail_over_enable": false, "filters": {"addRequestHeaderFilter": {"filterName": "addRequestHeaderFilter", "value": [{"key": "Host", "values": "wms-la-std.com"}]}}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/wms-standard.html", "predicates": [], "priority": 1, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targetType": "service", "weight": 100}, {"loadbalancer": "LEAST_ACTIVE", "root": false, "sub": 1, "target": "gateway_group=browser", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4678", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_3f8a2118-a496-4b4e-ad0b-2e46bc373ec7", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/baseDist/wmsBase.json", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/wmsBase.json", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "green"}]}], "priority": 2, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4679", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_a041419c-e647-4e0a-b083-d989f3c1215e", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/baseDist/wmsBase.json", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/wmsBase.json", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "blue"}]}], "priority": 3, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4680", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_0c7f9949-66eb-40a8-a069-96402b66d006", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/baseDist/wmsBase.json", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/wmsBase.json", "predicates": [], "priority": 4, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4681", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_7c8012c7-c93a-415b-86d7-aba84540110e", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/laDist/microLa.json", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/microLa.json", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "green"}]}], "priority": 5, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4682", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_8dc3f70a-198e-4f9b-9acb-c23ef4d39df5", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/laDist/microLa.json", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/microLa.json", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "blue"}]}], "priority": 6, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4683", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_a6ec7ee7-d088-4604-88bb-82fff868dac6", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/laDist/microLa.json", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/microLa.json", "predicates": [], "priority": 7, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4684", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_afa7edd7-357b-4019-bde7-b8471193b901", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/inboundDist/inbound.json", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/inbound.json", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "green"}]}], "priority": 8, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4685", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_190bfeb7-206a-433f-8e27-76185e1c0557", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/inboundDist/inbound.json", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/inbound.json", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "blue"}]}], "priority": 9, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4686", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_abfb127d-4eb4-438e-b1a7-6bfbfe6b21b7", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/inboundDist/inbound.json", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/inbound.json", "predicates": [], "priority": 10, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4687", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_30f69f68-23a4-4d0c-b090-1b771a32977f", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/outboundDist/outbound.json", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/outbound.json", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "green"}]}], "priority": 11, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4688", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_cb404650-a7f4-4e51-aa19-2a6d90bddc1a", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/outboundDist/outbound.json", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/outbound.json", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "blue"}]}], "priority": 12, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4689", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_b14840b0-d08e-4b1b-897a-e1acdb538550", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/outboundDist/outbound.json", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/outbound.json", "predicates": [], "priority": 13, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4690", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_95161a9d-b7bc-4583-8686-4f2d3b527131", "advance_route_fail_strategy": "request_others_service_instances", "detail_type": "normal", "fail_over_enable": false, "filters": {"addRequestHeaderFilter": {"filterName": "addRequestHeaderFilter", "value": [{"key": "Host", "values": "wms-la-inbound.com"}]}}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/inbound-standard.html", "predicates": [], "priority": 14, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targetType": "service", "weight": 100}, {"loadbalancer": "LEAST_ACTIVE", "root": false, "sub": 1, "target": "gateway_group=browser", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4691", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_00efbbe4-76da-43c6-82a2-50467b90ca20", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/baseDist/wmsBase.html", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/wmsBase.html", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "green"}]}], "priority": 15, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4692", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_a1211113-fbc3-4269-b3ff-9dd140c64fd6", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/baseDist/wmsBase.html", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/wmsBase.html", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "blue"}]}], "priority": 16, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4693", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_4d2676e5-29d0-4c24-b436-b4528c46f52a", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/baseDist/wmsBase.html", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/wmsBase.html", "predicates": [], "priority": 17, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4694", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_839c4310-1131-4f93-b400-4a7cb0e94558", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/laDist/laIndex.html", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/laIndex.html", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "green"}]}], "priority": 18, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4695", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_66290f0e-290d-4dae-a54e-ed937bf5b977", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/laDist/laIndex.html", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/laIndex.html", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "blue"}]}], "priority": 19, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4696", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_3558ff2a-fbea-4a1e-91bf-7a2ae4d040a4", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/laDist/laIndex.html", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/laIndex.html", "predicates": [], "priority": 20, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4697", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_b7ac38dc-9c0e-43d5-8360-4e663eb76c33", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/inboundDist/inboundIndex.html", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/inboundIndex.html", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "green"}]}], "priority": 21, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4698", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_8113d903-f7df-4ca2-ae92-78e9c0f77a52", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/inboundDist/inboundIndex.html", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/inboundIndex.html", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "blue"}]}], "priority": 22, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4699", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_aba690ed-8532-4244-9f72-06cfa25701bc", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/inboundDist/inboundIndex.html", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/inboundIndex.html", "predicates": [], "priority": 23, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4700", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_102ead73-b9c1-4df0-a876-b07d054535c3", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/outboundDist/outboundIndex.html", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/outboundIndex.html", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "green"}]}], "priority": 24, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4701", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_579bdb39-5325-4fa6-9ccb-7dd6f2b2658e", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/outboundDist/outboundIndex.html", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/outboundIndex.html", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "blue"}]}], "priority": 25, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4702", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_7e6a79a4-c751-4281-ad35-d4a4a21d86bb", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_path": "/outboundDist/outboundIndex.html", "mapping_strategy": "appoint", "offset": 0, "parent_priority": 1, "path": "/outboundIndex.html", "predicates": [], "priority": 26, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4703", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_90388cdb-d03b-4c1c-b75d-c8fdd2bb35af", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/**/*.js", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "green"}]}], "priority": 27, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4704", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_7c531af7-9b55-4f98-9c47-248588d4f3b8", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/**/*.js", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "blue"}]}], "priority": 28, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4705", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_a87d727a-9a14-4578-99d1-bd6f3824413a", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/**/*.js", "predicates": [], "priority": 29, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4706", "rule_model": "normal", "type": "http"}, {"_math_route_content_id": "wms-la-front_42cf7d8a-79af-4596-ac91-4be929851681", "_route_content_id": "wms-la-front_e601b1f1-c393-41d1-847a-395b71abbdef", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "SHEIN_ABGROUP", "values": "^[0-9]$|^[1-8][0-9]$|^9[0-9]$|^100$"}]}], "priority": 30, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4707", "rule_model": "normal", "type": "http"}, {"_math_route_content_id": "wms-la-front_e601b1f1-c393-41d1-847a-395b71abbdef", "_route_content_id": "wms-la-front_42cf7d8a-79af-4596-ac91-4be929851681", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "SHEIN_ABGROUP", "values": "^x$"}]}], "priority": 31, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4708", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_ae8fece5-ecb1-4df2-b3d0-86d906d67393", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/", "predicates": [], "priority": 32, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "canary=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4709", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_a6a44ec6-f797-4e6b-877c-4ca7a21103e1", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {"addRequestHeaderFilter": {"filterName": "addRequestHeaderFilter", "value": [{"key": "uberctx-blue-green", "values": "green"}]}}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/**", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "green"}]}], "priority": 33, "request_time_out": 300000, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "wgw-la-pc", "targetType": "service", "weight": 100}, {"loadbalancer": "LEAST_ACTIVE", "root": false, "sub": 1, "target": "blue_green=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4710", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_9bde1643-16f9-4f5c-8a58-26d90f63a976", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {"addRequestHeaderFilter": {"filterName": "addRequestHeaderFilter", "value": [{"key": "uberctx-blue-green", "values": "blue"}]}}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/**", "predicates": [{"predicateName": "cookieRouteRulePredicate", "value": [{"key": "canary", "values": "blue"}]}], "priority": 34, "request_time_out": 300000, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "wgw-la-pc", "targetType": "service", "weight": 100}, {"loadbalancer": "LEAST_ACTIVE", "root": false, "sub": 1, "target": "blue_green=blue", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4711", "rule_model": "normal", "type": "http"}, {"_route_content_id": "wms-la-front_b6533c9b-6911-4de2-ba72-203a952ca914", "advance_route_fail_strategy": "request_others_service_instances", "fail_over_enable": false, "filters": {"addRequestHeaderFilter": {"filterName": "addRequestHeaderFilter", "value": [{"key": "uberctx-blue-green", "values": "green"}]}}, "host_strategy": "advance", "ignore_original_dc_header": true, "mapping_host": "", "mapping_strategy": "strip", "offset": 0, "parent_priority": 1, "path": "/**", "predicates": [], "priority": 35, "request_time_out": 300000, "route_target": [{"loadbalancer": "LEAST_ACTIVE", "root": true, "target": "wgw-la-pc", "targetType": "service", "weight": 100}, {"loadbalancer": "LEAST_ACTIVE", "root": false, "sub": 1, "target": "blue_green=green", "targetType": "consul_tag", "weight": 100}], "rue_id": "null-4712", "rule_model": "normal", "type": "http"}]