# wpm标准化

## 事项
- [] wpm标准化
  - [] 前端切换开关: 劫持路由(todo)
  - [✅] wms: feat/std-wpm 审计接口
  - [✅] wms: feat/std-wpm 子应用处理 导出下载的路由
  - [✅] wms: 路径跳转检查
  - [✅] mot: 路径跳转检查
  - [✅] 检查纯前端的校验权限: 北美有同步到位，没问题
- [] wpm标准化: 中东
  - [✅] wms: paas 
  - [✅] wms: mesh
  - [✅] mot: paas
  - [✅] mot: mesh
- [] wpm标准化：拉美
  - [] 权限节点
    - [✅] mot-la img-icon
  - [] 需求同步
    - [] wms
    - [] mot
  - 检查新增的页面
    - wms
      - wpm-standard/management/basic-data/warehouse-area-coordinate-configuration
      - wpm-standard/system/outbound-cfg/picking-group-capacity-configuration
    - mot
      - /wpm-standard/in-warehouse/in-location-stock-inventory
      - /wpm-standard/in-warehouse/in-location-stock-query

## 需求同步
- 需求同步
    - wms
      - [✅] pre-master-12-10
      - [✅] pre-master-12-16
      - [✅] pre-master-12-19
      - [✅] pre-master-12-26
      - [✅] Merge branch 'pre-master-wstd-wis' into 'master' 
      - [✅] pre-master-01-02
      - [✅] Merge branch 'pre-master-prefix' into 'master' 
      - [✅] pre-master-01-09
      - [✅] Merge branch 'pre-master-01-14' into 'master'
      - [✅] Merge branch 'pre-master-1p-cluster-picking' into 'master'
      - [✅] Merge branch 'pre-master-wstd-osm' into 'master' 
      - [✅] Merge branch 'pre-master-01-16' into 'master'
      - [✅] Merge branch 'pre-master-wstd-wmd' into 'master' 
      - [✅] Merge branch 'pre-master-lock-warehouse' into 'master'
      - [✅] Merge branch 'pre-master-01-21' into 'master'
    - mot
      - [✅] pre-master-12-10
      - [✅] pre-master-12-16
      - [✅] pre-master-12-19
      - [✅] Merge branch 'pre-master-password' into 'master'
      - [✅] pre-master-12-26
      - [✅] Merge branch 'pre-master-scanInput' into 'master' 
      - [✅] Merge branch 'pre-master-prefix' into 'master' 
      - [✅] Merge branch 'pre-master-wstd-osm' into 'master'
      - [✅] Merge branch 'pre-master-01-16' into 'master' 
      - [✅] Merge branch 'pre-master-01-21' into 'master'

- 这段时间的新增页面

## wms 文件路径
// micro-la
  '/management', //  后台管理
  '/personnel-mgt', // 人员管理
  '/stock-manage-new', // 商品管理系统/库内管理/库存管理

// wpm
  '/board-mgt', // 包裹管理系统/运营监控
  '/exception-mgt', // 包裹管理系统/异常管理
  '/inbound-mgt', // 包裹管理系统/入库管理
  '/outbound-mgt', // 包裹管理系统/出库管理
  '/package-mgt', // 包裹管理系统/包裹管理
  '/system', // 包裹管理系统/系统配置
  '/shipping-mgt', // 包裹管理系统/发货管理
  '/developer-tools', // 包裹管理系统/开发者工具

## mot 文件路径

// mot-la
  

// wpm
  src/pages/wpm-standard/in-warehouse
  src/pages/wpm-standard/inbound
  src/pages/wpm-standard/outbound
  src/pages/wpm-standard/sorting-center-management

## 分支对比
wms:
https://gitlab.sheincorp.cn/ofla/wms-la/-/commits/master?search=+into+%27master%27

wpm-std-front: 
https://gitlab.sheincorp.cn/wms/front/standard/wpm-std-front/-/branches?state=all&sort=updated_desc&search=pre-master-wpm

----------------------

mot:
https://gitlab.sheincorp.cn/ofla/mot-la/-/commits/master?search=+into+%27master%27

mot-wpm-std-front:
https://gitlab.sheincorp.cn/wms/front/standard/mot-wpm-std-front/-/branches?state=all&sort=updated_desc&search=pre-master-wpm


## 需求分支
wms-la:
feat/std-wpm

mot-la:
feat/std-wpm

wms-wpm:
- pre-master-wpm-01-21
- pre-master-wpm-traceId
- OFC-231984-wpm

mot-wpm
- pre-master-wpm-01-21
- pre-master-wpm-traceId

## 路由替换列表
was-la MENU_RULE_REPLACE_SWITCH

pc-查询路由替换列表
/system/authRule/query_rule_replace_list

pda-查询路由替换列表
/system/pda/query_rule_replace_list
  - wpm中没有发现需要跳转的页面
  - (没用到)
