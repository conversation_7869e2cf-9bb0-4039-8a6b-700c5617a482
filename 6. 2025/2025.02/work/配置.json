[{"is_open": 1, "old_rule": "/developer-tools/elasticsearch", "new_rule": "/wpm-standard/developer-tools/elasticsearch"}, {"is_open": 1, "old_rule": "/developer-tools/plan-picking-group-query", "new_rule": "/wpm-standard/developer-tools/plan-picking-group-query"}, {"is_open": 1, "old_rule": "/developer-tools/wpm-tools", "new_rule": "/wpm-standard/developer-tools/wpm-tools"}, {"is_open": 1, "old_rule": "/system/operate-link-duration-cfg", "new_rule": "/wpm-standard/system/operate-link-duration-cfg"}, {"is_open": 1, "old_rule": "/system/device-cfg/auto-device-address-config", "new_rule": "/wpm-standard/system/device-cfg/auto-device-address-config"}, {"is_open": 1, "old_rule": "/system/device-cfg/auto-device-config", "new_rule": "/wpm-standard/system/device-cfg/auto-device-config"}, {"is_open": 1, "old_rule": "/system/inbound-cfg/inbound-check", "new_rule": "/wpm-standard/system/inbound-cfg/inbound-check"}, {"is_open": 1, "old_rule": "/system/outbound-cfg/exception-take-down-config", "new_rule": "/wpm-standard/system/outbound-cfg/exception-take-down-config"}, {"is_open": 1, "old_rule": "/system/outbound-cfg/location-suggest-limit-cfg", "new_rule": "/wpm-standard/system/outbound-cfg/location-suggest-limit-cfg"}, {"is_open": 1, "old_rule": "/system/outbound-cfg/oversea-time-config", "new_rule": "/wpm-standard/system/outbound-cfg/oversea-time-config"}, {"is_open": 1, "old_rule": "/system/outbound-cfg/force-order-configuration", "new_rule": "/wpm-standard/system/outbound-cfg/force-order-configuration"}, {"is_open": 1, "old_rule": "/system/outbound-cfg/circle-order-rule-config", "new_rule": "/wpm-standard/system/outbound-cfg/circle-order-rule-config"}, {"is_open": 1, "old_rule": "/system/outbound-cfg/picking-group-strategy", "new_rule": "/wpm-standard/system/outbound-cfg/picking-group-strategy"}, {"is_open": 1, "old_rule": "/system/outbound-cfg/picking-group-configuration", "new_rule": "/wpm-standard/system/outbound-cfg/picking-group-configuration"}, {"is_open": 1, "old_rule": "/system/outbound-cfg/warehouse_partition_config", "new_rule": "/wpm-standard/system/outbound-cfg/warehouse_partition_config"}, {"is_open": 1, "old_rule": "/board-mgt/oversea-main-board", "new_rule": "/wpm-standard/board-mgt/oversea-main-board"}, {"is_open": 1, "old_rule": "/board-mgt/picking-sorting-board", "new_rule": "/wpm-standard/board-mgt/picking-sorting-board"}, {"is_open": 1, "old_rule": "/board-mgt/call-task-board", "new_rule": "/wpm-standard/board-mgt/call-task-board"}, {"is_open": 1, "old_rule": "/package-mgt/combine-suggest", "new_rule": "/wpm-standard/package-mgt/combine-suggest"}, {"is_open": 1, "old_rule": "/package-mgt/force-order-record", "new_rule": "/wpm-standard/package-mgt/force-order-record"}, {"is_open": 1, "old_rule": "/package-mgt/child-package-list", "new_rule": "/wpm-standard/package-mgt/child-package-list"}, {"is_open": 1, "old_rule": "/package-mgt/package-list", "new_rule": "/wpm-standard/package-mgt/package-list"}, {"is_open": 1, "old_rule": "/package-mgt/cross-border-parcel", "new_rule": "/wpm-standard/package-mgt/cross-border-parcel"}, {"is_open": 1, "old_rule": "/package-mgt/warehouse-inspection-list", "new_rule": "/wpm-standard/package-mgt/warehouse-inspection-list"}, {"is_open": 1, "old_rule": "/inbound-mgt/receive-detail-management", "new_rule": "/wpm-standard/inbound-mgt/receive-detail-management"}, {"is_open": 1, "old_rule": "/inbound-mgt/shelf-detail-management", "new_rule": "/wpm-standard/inbound-mgt/shelf-detail-management"}, {"is_open": 1, "old_rule": "/inbound-mgt/fm-mgt", "new_rule": "/wpm-standard/inbound-mgt/fm-mgt"}, {"is_open": 1, "old_rule": "/inbound-mgt/sort-center-warehouse-relation", "new_rule": "/wpm-standard/inbound-mgt/sort-center-warehouse-relation"}, {"is_open": 1, "old_rule": "/inbound-mgt/transport-shelf-print", "new_rule": "/wpm-standard/inbound-mgt/transport-shelf-print"}, {"is_open": 1, "old_rule": "/inbound-mgt/transport-shelf-detail-management", "new_rule": "/wpm-standard/inbound-mgt/transport-shelf-detail-management"}, {"is_open": 1, "old_rule": "/inbound-mgt/transport-shelf-management", "new_rule": "/wpm-standard/inbound-mgt/transport-shelf-management"}, {"is_open": 1, "old_rule": "/inbound-mgt/transfer-vehicle-task", "new_rule": "/wpm-standard/inbound-mgt/transfer-vehicle-task"}, {"is_open": 1, "old_rule": "/inbound-mgt/transfer-vehicle", "new_rule": "/wpm-standard/inbound-mgt/transfer-vehicle"}, {"is_open": 1, "old_rule": "/inbound-mgt/inbound-weight-check", "new_rule": "/wpm-standard/inbound-mgt/inbound-weight-check"}, {"is_open": 1, "old_rule": "/inbound-mgt/inbound-big-check", "new_rule": "/wpm-standard/inbound-mgt/inbound-big-check"}, {"is_open": 1, "old_rule": "/inbound-mgt/inbound-volume-re-check", "new_rule": "/wpm-standard/inbound-mgt/inbound-volume-re-check"}, {"is_open": 1, "old_rule": "/package-mgt/package-shift-order", "new_rule": "/wpm-standard/package-mgt/package-shift-order"}, {"is_open": 1, "old_rule": "/package-mgt/package-stock-inquiry", "new_rule": "/wpm-standard/package-mgt/package-stock-inquiry"}, {"is_open": 1, "old_rule": "/outbound-mgt/picking-management/picking-group-management", "new_rule": "/wpm-standard/outbound-mgt/picking-management/picking-group-management"}, {"is_open": 1, "old_rule": "/outbound-mgt/picking-management/picking-task-query", "new_rule": "/wpm-standard/outbound-mgt/picking-management/picking-task-query"}, {"is_open": 1, "old_rule": "/outbound-mgt/picking-management/picking-task-detail", "new_rule": "/wpm-standard/outbound-mgt/picking-management/picking-task-detail"}, {"is_open": 1, "old_rule": "/outbound-mgt/picking-management/pick-container-status-query", "new_rule": "/wpm-standard/outbound-mgt/picking-management/pick-container-status-query"}, {"is_open": 1, "old_rule": "/outbound-mgt/sowing-management/sowing-detail", "new_rule": "/wpm-standard/outbound-mgt/sowing-management/sowing-detail"}, {"is_open": 1, "old_rule": "/outbound-mgt/package/package-record", "new_rule": "/wpm-standard/outbound-mgt/package/package-record"}, {"is_open": 1, "old_rule": "/outbound-mgt/package/parcel-package", "new_rule": "/wpm-standard/outbound-mgt/package/parcel-package"}, {"is_open": 1, "old_rule": "/outbound-mgt/sowing-management/first-sowing-detail", "new_rule": "/wpm-standard/outbound-mgt/sowing-management/first-sowing-detail"}, {"is_open": 1, "old_rule": "/outbound-mgt/sowing-management/pre-sowing-information-management", "new_rule": "/wpm-standard/outbound-mgt/sowing-management/pre-sowing-information-management"}, {"is_open": 1, "old_rule": "/exception-mgt/exception-temporary-search", "new_rule": "/wpm-standard/exception-mgt/exception-temporary-search"}, {"is_open": 1, "old_rule": "/exception-mgt/exception-details-search", "new_rule": "/wpm-standard/exception-mgt/exception-details-search"}, {"is_open": 1, "old_rule": "/exception-mgt/exception-search", "new_rule": "/wpm-standard/exception-mgt/exception-search"}, {"is_open": 1, "old_rule": "/shipping-mgt/replace-form", "new_rule": "/wpm-standard/shipping-mgt/replace-form"}, {"is_open": 1, "old_rule": "/shipping-mgt/package-boxing", "new_rule": "/wpm-standard/shipping-mgt/package-boxing"}, {"is_open": 1, "old_rule": "/outbound-mgt/delivery-box-detail", "new_rule": "/wpm-standard/outbound-mgt/delivery-box-detail"}, {"is_open": 1, "old_rule": "/outbound-mgt/delivery-box", "new_rule": "/wpm-standard/outbound-mgt/delivery-box"}, {"is_open": 0, "old_rule": "/outbound/shipping-scan-by-package", "new_rule": "/wpm-standard/outbound/shipping-scan-by-package"}, {"is_open": 0, "old_rule": "/outbound/shipping-scan", "new_rule": "/wpm-standard/outbound/shipping-scan"}, {"is_open": 0, "old_rule": "/outbound/package-boxing", "new_rule": "/wpm-standard/outbound/package-boxing"}, {"is_open": 0, "old_rule": "/sorting-center-management/delivery-scan", "new_rule": "/wpm-standard/sorting-center-management/delivery-scan"}, {"is_open": 0, "old_rule": "/sorting-center-management/sub-package-boxing", "new_rule": "/wpm-standard/sorting-center-management/sub-package-boxing"}, {"is_open": 0, "old_rule": "/sorting-center-management/pick-center-receipt", "new_rule": "/wpm-standard/sorting-center-management/pick-center-receipt"}, {"is_open": 1, "old_rule": "/inbound/receipt-and-storage", "new_rule": "/wpm-standard/inbound/receipt-and-storage"}, {"is_open": 1, "old_rule": "/inbound/put-shelves", "new_rule": "/wpm-standard/inbound/put-shelves"}, {"is_open": 1, "old_rule": "/inbound/fm-manage", "new_rule": "/wpm-standard/inbound/fm-manage"}, {"is_open": 1, "old_rule": "/inbound/receipt-scan", "new_rule": "/wpm-standard/inbound/receipt-scan"}, {"is_open": 1, "old_rule": "/outbound/picking?type=concentratePick", "new_rule": "/wpm-standard/outbound/picking?type=concentratePick"}, {"is_open": 1, "old_rule": "/outbound/picking?type=pickSplitSameTime", "new_rule": "/wpm-standard/outbound/picking?type=pickSplitSameTime"}, {"is_open": 1, "old_rule": "/outbound/transfer-warehouse-collect", "new_rule": "/wpm-standard/outbound/transfer-warehouse-collect"}, {"is_open": 1, "old_rule": "/outbound/transfer-warehouse-sowing", "new_rule": "/wpm-standard/outbound/transfer-warehouse-sowing"}, {"is_open": 1, "old_rule": "/outbound/abnormal-temporary", "new_rule": "/wpm-standard/outbound/abnormal-temporary"}, {"is_open": 1, "old_rule": "/outbound/abnormal-shelf-launch", "new_rule": "/wpm-standard/outbound/abnormal-shelf-launch"}, {"is_open": 1, "old_rule": "/outbound/exception-unshelve", "new_rule": "/wpm-standard/outbound/exception-unshelve"}, {"is_open": 1, "old_rule": "/outbound/kitting-exception-unshelve", "new_rule": "/wpm-standard/outbound/kitting-exception-unshelve"}, {"is_open": 1, "old_rule": "/outbound/timeout-exception-unshelve", "new_rule": "/wpm-standard/outbound/timeout-exception-unshelve"}, {"is_open": 1, "old_rule": "/outbound/collection-warehouse-first-sowing", "new_rule": "/wpm-standard/outbound/collection-warehouse-first-sowing"}, {"is_open": 1, "old_rule": "/outbound/collection-warehouse-second-sowing", "new_rule": "/wpm-standard/outbound/collection-warehouse-second-sowing"}, {"is_open": 1, "old_rule": "/in-warehouse/sub-package-query", "new_rule": "/wpm-standard/in-warehouse/sub-package-query"}, {"is_open": 1, "old_rule": "/in-warehouse/package-query", "new_rule": "/wpm-standard/in-warehouse/package-query"}, {"is_open": 1, "old_rule": "/in-warehouse/collection-search", "new_rule": "/wpm-standard/in-warehouse/collection-search"}, {"is_open": 1, "old_rule": "/in-warehouse/container-query", "new_rule": "/wpm-standard/in-warehouse/container-query"}, {"is_open": 1, "old_rule": "/in-warehouse/location-stock-query", "new_rule": "/wpm-standard/in-warehouse/location-stock-query"}, {"is_open": 1, "old_rule": "/in-warehouse/package-shift", "new_rule": "/wpm-standard/in-warehouse/package-shift"}, {"is_open": 1, "old_rule": "/in-warehouse/pre-broadcast-query", "new_rule": "/wpm-standard/in-warehouse/pre-broadcast-query"}, {"is_open": 1, "old_rule": "/in-warehouse/query-second-shelf", "new_rule": "/wpm-standard/in-warehouse/query-second-shelf"}, {"is_open": 1, "old_rule": "/in-warehouse/exp-location-query", "new_rule": "/wpm-standard/in-warehouse/exp-location-query"}, {"is_open": 1, "old_rule": "/system/inbound-cfg/lane-group-configuration", "new_rule": "/wpm-standard/system/inbound-cfg/lane-group-configuration"}, {"is_open": 1, "old_rule": "/developer-tools/dev-tools", "new_rule": "/wpm-standard/developer-tools/dev-tools"}, {"is_open": 1, "old_rule": "/developer-tools/plan-tools", "new_rule": "/wpm-standard/developer-tools/plan-tools"}, {"is_open": 1, "old_rule": "/developer-tools/transport-shelf-query", "new_rule": "/wpm-standard/developer-tools/transport-shelf-query"}, {"is_open": 1, "old_rule": "/in-warehouse/in-location-stock-inventory", "new_rule": "/wpm-standard/in-warehouse/in-location-stock-inventory"}, {"is_open": 1, "old_rule": "/in-warehouse/in-location-stock-query", "new_rule": "/wpm-standard/in-warehouse/in-location-stock-query"}]