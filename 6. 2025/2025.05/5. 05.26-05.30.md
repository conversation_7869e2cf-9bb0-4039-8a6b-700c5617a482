# 5 月份 第 5 周
## 本周 todo
cursor
考试
  - 前端考试
  - 安全考试

## 2025.05.26 周一
todo
- 食堂项目 迭代三
  - [✅] webview 问题处理
  - [✅] 登录 联调
  - [✅] 订单核销 联调
  - [] 现场收银 联调

## 2025.05.27 周二
todo
- 食堂项目 迭代三
  - [✅] 现场收银 mock
  - [✅] 设置功能
  - [✅] 消费码
  - [✅] 现场收银 联调
  - [] 取消支付

## 2025.05.28 周三
todo
- [] OFC-287277 手持设备安卓版本升级跟进&汇总最终结论
- [✅] OFC-286654 手持终端新增打印小票功能(配置和打印)
  - [] 硬件调试
  - [] 设置
  - [] 打印

## 2025.05.29 周四
todo
- [] OFC-287277 手持设备安卓版本升级跟进&汇总最终结论
- [✅] OFC-286650 “IPS模组化”：时区&国家线模块+上游交互接口可配置&隔断
- [] 迭代3 手持终端优化
  - [✅] cookie 持久化
  - [✅] 返回按键正常
  - [❌] 渲染异常

## 2025.05.30 周五
todo
- [✅] OFC-288737 motapp 环境变量配置化
- [] 迭代3 手持终端优化
  - [✅] 声效
  - [] ui调整
  - [] token 联调
  - [] 渲染异常


com.google.android.webview_66.0.3359.158-335915850_minAPI21(arm64-v8a,armeabi-v7a)(nodpi)_apkmirror.com.apk

pda 试了没问题， 它的版本才66.0.3359.158

- [❌] com.google.android.webview_100.0.4896.58-489605800_minAPI23(armeabi-v7a)(nodpi)_apkmirror.com.apk
- [❌] com.google.android.webview_100.0.4896.58-489605803_minAPI23(arm64-v8a,armeabi-v7a)(nodpi)_apkmirror.com.apk
- [✅] com.google.android.webview_106.0.5249.126-524912603_minAPI23_maxAPI28(arm64-v8a,armeabi-v7a)(nodpi)_apkmirror.com.apk
- [x]com.google.android.webview_108.0.5359.128-535912803_minAPI24_maxAPI28(arm64-v8a,armeabi-v7a)(nodpi)_apkmirror.com.apk
- [✅] com.google.android.webview_110.0.5481.65-548106503_minAPI24_maxAPI28(arm64-v8a,armeabi-v7a)(nodpi)_apkmirror.com.apk

google.android.webview_66.0.3359.158-335915850_minAPI21(arm64-v8a,armeabi-v7a)(nodpi).apk 
google.android.webview_100.0.4896.58-489605803_minAPI23(arm64-v8a,armeabi-v7a)(nodpi).apk 
google.android.webview_106.0.5249.126-524912603_minAPI23_maxAPI28(arm64-v8a,armeabi-v7a)(nodpi).apk
google.android.webview_108.0.5359.128-535912803_minAPI24_maxAPI28(arm64-v8a,armeabi-v7a)(nodpi).apk 
google.android.webview_110.0.5481.65-548106503_minAPI24_maxAPI28(arm64-v8a,armeabi-v7a)(nodpi).apk 
google.android.webview_110.0.5481.154-548115403_minAPI24_maxAPI28(arm64-v8a,armeabi-v7a)(nodpi).apk

https://www.apkmirror.com/uploads/page/20/?appcategory=android-system-webview

1、确定原版本和升级目标版本(走安全验证)
2、（找产品）验证后台远程推送安装升级方案，并确定最终推广升级方案
3、（找产品）协调现场设备升级版本后，使用悠饭APP功能是否正常
4、渲染异常

初始版本：62.0.3202.84

## 2025.05.31 周六
## 2025.06.01 周日

