# fads 治理
只治理es

## ips
[✅] 可以了，多了1.147了

74.633-69.71=4.923

目前：
17.3/18
3.72/4
17.3-12.89=4.41
3.72-2.06=1.66
4.41+1.66=6.07

12.89/18
2.06/4

14/18
2.64/4

多了14-12.89=1.11
多了2.64-2.06=0.58
1.11+0.58=1.69

4.923-1.69=3.233

14:39 补充注释方法还能扣一扣，看脚本修正情况

15.82/18
3.56/4

多了15.82-14=1.82
多了3.56-2.64=0.92
1.82+0.92=2.74

3.233-2.74=0.493

15:16 大喜，只差0.5分了

目标是把下面这几个都处理了
jsdoc/require-param
jsdoc/require-returns-type
jsdoc/require-returns
jsdoc/require-param-type
jsdoc/valid-types

17.1/18
3.68/4

多了17.1-15.82=1.28
多了3.68-3.56=0.12
1.28+0.12=1.4

0.493-1.4=-0.907


jsdoc/check-types 121
jsdoc/require-param 53
jsdoc/require-returns-type 22
jsdoc/require-returns 18
jsdoc/require-param-type 11

17.3/18
3.72/4
多了17.3-17.1=0.2
多了3.72-3.68=0.04
0.2+0.04=0.24

-0.907-0.24=-1.147

## ips-w
[✅] 可以了，多了0.779了

72.241-69.83=2.411

12.36/18
1.97/4

14.41/18
3.11/4

多了14.41-12.36=2.05
多了3.11-1.97=1.14
2.05+1.14=3.19
2.411-3.19=-0.779



## mot-la

### v0
```json
"eslintAvgIssues": {
        "score": 9.09,
        "maxScore": 18
      },
      "eslintFileRatio": {
        "score": 1.73,
        "maxScore": 4
      },
```

### v1
脚本初成
```json
"eslintAvgIssues": {
        "score": 13.52,
        "maxScore": 18
      },
      "eslintFileRatio": {
        "score": 2.89,
        "maxScore": 4
      },
```

13.52-9.09=4.43
2.89-1.73=1.16
4.43+1.16=5.59

### v2
修正 jsdoc/require-returns-check 
```json
"eslintAvgIssues": {
        "score": 14.28,
        "maxScore": 18
      },
      "eslintFileRatio": {
        "score": 3.18,
        "maxScore": 4
      },
```

14.28-13.52=0.76
3.18-2.89=0.29
0.76+0.29=1.05

5.59+1.05=6.64

