# 超级萝卜
下面是我的需求规划，你帮我分析和完善下，列出一个完整的计划，输出一个待办todo.md。记住，只规划，不要改代码

## 现有痛点
- 不能实时跟进需求的进度（需求漏发或者撤下来）
- 表格很难找到自己需要关注的信息

## 想要实现的功能
- 有个任务界面
- 可以设置定时提醒
- 任务可以检查涉及到的需求状态/灰度跟进情况
- 任务可以@到对应人员
- 有个基础的通信功能，主要用于@对应人员，对应人员能接收到chrome通知

## 互联
1. jira/dcp（通过页面获取数据
2. gitlab（api
3. paas（主要是跳转
4. arc（当成数据库来用，读取/写入/更新）
5. 统合任务页面（有任务的概念）

## 工作流
当前工作流流程：
1. 发版人员: 根据版本号获取jira数据，导出文件(jira)
2. 发版人员: 根据jira数据，结合gitlab，创建pre-master分支，创建gitlab mr(gitlab)
3. 发版人员: 汇总数据，导出到arc 表格(arc)
4. 发版人员: 在群里提示(企业微信)
5. 开发者: 合并分支(gitlab)
6. 发版人员: 确认分支合并情况(arc)
7. 发版人员: 发灰度(paas)
8. 开发者: 登记灰度验收情况(arc)
9: 发版人员: 检查灰度验收情况(arc)
10: 发版人员: 检查需求合并情况(gitlab 脚本)
11: 发版人员: 发生产(paas)

## 临时发版
可以相对方便的生成临时发版的信息

例如：
@Rebekah Hu @Jax Yang @Vis Lan 申请发一版灰度
【需求优化】 【前端需求】合包标签由于去掉了OVERPACK，需要换回原来的小纸88*80的尺寸
系统：wms-outbound
需求号：OFC-281737
MR: https://gitlab.sheincorp.cn/wms/front/wms-micro/outbound/-/merge_requests/4910
