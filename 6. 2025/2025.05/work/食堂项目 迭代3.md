# 食堂项目 迭代3

## 总体进度
- [✅] 公共逻辑
  - [✅] 多页打包独立入口
  - [] sn码写入
- [✅] /login 登录
  - 设备端 /login
  - 员工端
    - 扫一扫 /canteen-device-scan-login
- [✅] /canteen-device/home 设备首页
- [✅] /canteen-device/order-redeem 订单核销
- [] /canteen-device/on-site-scan-pay 现场收银
  - [] [设备端]现场收银 /on-site-scan-pay
  - [] [员工端]消费码 src/pages/canteen/consumer-qrcode

## temp
canteenDeviceSN: VB09212K20452

曾杰代码分支：OFC-284181

设备查询登录二维码：https://soapi.sheincorp.cn/application/3694/routes/245459/doc
轮询获取ulptoken: https://soapi.sheincorp.cn/application/3694/routes/245462/doc

查询绑定信息：https://soapi.sheincorp.cn/application/3694/routes/245481/doc

企微扫码登录：https://soapi.sheincorp.cn/application/3694/routes/245477/doc

chrome: 62



## webview 

http://localhost:8080/canteen-device.html#/canteen-device/

lpmpm-test01.dotfashion.cn/canteen-device.html

lpmpm-test01.dotfashion.cn/canteen-device.html

https://open.weixin.qq.com/connect/oauth2/authorize?appid=ww6e132f02586f7900&redirect_uri=https%3A%2F%2Flpmpm-test01.dotfashion.cn%2F%23%2Fcanteen-device-scan-login&response_type=code&scope=snsapi_base&state=1#wechat_redirect

https://lpmpm-test01.dotfashion.cn/?code=dpRlViii1aLMxtnucmyzx2vOChwsXBsDT-VMMfTVcOU&state=1#/canteen-device-scan-login?loginCodeId=03be8daa6f174502886639ecfc155e91


https://open.weixin.qq.com/connect/oauth2/authorize?appid=ww6e132f02586f7900&redirect_uri=https%3A%2F%2Flpmpm-test01.dotfashion.cn%2F%23%2Fcanteen-device-scan-login%3FloginCodeId%3D1a520f709a5044049fe4b23c659d2787&response_type=code&scope=snsapi_base&state=1#wechat_redirect

https://lpmpm-test01.dotfashion.cn/?code=MRZ2eC16TZVykv3-_RIMSiwUfcoygJ6xDov601ODHr0&state=1#/canteen-device-scan-login?loginCodeId=03be8daa6f174502886639ecfc155e91

http://localhost:8080/?code=MRZ2eC16TZVykv3-_RIMSiwUfcoygJ6xDov601ODHr0&state=1#/canteen-device-scan-login?loginCodeId=03be8daa6f174502886639ecfc155e91


https://lpmpm-test01.dotfashion.cn/canteen-device#/canteen-device/login

https://lpmpm-test01.dotfashion.cn/bus-driver#/bus-driver/login

*************:8080/canteen-device.html#/canteen-device/login

*************:8080/canteen-device.html#/canteen-device/home

src/canteen-device

            // loadUrl("file:///android_asset/webkit_test.html")
            // loadUrl("file:///android_asset/index.html")
            // loadUrl("file:///android_asset/x5_test.html")
            // loadUrl("https://baidu.com")
            // loadUrl("https://wms.wx-test.sheincorp.cn/#/auth-renew")
            // loadUrl("https://lpmpm-test01.dotfashion.cn/canteen-device#/canteen-device/login")
            loadUrl("http://*************:8080/canteen-device.html#/canteen-device/login")

## 现场收银 prompt

帮我调整现场收银的页面逻辑，完成需求

src/pages/canteen-device/on-site-scan-pay/index.touchStartX

页面计算器组件 AdditionCalculator 确认金额，触发扫码功能handleScan

扫码成功后，展示支付中弹窗（图1），同时倒计时5秒，5秒后可取消支付（图2）。

同时每1秒轮询getOrderStatusAPI，如果orderStatus 为已完成，则展示收银成功界面（图3）

如果orderStatus 为已取消，则提示取消成功，弹窗消失。

取消支付接口为cancelOrderAPI

## 消费码

src/pages/canteen/_/components/remaining-wx-pay
src/pages/canteen/_/hooks/use-canteen-pay.ts

## 安卓应用

- 

