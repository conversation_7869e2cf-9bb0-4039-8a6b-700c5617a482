# OFC-302231 
【日常】IPS 新增“管理中心”模块及“自动叫号”相关逻辑【开发联调&对照prd自测】

prd：https://ax.sheincorp.cn/unpkg/ax/YT0EPU/newest/start.html#id=e6n2k5&g=1


## 接口文档
### 月台管理中心


https://cloud.sheincorp.cn/soapi/app/28434/route/http/view-http/2028066
https://cloud.sheincorp.cn/soapi/app/28434/route/http/view-http/2028065

### 园区规则管理
https://ips-test01.dotfashion.cn/#/management-center/message-push-manage

https://cloud.sheincorp.cn/soapi/app/28434/route/http/view-http/1898608?group=318736&folder=383535

新增字段auto_call_threshold

## 月台管理中心
需求：新增页面：月台管理中心

soapi 接口文档：
https://cloud.sheincorp.cn/soapi/app/28434/route/http/view-http/2028066
https://cloud.sheincorp.cn/soapi/app/28434/route/http/view-http/2028065


## 园区规则管理
需求：

1、“车辆入园/出园管理”新增“入园自动叫号触发阈值”字段：
- 文本框
- 仅允许输入数字
- 输入范围为0-999

点击提示 icon 触发提示浮窗：已入园车辆数小于阈值则触发自动叫号，配置为 0 则视为禁用

2、对当前园区的“触发阈值”字段进行展示，上线后初始化所有园区该阈值为0，后续新增园区默认值为0

https://ips-test01.dotfashion.cn/#/management-center/message-push-manage

https://cloud.sheincorp.cn/soapi/app/28434/route/http/view-http/1898608?group=318736&folder=383535

新增字段auto_call_threshold
