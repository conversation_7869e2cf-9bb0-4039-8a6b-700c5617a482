# OFC-297841
【日常】“国内计薪”劳务费坏账清理功能线上化

prd: https://ax.sheincorp.cn/unpkg/ax/HU2RPC/newest/start.html#id=t4u2es&g=1

## 改动点
- [] 功能1: 劳务费付账表页面 6h
  - /domestic-salary/salary-laborfee-negative
- [] 功能2: 人员信息新增岗位类别列字段 0.1h
- [] 功能3: 原人员架构移动目录并修改标题名称，增加两个搜索条件 ，增加编辑功能 4h
  - /domestic-salary/basic-info/salary-wms-architecture

## prompt cooking 

帮忙开发页面：劳务费负帐表

可以参考：/domestic-salary/basic-info/warehouses-calculate-income

页面的相关接口:

1. 计薪-劳务费负账-导入模版下载：
https://soapi.sheincorp.cn/application/4361/routes/post_wbms_front_salary_laborfee_negative__download_template/doc
2. 计薪-劳务费负账-列表查询：
https://soapi.sheincorp.cn/application/4361/routes/post_wbms_front_salary_laborfee_negative__query_list/doc

header
| 搜索条件名称 | 输入格式           | 默认值 | 数据来源   | 页面交互要求         | 说明描述             |
|--------------|-------------------|--------|------------|----------------------|----------------------|
| 工号         | 下拉-单选         | null   | 人员信息   | 长度适应内容         | 模糊搜索，精准查询   |
| 负账期间     | 时间选择器         | null   | 无         | 选择月份             |                      |
| 记账状态     | 下拉-单选         | null   | 无         | 选项：<br>1-待入账<br>2-已入账<br>3-已封存 |                      |

handle

| 触发器名称 | 是否默认置灰 | 是否支持搜索条件触发 | 触发前提条件/状态 | 是否支持批量（勾选列表） | 是否受分页限制 | 是否二次确认 | 处理逻辑，从以下几个方面描述           | 操作反馈         |
|------------|--------------|---------------------|----------------------|-------------------------|----------------|--------------|--------------------------------------|------------------|
| 导出       | 否           | –                   | –                    | –                       | –              | –            | 导出当前列表所有内容                   |                  |
| 返回       | 否           | –                   | –                    | –                       | –              | –            | 返回原操作页面                         |                  |
| 导入       |              |                     |                      |                         |                |              |                                      |                  |

list
| 字段名称   | 数据来源       | 数据保存/更新条件、节点   | 交互说明                                                                                                                   | 数据说明                                         | 导入逻辑                                         |
|------------|----------------|--------------------------|----------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------|---------------------------------------------------|
| 三级部门   | 人员信息       | 负账计算/导入            | 根据工号，关联人员信息带出；文字：字体、字号、颜色、对齐、间距等                                                          |                                                  |                                                   |
| 四级部门   | 人员信息       | 负账计算/导入            | 根据工号，关联人员信息带出                                                                                                 |                                                  |                                                   |
| 工号       | WBMS/导入      | 负账计算/导入            | 必填；工号在人员信息内                                                                                                     |                                                  |                                                   |
| 姓名       | 人员信息       | 负账计算/导入            | 根据工号，关联人员信息带出                                                                                                 |                                                  |                                                   |
| 岗位       | 人员信息       | 负账计算/导入            | 根据工号，关联人员信息带出                                                                                                 |                                                  |                                                   |
| 公司       | 花名册         | 负账计算/导入            | 根据工号+负账期间，关联花名册，花名册不存在取人员信息；非必填，导入名称                                                    |                                                  |                                                   |
| 入职时间   | 人员信息       | 负账计算/导入            | 根据工号，关联人员信息带出；YYYY-MM-DD；必填，期间必须小于等于当前月                                                       |                                                  |                                                   |
| 离职时间   | 人员信息       | 负账计算/导入            | 根据工号，关联人员信息带出；YYYY-MM-DD；必填，负账金额必须大于0                                                            |                                                  |                                                   |
| 负账期间   | WBMS/导入      | 负账计算/导入            | 负账计算任务-对应的计算月；YYYY-MM                                                                                        |                                                  |                                                   |
| 负账金额   | WBMS/导入      | 负账计算/导入            | 负账计算任务-对应的结算金额                                                                                               |                                                  |                                                   |
| 记账状态   | WBMS           | 负账计算/导入            | 新增或导入初始化默认：1-待记账；写入福利补贴后更新：2-已记账；判断不符合记账条件时更新：3-已封存                           |                                                  |                                                   |
| 记账期间   | WBMS           | 负账计算/导入            | 写入当前福利补贴后，对应的月份；YYYY-MM                                                                                   |                                                  |                                                   |
| 更新时间   | WBMS           | 负账计算/导入            | YYYY-MM-DD HH:MM:SS                                                                                                       |                                                  |                                                   |
| 操作       | 操作日志       |                          | 操作：新增；操作时间：YYYY-MM-DD HH:MM:SS；操作人：工号+英文名                                                            |                                                  |                                                   |


## 功能2:
接口/wbms/front/salary_user/query_list , 新增的字段posn_jb_type_name

## 功能3:

/domestic-salary/basic-info/salary-wms-architecture


1.[✅] 页面换名字换位置，以prd为准
2.接口文档https://soapi.sheincorp.cn/application/4361/routes/group/%E8%AE%A1%E8%96%AA-%E4%BA%BA%E5%91%98%E6%9E%B6%E6%9E%84
a.详情接口，编辑前查询，不要取列表
b.编辑接口
c.导出
d.列表查询新增条件：

然后列表查询会用到一个现有子仓，列表里面是取id查询，用这个页面现有的接口获取这个id/wbms/front/sub_warehouse/query_list