# 月台管理中心功能开发计划

## 1. 功能概述

### 功能描述
开发月台管理中心页面，以看板形式展示月台信息，实现月台配置和批量管理功能。主要包括：
- 看板形式的月台展示（按子仓分类）
- 月台状态实时显示（未开放、空闲、车辆占用）
- 单个月台配置功能（开放状态、截止时间、自动叫号）
- 批量配置功能（批量开放、批量关闭）

### 核心价值和目标
- 提供直观的月台看板管理界面
- 实现月台状态的实时监控和配置
- 支持单个和批量月台操作
- 提升月台运营管理效率

### 与现有功能的关系
- 复用现有的看板页面架构模式（参考platform-show-board）
- 集成现有的公共组件和样式
- 遵循项目统一的数据流和状态管理模式

## 2. 上下文分析

### 相关现有模块分析
1. **platform-show-board**: 提供了月台看板展示的基础组件结构和样式
2. **park-process-config**: 提供了配置弹窗的实现模式
3. **board目录下的看板页面**: 提供了看板布局和实时数据展示的参考
4. **公共组件**: Modal、Form、Button等组件的使用规范
5. **数据管理**: 基于rrc-loader-helper的状态管理模式

### 代码架构模式识别
- **MVC模式**: store.js负责数据管理，jsx负责视图渲染
- **组件化**: 页面拆分为header、list、handle等独立组件
- **状态管理**: 使用rrc-loader-helper进行状态管理
- **API调用**: 统一使用sendBasePostRequest进行接口请求

### 可复用组件梳理
- **Table组件**: 来自shineout，支持分页、排序、筛选
- **Modal组件**: 用于配置弹窗
- **Form组件**: 用于表单输入
- **Button组件**: 操作按钮
- **Select组件**: 下拉选择
- **公共样式**: management-center通用样式

### 现有实现方式参考
- **接口调用**: sendBasePostRequest自动处理下划线转驼峰
- **分页处理**: 统一的pagination配置
- **错误处理**: Modal.error统一错误提示
- **成功提示**: Message.success统一成功提示

## 3. 技术设计

### 详细的技术实现方案

#### 3.1 页面架构设计
```
月台管理中心页面
├── 页面头部（批量配置按钮）
├── 子仓分类看板区域
│   ├── 子仓标题
│   ├── 月台卡片网格布局
│   │   ├── 月台卡片（月台号、ICON、运营状态）
│   │   ├── 自卸标签
│   │   └── 截止时间标签
│   └── 批量选择模式（勾选框）
├── 月台配置弹窗
└── 批量配置弹窗
```

#### 3.2 页面结构设计
```
src/pages/management-center/platform-management-center/
├── index.jsx                 # 主入口文件
├── store.js                  # 状态管理
├── api.js                    # API接口定义
├── style.less               # 样式文件
└── jsx/
    ├── header.jsx            # 头部组件（批量操作）
    ├── board.jsx             # 看板展示组件
    ├── platform-card.jsx    # 月台卡片组件
    ├── config-modal.jsx     # 配置弹窗组件
    └── batch-modal.jsx      # 批量配置弹窗组件
```

#### 3.3 核心组件设计

**PlatformManagementCenter 主组件**
- 负责整体页面布局和状态管理
- 管理月台数据的加载和更新
- 控制批量配置模式的切换

**SubWarehouseSection 子仓区域组件**
- 按子仓分类展示月台
- 处理子仓内月台的排序逻辑

**PlatformCard 月台卡片组件**
- 展示单个月台的状态信息
- 支持点击配置和批量选择
- 实时状态更新显示

**PlatformConfigModal 配置弹窗**
- 单个月台配置表单
- 开放状态、截止时间、自动叫号配置
- 表单验证和提交逻辑

**BatchConfigModal 批量配置弹窗**
- 批量开放/关闭操作
- 批量截止时间设置

#### 3.4 数据流设计
```
用户操作 → Action → Reducer → State → 组件重新渲染
实时数据 → WebSocket/轮询 → State更新 → 月台状态刷新
```
- **状态管理**: 使用rrc-loader-helper管理页面状态
- **API调用**: 基于SOAPI接口文档实现数据获取和更新
- **数据转换**: 利用sendBasePostRequest自动处理字段命名转换

#### 3.5 接口集成
根据SOAPI文档，月台管理中心使用以下接口：
- **查询接口**: `/ips/front/pc/platform/query_info` - 获取看板月台数据
- **批量更新接口**: `/ips/front/pc/platform/batch_update_platform_info` - 支持单个和批量月台配置
- **字段转换**: 后端下划线字段自动转换为前端驼峰字段

### API接口设计

#### 1. 查询月台信息接口
- **接口路径**: `/ips/front/pc/platform/query_info`
- **请求方法**: POST
- **接口描述**: 月台管理中心-查询月台信息
- **请求参数**:
  ```javascript
  {
    park_id?: number // 园区ID，可选，int64格式
  }
  ```
- **返回数据**:
  ```javascript
  {
    code: string,           // 响应编码，成功："0"
    msg?: string,           // 错误描述，成功："OK"
    bbl?: object,           // 响应异常时的bbl相关信息
    info?: {                // 月台信息
      prk_id?: number,                    // 园区ID，int64格式
      platform_num?: number,             // 月台总数，int32格式
      free_platform_num?: number,        // 空闲月台数，int32格式
      occupied_platform_num?: number,    // 已占用月台数，int32格式
      closed_platform_num?: number,      // 未开放月台数，int32格式
      sub_warehouse_info_list?: [         // 子仓信息列表
        {
          sub_warehouse_id?: number,      // 子仓ID，int64格式
          sub_warehouse_name?: string,    // 子仓名称
          platform_details?: [            // 子仓月台信息列表
            {
              platform_id?: number,                    // 月台ID，int64格式
              platform_name?: string,                  // 月台名称
              platform_type?: string,                  // 月台类型："1"非仓库自卸月台、"2"仓库自卸月台，byte格式
              platform_open_status?: string,          // 月台开放状态："1"开放、"2"关闭、"3"长期有效，byte格式
              platform_open_status_name?: string,     // 月台开放状态名称
              platform_usage_status?: string,         // 月台使用状态："1"空闲、"2"占用，byte格式
              platform_usage_status_name?: string,    // 月台使用状态名称
              platform_close_time?: string,           // 月台开放截止时间，datetime格式
              auto_call?: string,                      // 是否开启自动叫号："1"是、"2"否，byte格式
              license_number?: string                  // 车牌号
            }
          ]
        }
      ]
    }
  }
  ```

#### 2. 批量更新月台信息接口
- **接口路径**: `/ips/front/pc/platform/batch_update_platform_info`
- **请求方法**: POST
- **接口描述**: 月台管理中心-批量更新月台信息
- **请求参数**:
  ```javascript
  {
    park_id?: number,                     // 园区ID，可选，int64格式
    platform_change_info_list?: [         // 月台信息列表
      {
        platform_id?: number,            // 月台ID，int64格式
        platform_open_status?: string,   // 月台开放状态："1"开放、"2"关闭、"3"长期有效，byte格式
        platform_close_time?: string,    // 月台开放截止时间，格式为 yyyy-mm-dd HH:mm:ss，datetime格式
        auto_call?: string,               // 是否开启自动叫号："1"是、"2"否，byte格式
        platform_type?: string           // 月台类型："1"非仓库自卸月台、"2"仓库自卸月台，byte格式
      }
    ]
  }
  ```
- **返回数据**:
  ```javascript
  {
    code: string,     // 响应编码，成功："0"
    msg?: string,     // 错误描述，成功："OK"
    bbl?: object      // 响应异常时的bbl相关信息
  }
  ```

**主组件API调用示例**：
```javascript
// store.js中的API调用模式
* search(param = {}) {
  markStatus('loading');
  const { pageNum, pageSize } = this.state.pageInfo;
  const params = {
    ...this.state.searchParams,
    pageNum,
    pageSize,
    ...param,
  };
  const { code, info, msg } = yield queryPlatformInfoAPI(params);
  if (code === '0') {
    yield this.changeData({
      boardData: info.data,
      pageInfo: {
        ...this.state.pageInfo,
        count: info.meta.count,
      },
    });
  } else {
    Modal.error({ title: msg });
  }
}

// 批量更新月台配置
* batchUpdatePlatform(platformList) {
  markStatus('loading');
  const { code, msg } = yield batchUpdatePlatformInfoAPI({
    park_id: this.state.selectedParkId,
    platform_change_info_list: platformList,
  });
  if (code === '0') {
    Message.success(t('配置成功'));
    yield this.search(); // 重新加载数据
  } else {
    Modal.error({ title: msg });
  }
}
```

**接口说明**：
- 查询接口返回按子仓分类的月台详细信息，包含月台状态、车牌号、截止时间等
- 批量更新接口统一处理单个和批量配置，支持开放状态、截止时间、自动叫号、月台类型等字段

**错误处理最佳实践**：
- **不使用try-catch**：项目底层fetch.js已实现完整的错误处理机制，Redux-Saga中无需try-catch
- **统一错误判断**：通过`code === '0'`判断业务成功，`code !== '0'`时显示后端返回的错误信息
- **错误提示规范**：使用`Modal.error({ title: msg })`显示业务错误，使用`Message.success()`显示成功提示
- **网络异常处理**：由底层fetch.js统一处理网络错误、权限验证、SSO认证等异常情况

### 与现有代码的集成方式
- 遵循management-center目录结构规范
- 复用公共组件和样式
- 使用统一的错误处理和消息提示机制
- 集成现有的权限控制体系

### 遵循的设计模式和规范
- **组件化开发**: 功能模块独立封装
- **状态集中管理**: store统一管理页面状态
- **API统一封装**: api.js统一管理接口调用
- **样式模块化**: less文件独立管理样式

### 数据流和控制流设计
```
用户操作 → 组件事件 → store方法 → API调用 → 状态更新 → 视图重渲染
```

## 4. 实现计划

### 详细的开发步骤

#### 第一阶段：基础页面搭建
1. **创建页面目录结构**
   ```
   src/pages/management-center/platform-management-center/
   ├── index.jsx
   ├── store.js
   ├── api.js
   ├── style.less
   └── jsx/
       ├── header.jsx
       ├── board.jsx
       ├── platform-card.jsx
       ├── config-modal.jsx
       └── batch-modal.jsx
   ```

2. **实现主页面组件**
   - 创建基础看板页面布局
   - 集成园区选择器
   - 添加页面路由配置
   - 实现批量配置模式切换

3. **搭建头部区域**
   - 添加批量配置按钮
   - 集成批量操作按钮组（开放、关闭、取消）

#### 第二阶段：看板展示功能
1. **实现月台看板组件**
   - 按子仓分类展示月台
   - 实现月台卡片网格布局
   - 添加子仓标题和月台排序

2. **开发月台卡片组件**
   - 展示月台号、ICON、运营状态
   - 实现自卸标签和截止时间标签
   - 添加批量选择勾选框
   - 处理卡片点击事件

3. **集成数据加载**
   - 使用rrc-loader-helper管理状态
   - 实现看板数据API接口调用
   - 添加实时状态更新机制

#### 第三阶段：配置功能开发
1. **创建单个月台配置弹窗**
   - 基于antd Modal和Form
   - 实现开放状态、截止时间、自动叫号配置
   - 添加表单验证规则和时间校验

2. **开发批量配置弹窗**
   - 实现批量开放配置
   - 实现批量关闭配置
   - 添加截止时间批量设置

3. **实现配置逻辑**
   - 集成单个配置API接口
   - 集成批量配置API接口
   - 处理表单提交和验证
   - 添加Toast成功/失败反馈

#### 第四阶段：状态管理和优化
1. **实现实时状态更新**
   - 添加月台状态轮询机制
   - 处理车辆占用状态显示
   - 实现截止时间自动状态变更

2. **用户体验优化**
   - 添加加载动画和状态指示
   - 优化批量选择交互
   - 完善错误处理和提示

3. **测试和调试**
   - 功能测试验证
   - 状态更新测试
   - 批量操作测试
   - 性能测试和优化

### 需要修改的现有文件
- 路由配置文件（添加新页面路由）
- 权限配置文件（如需要）

### 需要新增的文件和目录
```
src/pages/management-center/platform-management-center/
├── index.jsx                 # 主入口文件
├── store.js                  # 状态管理
├── api.js                    # API接口定义
├── style.less               # 样式文件
└── jsx/
    ├── header.jsx            # 头部组件（批量操作）
    ├── board.jsx             # 看板展示组件
    ├── platform-card.jsx    # 月台卡片组件
    ├── config-modal.jsx     # 配置弹窗组件
    └── batch-modal.jsx      # 批量配置弹窗组件
```

### 代码示例或关键函数签名

#### API接口定义
```javascript
// api.js
import { sendBasePostRequest } from '@src/server/fetch';

// 查询月台信息（看板数据）
export const queryPlatformInfoAPI = data => sendBasePostRequest({
  url: `${process.env.PC_URL}/ips/front/pc/platform/query_info`,
  data,
});

// 批量更新月台信息（支持单个和批量）
export const batchUpdatePlatformInfoAPI = data => sendBasePostRequest({
  url: `${process.env.PC_URL}/ips/front/pc/platform/batch_update_platform_info`,
  data,
});

// 请求参数示例
// 查询参数: { park_id: 123 }
// 批量更新参数: {
//   park_id: 123,
//   platform_change_info_list: [{
//     platform_id: 456,
//     platform_open_status: '1', // 1开放、2关闭、3长期有效
//     platform_close_time: '2024-01-01 18:00:00',
//     auto_call: '1', // 1是、2否
//     platform_type: '1' // 1非仓库自卸月台、2仓库自卸月台
//   }]
// }
```

#### Store状态管理
```javascript
// store.js
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { Modal, Message } from 'shineout';
import { getPageSize } from '@src/utils/helper';
import { queryPlatformInfoAPI, batchUpdatePlatformInfoAPI } from './api';

const defaultState = {
  loading: 1, // 0 loading..., 1 success
  isHeaderZoomOut: false, // 缩放
  boardData: [], // 看板数据
  selectedParkId: null, // 选中的园区ID
  batchMode: false, // 批量配置模式
  selectedPlatforms: [], // 选中的月台列表
  configModalVisible: false, // 配置弹窗显示状态
  batchModalVisible: false, // 批量配置弹窗显示状态
  currentPlatform: null, // 当前配置的月台
  searchParams: {
    parkId: '', // 园区ID
  },
  pageInfo: {
    pageNum: 1, // 页码
    count: 0, // 表格总条数
    pageSize: 20,
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
};

export default {
  state: defaultState,
  $init: (draft) => {
    Object.assign(draft, defaultState, { pageInfo: { ...defaultState.pageInfo, pageSize: getPageSize() || 20 } });
  },
  changeData: (state, data) => {
    Object.assign(state, data);
  },
  changeSearchParamsData(state, data) {
    Object.assign(state, {
      searchParams: {
        ...state.searchParams,
        ...data,
      },
    });
  },
  * init() {
    yield this.search(); // 初始调用一次
  },
  // ... 其他方法
};
```

## 5. 集成考虑

### 与现有功能的集成点
- **权限系统**: 集成现有的用户权限验证
- **公共组件**: 复用Table、Modal、Form等组件
- **样式系统**: 继承management-center的样式规范
- **错误处理**: 使用统一的错误提示机制

### 可能的影响范围分析
- **路由系统**: 需要添加新的页面路由
- **权限配置**: 可能需要添加新的权限节点
- **公共样式**: 可能需要扩展部分样式定义

### 向后兼容性保证
- 不修改现有API接口
- 不影响现有页面功能
- 遵循现有的代码规范和架构模式

### 配置和部署变更
- 无需特殊的配置变更
- 遵循现有的部署流程
- 可能需要更新权限配置

## 6. 风险与注意事项

### 潜在技术风险
1. **接口字段映射**: 确保后端下划线字段正确转换为前端驼峰字段
2. **实时数据更新**: 月台状态可能需要定时刷新机制
3. **权限控制**: 确保配置功能的权限验证

### 实现难点预判
1. **复杂的月台状态展示**: 需要根据不同状态显示不同的样式和信息
2. **配置弹窗的表单验证**: 需要实现完整的表单验证逻辑
3. **分页和搜索的性能优化**: 大量数据时的性能考虑

### 性能影响评估
- **页面加载**: 初始加载时间预计在合理范围内
- **数据刷新**: 考虑实现增量更新减少网络请求
- **内存使用**: 大量列表数据时需要考虑虚拟滚动

### 错误处理策略
1. **网络错误**: 使用统一的错误提示和重试机制
2. **数据异常**: 实现数据校验和异常状态展示
3. **用户操作错误**: 提供清晰的操作指引和错误提示

## 7. 开发时间估算

- **API接口层**: 0.5天
- **状态管理**: 1天
- **页面组件开发**: 2天
- **样式和交互优化**: 1天
- **测试和调试**: 0.5天

**总计**: 约5个工作日

## 8. 验收标准

1. **功能完整性**: 实现PRD中定义的所有功能点
2. **界面一致性**: 与设计图保持一致
3. **性能要求**: 页面加载时间<3秒，操作响应时间<1秒
4. **兼容性**: 支持主流浏览器
5. **代码质量**: 通过ESLint检查，代码覆盖率>80%