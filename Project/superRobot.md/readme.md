# 超级值班萝卜

## 功能移植
- [] superRobot
  - [] jira
    - [✅] 提取数据
    - [✅] 跨页提取
    - [] 导出格式
  - [] gitlab
    - create mr
    - create branch
  - [] 日志系统

## gitlab
- autoTable, 一个用于自动填写「前端发版表」的脚本(根据jira数据，结合gitlab，生成表格数据)
- createPreMaster, 一个用于批量创建 pre-master 分支的脚本
- createMR, 一个用于批量创建 MR 的脚本
- combo, 一个用于自动预填写「前端发版表」& 创建 pre-master 分支 & 创建 MR 的脚本
  - autoTable
  - createPreMaster
  - createMR

## temp
chrome-extension://alcafiajcdpfhgfpibepikhjnjocphke/dashboard.html
chrome-extension://alcafiajcdpfhgfpibepikhjnjocphke/options.html