#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示脚本 - 完整流程演示
"""

import os
import subprocess
import sys

def check_dependencies():
    """检查依赖库"""
    try:
        import pandas
        import openpyxl
        print("✓ 依赖库检查通过")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖库: {e}")
        print("请运行: pip install pandas openpyxl")
        return False

def run_demo():
    """运行演示"""
    print("=" * 50)
    print("页面映射处理脚本演示")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 步骤1：创建示例Excel文件
    print("\n步骤1：创建示例Excel文件...")
    try:
        exec(open('create_sample_excel.py').read())
        print("✓ 示例Excel文件创建成功")
    except Exception as e:
        print(f"✗ 创建示例Excel文件失败: {e}")
        return
    
    # 步骤2：检查JSON文件
    print("\n步骤2：检查JSON权限节点文件...")
    if os.path.exists('permission_nodes.json'):
        print("✓ JSON文件存在")
    else:
        print("✗ JSON文件不存在")
        return
    
    # 步骤3：运行主处理脚本
    print("\n步骤3：运行页面映射处理...")
    try:
        exec(open('page_mapping_processor.py').read())
        print("✓ 处理完成")
    except Exception as e:
        print(f"✗ 处理失败: {e}")
        return
    
    # 步骤4：检查输出文件
    print("\n步骤4：检查输出文件...")
    if os.path.exists('output_data.xlsx'):
        print("✓ 输出文件生成成功: output_data.xlsx")
        
        # 显示结果预览
        try:
            import pandas as pd
            df = pd.read_excel('output_data.xlsx')
            print("\n结果预览:")
            print(df.to_string(index=False))
        except Exception as e:
            print(f"预览失败: {e}")
    else:
        print("✗ 输出文件未生成")
    
    print("\n" + "=" * 50)
    print("演示完成！")
    print("=" * 50)

if __name__ == "__main__":
    run_demo()
