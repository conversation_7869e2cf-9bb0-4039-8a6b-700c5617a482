#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建示例Excel文件的脚本
"""

import pandas as pd

def create_sample_excel():
    """创建示例Excel文件"""
    
    # 示例数据
    data = {
        '序号': [1, 2, 3, 4],
        '页面别名': [
            'inbound/reject-check-manage/reject-check-scan',
            'index',
            'qms/receipt-management/receipt',
            'basic-functions/barcode-print'
        ],
        '浏览次数(PV)': [37703, 31856, 30925, 30554],
        'PV占比': [0.089, 0.075, 0.073, 0.072],
        '用户数(UV)': [323, 1549, 356, 747],
        'UV占比': [0.199, 0.954, 0.219, 0.460]
    }
    
    df = pd.DataFrame(data)
    
    # 保存为Excel文件
    df.to_excel('input_data.xlsx', index=False)
    print("示例Excel文件已创建: input_data.xlsx")
    print("\n文件内容:")
    print(df.to_string(index=False))

if __name__ == "__main__":
    create_sample_excel()
