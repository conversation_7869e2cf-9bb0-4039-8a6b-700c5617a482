# 11.23-11.29
11月第四周

## 11.23 周一
- transition height auto 过渡动画

组件：resourceNavTab
要求：动态呈现效果

展开/折叠的判断机制，之前是通过通过optionsWrapper的height属性值进行判断，现在如果让height值变化肯定就不行了。所以是改为通过options的height值来判断，单flexbox布局中，子元素的高度就是受父元素影响，所以要套一层div，让options的height值自由。到这里，问题就算解决了。

[如何做一个通用的polyfill包](https://zhuanlan.zhihu.com/p/307753092)

## 11.24 周二
19:40 陷入了我最不喜欢的状态：焦虑。明明有一堆事情可以做，但是就是不敢花时间去做。总是怕自己没选到最合适的。这状态，真窝囊。好！列一下我可以做的事情。

工作上可以做的事情
- 组件库的搭建和规范
- vue项目中的loading优化
- vue项目中的组件过渡和动画
- 重构大型页面之后的总结

学习上可以做的事情
- react学习之 mobx与redux
- react学习之 原理
- 数据结构与算法 堆

实践上可以做的事情
- 沉浸式UI（3d与canvas）
- roma（electron）
- 图片显示（小程序）

阅读上可以做的事情
- 《如何阅读一本书》
- 资本论
- 美国众神

游戏上可以做的事情
- 马里奥3d all stars
- 等 稻姬
- 等 2077

舒服了，确实一堆可以做的事情，也明确了不少。好，今晚学习react原理。

lv1 使用
• Class 的生命周期 √
• Hoc 高阶组件 √
• Context √
• Hook 的使用
• react-router 的使用
• redux（包括mobx或saga)
lv2 原理
• fiber
• react 的事件机制
• react 调度机制
• Hook

react fiber真麻烦。。。

- [这可能是最通俗的 React Fiber(时间分片) 打开方式](https://juejin.cn/post/6844903975112671239)

## 11.25 周三
思考题：项目中怎么进行优化
Performance怎么用

- [React性能测量和分析](https://juejin.cn/post/6844903869378641933)
- [浅谈React性能优化的方向](https://juejin.cn/post/6844903865926549511)

- [【React深入】setState的执行机制](https://segmentfault.com/a/1190000018260218)

vue的原理
- [尤雨溪教你写vue 高级vue教程 源码分析](https://www.bilibili.com/video/BV1d4411v7UX?p=1)

## 11.26 周四
vue的原理
- [尤雨溪教你写vue 高级vue教程 源码分析](https://www.bilibili.com/video/BV1d4411v7UX?p=1)

## 11.27 周五
- [MobX 官网](https://cn.mobx.js.org/)
- [命令式编程（Imperative） vs声明式编程（ Declarative）](https://zhuanlan.zhihu.com/p/34445114)
- [响应式编程（Reactive Programming）介绍](https://wiki.jikexueyuan.com/project/android-weekly/issue-145/introduction-to-RP.html)

学习mobx


## 11.28 周六
继续学习mobx

## 11.29 周日
- [尤雨溪教你写vue 高级vue教程 源码分析](https://www.bilibili.com/video/BV1d4411v7UX?p=1)

- 响应性
- 插件
