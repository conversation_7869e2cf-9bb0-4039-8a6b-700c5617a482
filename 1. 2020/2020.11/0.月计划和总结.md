# 11月计划

## 私人时间
- 数据结构和算法（该看完了
- React 使用(该看完了
- 面试经（该看了

## 工作时间 
- 课堂管理平台 nav相关的重构
- 组件库

## 月尾总结
工作上：
- 课堂管理平台 nav相关的重构（完成了，还写了文档）
- 组件库（研究了，但是实践跟规范没跟上）

学习上：
- 数据结构和算法（鸽了）
- React 使用
- 面试经

11月前期，填充知识一度让我很痛苦。但后面我明悟了，不要为了找工作而学习，要为了学习和做事本身。

所以学习计划变成了React 的使用及原理掌握 -> vue 原理教程。这个路线显然更有意义。所以12月，在vue原理、react原理上要更精进些。

对找工作的要求：
- 更赚钱一些，最好年收能实现30%-50%的提升（目前年收9.1*14+4=131.4，简单换算就是升级为平均月收14k)
- 能睡足8小时
- 每天能早起锻炼
- 一周能有时间打打游戏

