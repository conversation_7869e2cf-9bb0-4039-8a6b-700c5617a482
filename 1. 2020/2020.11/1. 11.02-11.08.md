# 11.02-11.08
11月第一周

## 11.02 周一
摸了

## 11.03 周二
摸了

## 11.04 周三
** 65. 有效数字**

```javascript
var isNumber = function(s) {
    const graph = {
        0:{ 'blank': 0, 'sign': 1, '.': 2, 'digit': 6 },
        1:{ 'digit': 6, '.': 2 },
        2:{ 'digit': 3 },
        3:{ 'digit': 3, 'e': 4 },
        4:{ 'digit': 5, 'sign': 7 },
        5:{ 'digit': 5 },
        6:{ 'digit': 6, '.': 3,  'e': 4 },
        7:{ 'digit': 5 },
    }
    let state = 0
    for(c of s.trim()) {
        if(c >= '0' && c <= '9') {
            c = 'digit'
        } else if(c === ' ') {
            c = 'blank'
        } else if(c === '+' || c === '-') {
            c = 'sign'
        }
        state = graph[state][c]
        if(state === undefined) {
            return false
        }
    }
    if([3, 5, 6].includes(state)) {
        return true
    }
    return false
};
```

时间复杂度：O(n)
空间复杂度：O(1)

## 11.05 周四
摸了

## 11.06 周五
摸了

## 11.07 周六
leetcode 算法题：417. 太平洋大西洋水流问题
leetcode 算法题：133、克隆图
数据结构之“图” 章节总结

## 11.08 周日
数据结构之“堆” 简介
