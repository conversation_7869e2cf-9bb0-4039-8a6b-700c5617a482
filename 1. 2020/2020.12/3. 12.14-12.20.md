# 12.14-12.20
12月第三周！

好吧，2077实在太好玩了，这周通关，下周再好好学习

## 周计划

### 学习
- 看完 高级vue教程

### 工作
没想好，再折腾折腾组件规范

## 2020.12.14 周一
vue 高级教程
- 4.4 mutations

webpack.dll.conf.js
```javascript
/* 作用：构建出动态链接库文件  */
/* 编译环境 */
const isDev = process.env.NODE_ENV === 'development'

const pkg = require('../package.json') // 引入package.json
const config = require('../config')

const path = require('path')
const webpack = require('webpack')
const OptimizeCSSPlugin = require('optimize-css-assets-webpack-plugin')
const UglifyJsPlugin = require('uglifyjs-webpack-plugin')

const externals = Object.keys(config.externals)
const outputPath = isDev ? path.join(__dirname, '../static/common/debug') : path.join(__dirname, '../static/common/dist')
let optimization = {}
const plugins = [
  new webpack.DllPlugin({
    context: __dirname,
    // 描述动态链接库的 manifest.json 文件输出时的文件名称，不填默认是'build', 此处放在 dll.js 同目录下
    path: path.join(outputPath, '[name].manifest.json'),
    // 要和 output.library 中保持一致
    name: '[name]_[hash]'
  })
]

if (!isDev) {
  plugins.push(
    // 定义当前生产环境为node环境
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify('production')
    })
  )
  // webpack4 优化配置
  optimization = {
    minimize: true,
    minimizer: [
      new UglifyJsPlugin({
        test: /\.js$/i,
        exclude: /echarts/,
        uglifyOptions: {
          mangle: {
            reserved: ['$', 'exports', 'require'] // 保留的标识符
          },
          compress: { warnings: false },
          output: { comments: false }
        },
        cache: true,
        parallel: true,
        sourceMap: false
      }),
      new OptimizeCSSPlugin({
        cssProcessorOptions: { safe: true, autoprefixer: { remove: false }}
      })
    ]
  }
}

module.exports = {
  mode: isDev ? 'development' : 'production', // webpack4
  // 想要打包的模块
  entry: {
    // 把 生产依赖项 放到一个单独的动态链接库
    // 排除已从外部引入的模块 以及 css模块（如：element-theme-default）
    libs: Object.keys(pkg.dependencies)
      .filter(name => !externals.includes(name) && !['element-theme-default', 'ds-platform-front', 'ds-views'].includes(name))
      .map(name => {
        if (['vue', 'vuex', 'vue-router'].includes(name)) {
          return config.alias[name + '$']
        } else {
          return name
        }
      })
  },
  output: {
    // 动态链接库输出的位置
    path: outputPath,
    // 输出的动态链接库的文件名称，[name] 代表当前动态链接库的名称
    filename: '[name].dll.js',
    // 存放动态链接库的全局变量名称, 全局可获取
    library: '[name]_[hash]',
    libraryTarget: 'umd',
    umdNamedDefine: true
  },
  plugins,
  optimization
}

```

## 2020.12.15 周二
vue 高级教程
- 4.5 函数式编程

## 2020.12.16 周三
vue 高级教程
- 5 路由

## 2020.12.17 周四
摸了，2077

## 2020.12.18 周五
摸了，2077

## 2020.12.19 周六
摸了，2077

## 2020.12.20 周日
摸了，2077


