# 12.07-12.13
12月第二周！

这周2077就开玩了，合理安排好学习时间跟娱乐时间

## 周计划

### 学习
- 看完 高级vue教程

### 工作
没想好，再折腾折腾组件规范

## 2020.12.07 周一
看react router文档，看看之前是否有遗漏的地方。结果发现官网才是最大的宝库。

思考题：`e.preventDefault()`相关的思考，过度点击，穿透。

今天还是女票25岁生日，恭喜！

## 2020.12.08 周二
- 前端工程化学习
- GraphQL与APIJSON
- 微前端
- Mobx 最佳实践和规范
- vue3

## 2020.12.09 周三
- 组件库搭建

```javascript
'use strict'
const path = require('path')
const utils = require('./utils')
const config = require('../config')
const vueLoaderConfig = require('./vue-loader.conf')
const isDev = process.env.NODE_ENV === 'development'

function resolve(dir) {
  return path.join(__dirname, '..', dir)
}
module.exports = {
  context: path.resolve(__dirname, '../'),
  entry: {
    app: './src/main.js'
  },
  output: {
    path: config.build.assetsRoot,
    filename: '[name].js',
    publicPath: isDev ? config.dev.assetsPublicPath : config.build.assetsPublicPath
  },
  resolve: {
    extensions: ['.js', '.vue', '.json'],
    alias: config.alias
  },
  externals: config.externals, // 外部的，不需要去node_module中找的模块
  module: {
    noParse: /node_modules\/(element-ui\.js)/, // 无依赖项
    rules: [{
      test: /\.vue$/,
      loader: 'vue-loader',
      options: vueLoaderConfig,
      include: [resolve('src'), resolve('node_modules/element-ui'), resolve('node_modules/ds-views'), resolve('node_modules/ds-platform-front')]
    },
    {
      test: /\.js$/,
      loader: 'babel-loader?cacheDirectory=true',
      include: [resolve('src'), resolve('node_modules/webpack-dev-server/client')]
    },
    {
      test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
      loader: 'url-loader',
      options: {
        limit: 10000,
        name: utils.assetsPath('img/[name].[hash:7].[ext]')
      },
      include: [resolve('src'), resolve('static'), resolve('node_modules/ds-views'), resolve('node_modules/ds-platform-front')]
    },
    {
      test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
      loader: 'url-loader',
      options: {
        limit: 10000,
        name: utils.assetsPath('media/[name].[hash:7].[ext]')
      },
      include: [resolve('src')]
    },
    {
      test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
      loader: 'url-loader',
      options: {
        limit: 10000,
        name: utils.assetsPath('fonts/[name].[hash:7].[ext]')
      },
      include: [resolve('src'), resolve('node_modules/element-ui'), resolve('node_modules/ds-views'), resolve('node_modules/ds-platform-front')]
    }
    ]
  },
  node: {
    // prevent webpack from injecting useless setImmediate polyfill because Vue
    // source contains it (although only uses it if it's native).
    setImmediate: false,
    // prevent webpack from injecting mocks to Node native modules
    // that does not make sense for the client
    dgram: 'empty',
    fs: 'empty',
    net: 'empty',
    tls: 'empty',
    child_process: 'empty'
  }
}

```
## 2020.12.10 周四
完成！高兴！

## 2020.12.11 周五
摸了！

## 2020.12.12 周六
摸了！

## 2020.12.13 周日
摸了！3天连摸2077！

