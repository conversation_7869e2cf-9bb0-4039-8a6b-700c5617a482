# 10.26-11.01
十月第四周

## 周计划
- 数据结构和算法
- 整理和回顾 数据结构和算法
- 重读React 官方文档

## 2020.10.26 周一
- [React.Component](https://www.yuque.com/docs/share/776b0893-9f8e-4425-b1a1-c578f81ed0c3)
- [React 顶层 API](https://www.yuque.com/docs/share/498aa5e4-fc8e-40b4-985e-70cf355594b0)

## 2020.10.27 周二

- React refs
- React hoc

## 2020.10.28 周三

**图的深度广度优先遍历**


深度优先遍历算法

口诀：
- 访问根节点
- 对根节点的没访问过的相邻节点挨个进行深度优先遍历

![深度优先遍历算法口诀](./images/深度优先遍历算法口诀.png)

```javascript
const visited = new Set()
const dfs = (n) => {
    console.log(n)
    visited.add(n)
    graph.forEach(c => {
        if(!visited.has(c)) {
            dfs(c)
        }
    })
}
```

广度优先遍历算法

口诀：
- 新建一个队列，把根节点入队
- 把队头出队并访问
- 把对头的没访问过的相邻节点入队
- 重复第二、三步，直到队列为空

![广度优先遍历算法口诀](./images/广 度优先遍历算法口诀.png)

```javascript
const visited = new Set()
visited.add(2)
const q = [2]
while(q.length) {
    const n = q.shift()
    console.log(n)
    graph[n].forEach(c => {
        if(!visited.has(c)) {
            q.push(c)
            visited.add(n)
        }
    })
}
```

## 2020.10.29 周四
摸了

## 2020.10.30 周五
摸了

## 2020.10.31 周六
摸了

## 2020.11.01 周日
摸了

