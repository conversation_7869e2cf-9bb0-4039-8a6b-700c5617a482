# 10.19-10.25
十月第三周

## 周计划
继续学习
- 数据结构和算法
- React 动画（算是完成
- React Context使用（完成

## 2020.10.19 周一
**104、二叉树的最大深度**

```javascript
var maxDepth = function(root) {
    let res = 0
    const dfs = (n, l) => {
        if(!n) {return}
        if(!n.left && !n.right) {
            res = Math.max(res, l)
        }
        dfs(n.left, l + 1 )
        dfs(n.right, l + 1)
    }
    dfs(root, 1)
    return res
};
```

方法：深度优先遍历法
时间复杂度: O(n)
空间复杂度：里面有一个隐形的堆栈结构，最好情况是 O(logn)， 最坏情况是 O(n)

**111、二叉树的最小深度**
```javascript
var minDepth = function(root) {
    if(!root) {return 0}
    const q = [[root, 1]]
    while(q.length) {
        const [n, l] = q.shift()
        if(!n.left && !n.right) {
            return l;
        }
        if(n.left) q.push([n.left, l+1] )
        if(n.right) q.push([n.right, l+1])
    }
};
```

方法：广度优先遍历法
时间复杂度: 最坏情况O(n) n是节点数量
空间复杂度：最快情况O(n) n是节点数量


## 2020.10.20 周二
工作中的学习：React 动画
高阶API CSSTranstation

**102、二叉树的层序遍历**
```javascript
var levelOrder = function (root) {
    if (!root) return []
    let q = [root]
    const res = []
    while (q.length) {
        let len = q.length
        res.push([])
        while (len--) {
            const n = q.shift()
            res[res.length - 1].push(n.val)
            if (n.left) q.push(n.left)
            if (n.right) q.push(n.right)
        }

    }
    return res;
};
```

时间复杂度: 最坏情况O(n)
空间复杂度：最快情况O(n)

## 2020.10.21 周三
**94、二叉树的中序遍历**
递归版：
```javascript
var inorderTraversal = function(root) {
    const res = []
    const rec = (n) => {
        if(!n) return;
        if(n.left) rec(n.left)
        res.push(n.val)
        if(n.right) rec(n.right)
    }
    rec(root)

    return res
};
```

非递归版：
```javascript
var inorderTraversal = function (root) {
    const res = []
    const stack = []
    let p = root
    while (stack.length || p) {
        while (p) {
            stack.push(p)
            p = p.left
        }
        const n = stack.pop()
        res.push(n.val)
        p = n.right
    }

    return res
};
```


## 2020.10.22 周四
**112、路径总和**
```javascript
var hasPathSum = function (root, sum) {
    if (!root) return false;
    let res = false
    const dfs = (n, s) => {
        console.log(n.val)
        if (!n.left && !n.right && s === sum) {
            res = true
        }
        if (n.left) dfs(n.left, s + n.left.val)
        if (n.right) dfs(n.right, s + n.right.val)
    }
    dfs(root, root.val)
    return res
};
```

还挺简单的，用深度优先遍历dfs就差不多完事了


**前端与树：遍历JSON的所有节点值**
```javascript
const json = {
    a: { b: { c: 1}},
    d: [1, 2]
}

constdfs = (n, path) => {
    console.log(n, path)
    Object.keys(n).forEach(k => {
        dfs(n[k], path.concat(k))
    })
}

dfs(json, []);
```

还是深度优先遍历，算是其中一个变种。不好理解

## 2020.10.23 周五
**前端与树：渲染 Antd中的树组件**
```javascript
    dfs = (n) => {
        <TreeNode title={n.name} key={n.key}>
            {n.children.map(this.dfs}
        </TreeNode>
    }
```
数据结构与算法 
数据结构之“树”tree 技术总结
- 树是一种分层数据的抽象模型，在前端中广泛使用
- 树的常用操作： 深度/广度优先遍历，先中后序遍历

数据结构与算法 
数据结构之“图” 简介
- 图是网络结构的抽象模型，是一组由边连接的节点。
- 图可以表示任意二院关系，比如道路，航线
- 深度优先遍历：尽可能深的搜索图的分支
- 广度优先遍历：先访问离根节点最近的节点

深度优先遍历

![深度优先遍历算法口诀](./images/深度优先遍历算法口诀.png)

## 2020.10.24 周六

今天主要是边做边学，动手为主
- 怎么建设团队公共组件
- React 的state导致重渲染问题

### 怎么建设团队公共组件？
找了找解决方案，对lerna有点想法，想试一试看看

### React state导致重渲染问题
写了相关文档，可以直接看[React 的重渲染问题](https://www.yuque.com/docs/share/2cebcb26-63e2-438c-89ad-3acbe4da1df2)

## 2020.10.25 周日

重读 React 官方文档 React.Component