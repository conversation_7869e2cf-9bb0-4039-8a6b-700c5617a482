# 10.08-10.11
十月第一周

## 周计划
开始计划的时候，本周只剩4天了。但是还是要计划计划。

开始学习慕课网的[JavaScript版数据结构与算法 轻松解决前端算法面试](https://coding.imooc.com/learn/list/446.html)

## 2020.10.08 周四
数据结构与算法 
时间/空间复杂度计算
- 复杂度计算中常量可以忽略
- 空间可以理解为用到的内存
- log是什么，2^x=n x=log2n

## 2020.10.09 周五
数据结构与算法 
数据结构之“栈”stack
- 栈是一个后进先出的数据结构
- js中没有栈，但可以用Array来实现栈的所有功能
- 栈的应用场景：十进制转二进制、判断字符串的括号是否有效、函数调用堆栈
- 栈的常用操作：`push`, `pop`, `stack[stack.length - 1]`

## 2020.10.10 周六
数据结构与算法 
数据结构之“队列”queue
- 栈是一个先进先出的数据结构
- js中没有队列，但可以用Array来实现栈的所有功能
- 栈的应用场景：js异步中的任务队列、计算最近请求次数
- 栈的常用操作：`push`, `shift`, `queue[0]`

经典重现
```
setTimeout(() => console.log(1), 0);
console.log(2);
```

输入结果:21

因为JS引擎是单线程的，Event Loop遇到异步任务就丢给WebAPIs来处理，接着丢到Callback Queue。等前面的event执行完了，才会继续放到JS引擎中执行。

## 2020.10.11 周日
JS基础学习 Class

