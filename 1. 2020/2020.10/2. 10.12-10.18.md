# 10.12-10.18
十月第二周

## 周计划
继续学习
- 数据结构和算法
- React的用法和原理
- JS基础

## 2020.10.12 周一
### 1、React的使用
[Context](https://zh-hans.reactjs.org/docs/context.html#contextconsumer) 

Context在组件中挺好使的，提供了在组件之间（通常时一个父多个子）供向值的方式，而不必显式地通过组件树的逐层传递props。

#### API
##### - `React.createContext`

```javascript
const MyContext = React.createContext(defaultValue);
```

创建一个 Context 对象

只有当组件所处的树中没有匹配到 Provider 时，其 `defaultValue` 参数才会生效。这有助于在不使用 Provider 包装组件的情况下对组件进行测试。注意：将 `undefined` 传递给 Provider 的 value 时，消费组件的 defaultValue 不会生效。

##### - `Context.Provider`

```javascript
<MyContext.Provider value={/* 某个值 */}>
```

每个 Context 对象都会返回一个 Provider React 组件，它允许消费组件订阅 context 的变化。当 Provider 的 value 值发生变化时，它内部的所有消费组件都会重新渲染。

##### - `Class.contextType`

```javascript
class MyClass extends React.Component {
  componentDidMount() {
    let value = this.context;
    /* 在组件挂载完成后，使用 MyContext 组件的值来执行一些有副作用的操作 */
  }
  componentDidUpdate() {
    let value = this.context;
    /* ... */
  }
  componentWillUnmount() {
    let value = this.context;
    /* ... */
  }
  render() {
    let value = this.context;
    /* 基于 MyContext 组件的值进行渲染 */
  }
}
MyClass.contextType = MyContext;
```

挂载在 class 上的 contextType 属性会被重赋值为一个由 React.createContext() 创建的 Context 对象。不过通常都这么用

```javascript
class MyClass extends React.Component {
  static contextType = MyContext;
  render() {
    let value = this.context;
    /* 基于这个值进行渲染工作 */
  }
}
```

##### - `Context.Consumer`

```javascript
<MyContext.Consumer>
  {value => /* 基于 context 值进行渲染*/}
</MyContext.Consumer>
```

一个 React 组件可以订阅 context 的变更，这让你在函数式组件中可以订阅 context。

##### - `Context.displayName`

用于React DevTools

示例
- 动态 Context
- 在嵌套组件中更新 Context
- 使用多个 Context

注意事项：

避免下面的consumers组件重渲染，因为下面的例子当每一次 Provider 重渲染时， value 属性总是被赋值为新的对象

```javascript
// bad
class App extends React.Component {
  render() {
    return (
      <MyContext.Provider value={{something: 'something'}}>
        <Toolbar />
      </MyContext.Provider>
    );
  }
}

// good
class App extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      value: {something: 'something'},
    };
  }

  render() {
    return (
      <Provider value={this.state.value}>
        <Toolbar />
      </Provider>
    );
  }
}

```

### 2、fullpage源码学习
- 高度计算
  - pageYOffest 已滚动像素
  - offestTop 元素相对于父元素顶部的内边距
  - offestHeight 元素内部高度
- 事件兼容
  - 鼠标事件
- 值传递
  - context
  - ref

下回针对高度和位移真的得整理一篇出来。源码学习中学到的proptype和defaultProps，context。有待学习得ref。受益匪浅。

### 3、数据结构与算法 
数据结构之“链表”篇：

**237、删除链表**

**206、反转链表**

## 2020.10.13 周二

数据结构之“链表”篇：

**2、两数相加**

## 2020.10.14 周三

数据结构之“链表”篇：

**83、删除排序链表中的重复元素**

输入: 1->1->2->3->3
输出: 1->2->3

```javascript
var deleteDuplicates = function (head) {
    let p = head
    while (p && p.next) {
        if (p.val === p.next.val) {
            p.next = p.next.next
        } else {
            p = p.next
        }
    }
    return head
};
```

注意的是，如果两个重复的时候，不要前进

**141、环形链表**

快慢指针法，如果两个运动员一快一慢的在跑圈，快的肯定会遇上慢的，而且刚好快一圈。

```javascript
var hasCycle = function(head) {
    let p1 = head
    let p2 = head
    while(p1 && p2 && p2.next) {
        p1 = p1.next
        p2 = p2.next.next
        if(p1 === p2) {
            return true
        }
    }
    return false
};
```

## 2020.10.15 周四
JS中的原型链
- 如果A沿着原型链能找到B.prototype，那么A instanceof B 为true
- 如果在A对象上没有找到x属性，那么会沿着原型链找x属性

模拟instanceof
```
const instanceof = (A, B) => {
  let p = A;
  while(p) 
}
```

## 2020.10.16 周五
数据结构之“链表”篇 技术总结
- 链表是多个元素组成的元素
- 元素存储不连续，用next指针连在一起
- js中没有链表，但可以用Object来模拟链表
- 栈的常用操作：修改next，遍历链表
- js中的原型链也是一个链表
- 使用链表指针可以获取JSON的节点值


## 2020.10.17 周六
数据结构之“集合”篇 Set

**349、两个数组的交集**
```javascript
var intersection = function(nums1, nums2) {
    return [...new Set(nums1)].filter(n => nums2.includes(n));
};
```

学习到的点：
- new 对象可以在里面进行
- 上面的时间复杂度是时间复杂度 O(mn)

迭代Set：
- 多种迭代方式
for...in语句以任意顺序遍历一个对象的除Symbol以外的可枚举属性
Set.prototype.entries()
Set.prototype.forEach()
- Set和Array互转
```javascript
const myArr = [...mySet]
const myArr = Array.from(mySet)
const mySet = new Set(myArr)
```

数据结构之“集合”篇 技术总结
- 集合是一种无序且唯一的数据结构
- ES6中有集合，名为Set
- 集合的常见操作：去重、判断某元素是否在集合中、求交集...

数据结构之“字典”篇：

**349、两个数组的交集**
```javascript
var intersection = function(nums1, nums2) {
    let map = new Map()
    nums1.forEach(n => {
        map.set(n, true)
    })
    const res = []
    nums2.forEach(n => {
        if(map.get(n)) {
            res.push(n)
            map.delete(n)
        }
    })
    return res
};
```

**20、有效的括号**
```javascript
var isValid = function(s) {
    if(s.length % 2 === 1) {return false}

    const stack = []
    const map = new Map()
    map.set('(',')')
    map.set('{','}')
    map.set('[',']')
    for(let i = 0; i< s.length; i++) {
        const c = s[i]
        
        if(map.has(c)) {
            stack.push(c)
        } else {
            const t = stack[stack.length -1];

            if(map.get(t)=== c) {
                stack.pop()
            } else {
                return false
            }
        }
    }
    return stack.length === 0;
};
```

**1、两数之和**
```javascript
var twoSum = function(nums, target) {
    const map = new Map()
    for(let i = 0;i<nums.length; i++) {
        const n = nums[i]
        const n2 = target - n
        if(map.has(n2)) {
            return [map.get(n2), i]
        } else {
            map.set(n, i)
        }
    }
};
```

**3、无重复字符的最长子串**
```javascript
var lengthOfLongestSubstring = function(s) {
    let l = 0;
    let res = 0;
    const map = new Map()
    for(let r = 0;r<s.length;r++) {
        if(map.has(s[r]) && map.get(s[r]) >= l) {
            l = map.get(s[r]) + 1
        }
        res = Math.max(res, r - l + 1)
        map.set(s[r], r)
    }
    return res
};
```

解题思路：
双指针维护一个滑动窗口

**76、最小覆盖字串**
```javascript
var minWindow = function(s, t) {
    let l = 0
    let r = 0
    const need = new Map()
    for(let c of t) {
        need.set(c, need.has(c)? need.get(c) + 1:1)
    }
    let needType = need.size
    let res = ''
    while(r < s.length) {
        const c = s[r]
        
        if(need.has(c)) {
            need.set(c, need.get(c) -1)
            if(need.get(c) === 0) needType -= 1
        }
        while(needType === 0) {
            let newRes = s.substring(l, r+1)
            if(!res  || newRes.length < res.length) res = newRes
            const c2 = s[l]
            if(need.has(c2)) {
                need.set(c2, need.get(c2) + 1)
                if( need.get(c2) === 1) {
                    needType += 1
                }
            }
            l += 1;
        }
        r += 1;
    }
    return res;
};
```

时间复杂度 O(m + n), m是t的长度，n是s的长度
空间复杂度 O(k), k是t里面不同字符的个数

数据结构之“字典” 技术总结
- 与集合类似，是一种存储唯一值的数据结构，但是存储的是键值对。
- ES6中有集合，名为Map
- 集合的常见操作：键值对的增删查改

数据结构之“树” 简介
- 一种分层数据的抽象模型
- 前端工作中常见的树包括：DOM树、级联选择、树形控件
- JS中没有树，但是可以用Object和Array构建树
- 树的常用操作：深度/广度优先遍历、先中后序遍历


## 2020.10.18 周日

深度优先遍历：尽可能深的搜索树的分支
广度优先遍历：先访问离根节点最近的节点

深度优先遍历算法口诀
- 访问根节点
- 对根节点的children挨个进行深度优先遍历

```javascript
const dfs = root => {
  console.log(root.val)
  root.children.forEach(dfs)
}
```

广度优先遍历算法口诀
- 新建一个队列，把根节点入队
- 把队头出队并访问
- 把队头的children挨个入队
- 重复第2、3步，直到队列为空

```javascript
const bfs = root => {
  const q = [root]
  while(q.length>0) {
    const n = q.shift()
    console.log(n)
    n.children.forEach(child => {
      q.push(child)
    })
  }
}
```

二叉树的先中后序遍历
- 树中每个节点最多只能有两个节点
- 在js中通常用Object来模拟二叉树

先序遍历算法口诀:根左右

![先序遍历算法口诀](./images/先序遍历算法口诀.jpg)

递归版：
```javascript
const preOrder = root => {
  if(!root) return
  console.log(root.val)
  preOrder(root.left)
  preOrder(root.right)
}
```

非递归版：
```javascript
const preorder = (root) => {
  if (!root) { return; }
  const stack = [root];
  while (stack.length) {
    const n = stack.pop();
    console.log(n.val);
    if (n.right) stack.push(n.right);
    if (n.left) stack.push(n.left);
  }
};
```

中序遍历算法口诀: 左根右

![中序遍历算法口诀](./images/中序遍历算法口诀.jpg)

递归版：
```javascript
const inorder = (root) => {
  if (!root) { return; }
  inorder(root.left);
  console.log(root.val);
  inorder(root.right);
};
```

非递归版：
```javascript
const inorder = (root) => {
  if (!root) { return; }
  const stack = [];
  
  while(stack.length || p) {
    while(p) {
      stack.push(p)
      p = p.left
    }
    const n = stack.pop()
    console.log(n)
    p = p.right
  }
};
```

后序遍历算法口诀: 左右根

![后序遍历算法口诀](./images/后序遍历算法口诀.jpg)

```javascript
const postorder = (root) => {
  if (!root) { return; }
  postorder(root.left);
  postorder(root.right);
  console.log(root.val);
};
```

非递归版：
```javascript
const preorder = (root) => {
  if (!root) { return; }
  const outputStack = [];
  const stack = [root];
  while (stack.length) {
    const n = stack.pop();
    outputStack.push(n);
    if (n.left) stack.push(n.left);
    if (n.right) stack.push(n.right);
  }
  while(outputStack.length) {
    const n = outputStack.pop();
    console.log(n.val);
  }
};
```

不好理解，明天继续看，继续理解.


