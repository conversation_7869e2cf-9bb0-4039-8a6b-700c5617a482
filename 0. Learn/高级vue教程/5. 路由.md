# 5. 路由
Routing

## 5.1 哈希路由
```html
<div id="app">
  <component :is="url"></component>
  <a @click="routeTo('#foo')" href="#foo">foo</a>
  <a @click="routeTo('#bar')" href="#bar">bar</a>
</div>
```

```javascript
window.addEventListener('hashchange', () => {
  app.url = window.location.hash.slice(1)
})

const app = new Vue({
  el: '#app',
  data: {
    url: window.location.hash.slice(1)
  },
  components: {
    foo: { template: `<div>foo</div>`},
    bar: { template: `<div>bar</div>`},
  },
})
```

哈希路由的简单实现

## 5.2 路由表
```html
<div id="app">
  <component :is="url"></component>
  <a href="#foo">foo</a>
  <a href="#bar">bar</a>
</div>
```

```javascript
const Foo = { template: `<div>foo</div>` }
const Bar = { template: `<div>bar</div>` }
const NotFound = { template: `<div>not found!</div>` }

const routeTable = {
  'foo': Foo,
  'bar': Bar
}

window.addEventListener('hashchange', () => {
  app.url = window.location.hash.slice(1)
})

const app = new Vue({
  el: '#app',
  data: {
    url: window.location.hash.slice(1)
  },
  computed: {
    matchedComponent () {
      return routeTable[this.url] || NotFound
    }
  },
  render(h) {
      return h('div', [
          h(routeTable[this.url] || NotFound),
          h('a', {attr: {href: '#foo'}}, 'foo'),
          " | ",
          h('a', {attr: {href: '#bar'}}, 'bar')
      ])
  }
})
```

最神奇的地方还是在于理解 `app.url = window.location.hash.slice(1)`这一点，原来vue中的data能这么使用，但是为什么呢？

还有就是`<component :is="url"></component>`的用法，长见识了


## 5.3 动态路由


```javascript
// '#/foo/123' -> foo with id: 123
// '#/bar' -> Bar
// '#/404' -> NotFound

// path-to-regexp usage:
// const regex = pathToRegexp(pattern)
// const match = regex.exec(path)

const Foo = {
  props: ['id'],
  template: `<div>foo with id: {{ id }}</div>`
}
const Bar = { template: `<div>bar</div>` }
const NotFound = { template: `<div>not found!</div>` }

const routeTable = {
  '/foo/:id': Foo,
  '/bar': Bar
}

const compiledRoutes = []
Object.keys(routeTable).forEach(path => {
  const dynamicSegments = []
  const regex = pathToRegexp(path, dynamicSegments)
  const component = routeTable[path]
  compiledRoutes.push({
    component,
    regex,
    dynamicSegments
  })
})

window.addEventListener('hashchange', () => {
  app.url = window.location.hash.slice(1)
})

const app = new Vue({
  el: '#app',
  data: {
    url: window.location.hash.slice(1)
  },
  render (h) {
    const path = '/' + this.url

    let componentToRender
    let props = {}

    compiledRoutes.some(route => {
      const match = route.regex.exec(path)
      componentToRender = NotFound
      if (match) {
        componentToRender = route.component
        route.dynamicSegments.forEach((segment, index) => {
          props[segment.name] = match[index + 1]
        })
        return true
      }
    })

    return h('div', [
      h(componentToRender, { props }),
      h('a', { attrs: { href: '#foo/123' }}, 'foo 123'),
      ' | ',
      h('a', { attrs: { href: '#foo/234' }}, 'foo 234'),
      ' | ',
      h('a', { attrs: { href: '#bar' }}, 'bar'),
      ' | ',
      h('a', { attrs: { href: '#garbage' }}, 'garbage')
    ])
  }
})
```

用到了正则表达式，还有，其实我并没有完全掌握，得再学学