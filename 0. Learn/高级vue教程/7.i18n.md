# 7. i18n

```html
<div id="app">
  <h1>{{ $t('welcome-message') }}</h1>
  <button @click="changeLang('en')">English</button>
  <button @click="changeLang('zh')">中文</button>
  <button @click="changeLang('nl')">Dutch</button>
</div>
```

```javascript
const i18nPlugin = {
  install(vue, locales) {
      Vue.prototype.$t = function (id) {
        return locales[this.$root.lang][id]
      }
  }
}

Vue.use(i18nPlugin, /* option */ {
  en: { 'welcome-message': 'hello' },
  zh: { 'welcome-message': '你好' },
  nl: { 'welcome-message': 'Hallo' }
})

new Vue({
  el: '#app',
  data: {
    lang: 'en'
  },
  methods: {
    changeLang (lang) {
      this.lang = lang
    }
  }
})
```

知识点：
插件与响应性之间的结合，因为这是切换语言的关键