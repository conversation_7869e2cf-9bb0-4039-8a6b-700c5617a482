# 2. 插件
Writing Plugins

## 目录

6. 插件简介 03:53
7. 编写一个简单 plugin 04:08
8. 编写一个简单 plugin-讲解 04:36

## 插件

```javascript
const RulesPlugin = {
  install(Vue) {
    Vue.mixin({
      created() {
        if (this.$options.hasOwnProperty("rules")) {
          // Do something with rules
          const rules = this.$options.rules;
          Object.keys(rules).forEach((key) => {
            const rule = rules[key];
            this.$watch(key, (newValue) => {
              const result = rule.validate(newValue);
              if (!result) {
                console.log(rule.message);
              }
            });
          });
        }
      },
    });
  },
};

Vue.use(RulesPlugin);
```

上面是学习如何通过插件实现全局功能，另外就是你知道插件内部其实可以做任何事情。实际上通过设计API的使用方式驱动功能的实现。

