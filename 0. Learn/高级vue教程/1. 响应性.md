# 1. 响应性
Reactivity

## 目录
1. 响应性 05:37
2. getter和setter 05:12
3. getter和setter讲解 10:10
4. 依赖跟踪 02:46
5. 迷你观察者 02:26

## 响应性
Vue中的响应性是怎么实现的？

声明式怎么变成响应式？

```javascript
onAChanged(() => {
    b = a * 10
})
```
对于前端来说，想变成声明式，相当于在模板解析库的帮助下，state值
```javascript
onStateChanged(() => {
    view = render(state)
})
```

`view = render(state)` 它是所有视图渲染系统的高度抽象形式

```javascript
let update 
const onStatechanged = _update => {
    update = _update
}

const setState = newState => {
    state = newState
    update()
}
```
上面就是React更新状态的方法

vue不一样，是使用ES5的`Object.defineProperty`，重写了所有属性的getter和setter方法

## 1.1 getter和setter 
vue 利用`Object.defineProperty`重写了所有属性的getter和setter方法

简单练习：
```javascript
function convert (obj) {
  Object.keys(obj).forEach(key => {
    let internalValue = obj[key] 
    Object.defineProperty(obj, key, {
      get () {
        console.log(`getting key "${key}": ${internalValue}`)
        return internalValue
      },
      set (newValue) {
        console.log(`setting key "${key}" to: ${newValue}`)
        internalValue = newValue
      }
    })
  })
}
```

## 1.2 依赖跟踪
就是一种订阅者模式

```javascript
  // a class representing a dependency
  // exposing it on window is necessary for testing
  window.Dep = class Dep {
    constructor() {
      this.subscribers = new Set();
    }
    depend() {
      if (acitveUpdate) {
        // register the current active update as a subscriber
        this.subscribers.add(acitveUpdate);
      }
    }
    notify() {
      // run all subscriber functions
      this.subscribers.forEach((subscriber) => subscriber());
    }
  };

  let acitveUpdate;

  function autorun(update) {
    function wrappedUpdate() {
      acitveUpdate = wrappedUpdate;
      update();
      acitveUpdate = null;
    }
    wrappedUpdate();
  }
```

两者连在一起就是Vue的更新系统工作原理

```javascript
function observe (obj) {
  Object.keys(obj).forEach(key => {
    let internalValue = obj[key]
    let dep = new Dep()
    Object.defineProperty(obj, key, {
      get () {
        dep.depend()
        return internalValue
      },
      set (newValue) {
        const isChanged = internalValue !== newValue
        if (isChanged) {
          internalValue = newValue
          dep.notify()
        }
      }
    })
  })
}

window.Dep = class Dep {
  constructor () {
    this.subscribers = new Set()
  }

  depend () {
    if (activeUpdate) {
      this.subscribers.add(activeUpdate)
    }
  }

  notify () {
    this.subscribers.forEach(subscriber => subscriber())
  }
}

let activeUpdate

function autorun (update) {
  function wrappedUpdate () {
    activeUpdate = wrappedUpdate
    update()
    activeUpdate = null
  }
  wrappedUpdate()
}
```