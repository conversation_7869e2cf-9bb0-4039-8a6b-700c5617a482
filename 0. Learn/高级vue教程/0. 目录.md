# 高级vue教程
- [尤雨溪教你写vue 高级vue教程 源码分析](https://www.bilibili.com/video/BV1d4411v7UX?p=1)

## 目录
0. 简介 02:51
1. 响应性 05:37
2. getter和setter 05:12
3. getter和setter讲解 10:10
4. 依赖跟踪 02:46
5. 迷你观察者 02:26
6. 插件简介 03:53
7. 编写一个简单plugin 04:08
8. 编写一个简单plugin-讲解 04:36
9. Render Function简介 03:12
10. Virtual DOM 05:51
11. 响应性和Render Function整合 02:26
12. JSX对比Template 02:26
13. Render Function API 07:44
14. 动态渲染标签 03:02
15. 动态渲染标签-讲解 04:31
16. 动态渲染组件 01:08
17. 动态渲染组件-讲解 11:23
18. 高阶组件 05:52
19. 问答-高阶组件 03:02
20. 高阶组件-讲解 13:01
21. 状态管理简介 08:02
22. Props传递 02:56
23. Props传递-讲解 01:26
24. 对象共享 01:42
25. 对象共享-讲解 02:22
26. 共享实例 00:53
27. 共享实例-讲解 06:44
28. Mu<PERSON> 02:15
29. Mu<PERSON>讲解 03:43
30. 函数式编程 06:28
31. 问答-基于类型的API和嵌套对象 03:23
32. 函数式编程-解答 07:59
33. 哈希路由 02:36
34. 哈希路由-解答 03:14
35. 路由表 01:30
36. 路由表讲解 03:31
37. 正则表达式 08:30
38. 动态路由 02:21
39. 动态路由-解答 11:13
40. 表单验证-基于标记和基于模型对比 02:08
41. 封装表单验证工具类 16:30
42. 国际化 03:58
43. 国际化插件实现 05:09
44. 问答：web components 08:29
45. 问答：服务端渲染 06:09
46. 结束
