# 3. 渲染函数
Render Functions

## 目录

9. Render Function简介 03:12
10. Virtual DOM 05:51
11. 响应性和Render Function整合 02:26
12. JSX对比Template 02:26
13. Render Function API 07:44
14. 动态渲染标签 03:02
15. 动态渲染标签-讲解 04:31
16. 动态渲染组件 01:08
17. 动态渲染组件-讲解 11:23
18. 高阶组件 05:52
19. 问答-高阶组件 03:02
20. 高阶组件-讲解 13:01

## Virtual DOM

JSX和Template各有好处，Vue都支持。

render function API
```javascript
export default {
    render(h) {
        return h('div', {}, [...])
    }
}
``` 

另外一个鲜为人知的特性是h函数也可以渲染一个组件。h函数也可以第一个参数接受一个组件定义

```javascript
import MyComponent from '...'

h(MyComponent, {
    props: { ... }
})
```

## 3.1 动态渲染标签
h函数的简单运用

```javascript
Vue.component('example', {
  props: ['tags'],
  render(h) {
    return h('div', this.tags.map((tag, index) => h(tag, index)))
  }
})
```

## 3.2 动态渲染组件
```javascript
const Foo = {
  functional: true,
  render: h => h('div', 'foo')
}

const Bar = {
  functional: true,
  render: h => h('div', 'bar')
}

Vue.component('example', {
  props: ['ok'],
  functional: true,
  render (h, { props: { tags }}) {
    return this.ok? h(Foo): h(Bar)
  }
})

new Vue({
  el: '#app',
  data: { ok: true }
})
```

函数组件的使用场景：无需状态，只需要根据props来进行渲染

解构小技巧
```javascript
render (h, context) {
  context.props.tags
}
render (h, { props: { tags }}) {
  tags
}
```

## 3.3 高阶组件
高阶组件是增强，只通过传入props，传出props, 尽可能的降低耦合

```javascript
// mock API
function fetchURL (username, cb) {
  setTimeout(() => {
    // hard coded, bonus: exercise: make it fetch from gravatar!
    cb('https://avatars3.githubusercontent.com/u/6128107?v=4&s=200')
  }, 500)
}

const Avatar = {
  props: ['src'],
  template: `<img :src="src">`
}

function withAvatarURL (InnerComponent) {
  return {
    props: {
      username: String
    },
    data () {
      return {
        url: 'http://via.placeholder.com/200x200'
      }
    },
    created () {
      fetchURL(this.username, (url) => { this.url = url })
    },
    render (h) {
      return h(InnerComponent, { props: { src: this.url } })
    }
  }
}

const SmartAvatar = withAvatarURL(Avatar)

new Vue({
  el: '#app',
  components: { SmartAvatar }
})
```