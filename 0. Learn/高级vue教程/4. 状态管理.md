# 4. 状态管理
State Management

前端一开始并没有状态这么概念，更接近的是MVC里面的model，但当时backbone缺乏一种以声明的方式将模型与渲染结合。直到facebook提出flux这个概念。

Redux作者的话：Flux设计模式就像眼镜。 你知道何时需要它。如果你看到的一切都很好，那么你可能不需要它。但一旦你觉得有点问题，那你就需要它。

## 4.1 props传递
```javascript
new Vue({
  el: '#app',
  data: {
    count: 0
  },
  components: {
    Counter: {
      props: ['count'],
      template: `<div>{{ count }}</div>`
    }
  }
})
```
 
count在根组件中，子组件Counter只负责接收和渲染它，这些实例都有共同的父组件。这就是最基本的状态管理 

## 4.2 对象共享
```javascript
const state = {
  count: 0
}

const Counter = {
  data () {
    return state
  },
  render (h) {
    return h('div', this.count)
  }
}

new Vue({
  el: '#app',
  components: {
    Counter
  },
  methods: {
    inc () {
      state.count++
    }
  }
})
```

在vue中，你需要通过data返回才能得到响应性数据。如果你通过data返回数据，vue会调用观察对象转换它得到响应性能力

即：
```javascript
  data () {
    return state
  },
  render (h) {
    return h('div', this.count)
  }
```

## 4.3 共享实例

```javascript
const state = new Vue({
  data: {
    count: 0
  },
  methods: {
    inc () {
      this.count++
    }
  }
})

const Counter = {
  render: h => h('div', state.count)
}

new Vue({
  el: '#app',
  components: {
    Counter
  },
  methods: {
    inc () {
      state.inc()
    }
  }
})
```

因为state由vue处理，vue实例默认具有响应性。这意味着当你访问state.count时会自动触发getter，然后注册依赖关系。它的整个渲染过程都是响应性的。

## 4.4 mutations

```javascript
function createStore({ state, mutations }) {
  data: { state },
  methods: {
    commit (mutationType) {
      mutations[mutationType](this.state)
    }
  }
}

const store = createStore({
  state: { count: 0 },
  mutations: {
    inc(state) {
      state.count++
    }
  }
})

```

实际上这就是一个简单的vue实例，但确实实现了简单的vuex

## 4.5 函数式编程 

```javascript
function app ({ el, model, view, actions }) {
  const wrappedActions = {}

  Object.keys(actions).forEach(key => {
    const originalAction = actions[key]
    wrappedActions[key] = () => {
      vm.model = originalAction(vm.model)
    }
  })

  const vm = new Vue({
    el,
    data: {
      model
    },
    render (h) {
      return view(h, this.model, wrappedActions)
    },
    methods: actions
  })
}

// voila
app({
  el: '#app',
  model: {
    count: 0
  },
  actions: {
    inc: ({ count }) => ({ count: count + 1 }),
    dec: ({ count }) => ({ count: count - 1 })
  },
  view: (h, model, actions) => h('div', { attrs: { id: 'app' }}, [
    model.count, ' ',
    h('button', { on: { click: actions.inc }}, '+'),
    h('button', { on: { click: actions.dec }}, '-')
  ])
})
```

这个vue实例接收model然后让它具有响应性。接着写一个映射，向action提供当前model然后接收下一个model，然后我们用下一个model替换当前model。

暴露给外部的仍然只是一个纯粹的函数。

该练习的重点是让你接受范例，明白jsx与template之间，函数式与class式之间并不存在绝对的好坏差异，要视乎场景决定。保持开放，了解两者的好处。