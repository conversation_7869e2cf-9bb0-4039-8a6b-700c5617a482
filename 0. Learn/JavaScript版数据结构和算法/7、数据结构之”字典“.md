# 第7章 数据结构之“字典” Map

## 目录
- 7-1 字典简介 (07:23)
- 7-2 LeetCode：349. 两个数组的交集 (08:16)
- 7-3 LeetCode：20.有效的括号 (05:33)
- 7-4 LeetCode：1. 两数之和 (08:19)
- 7-5 LeetCode：3. 无重复字符的最长子串 (14:15)
- 7-6 LeetCode：76. 最小覆盖子串 (17:42)

## 总结
数据结构之“字典” 技术总结
- 与集合类似，是一种存储唯一值的数据结构，但是存储的是键值对。
- ES6中有集合，名为Map
- 集合的常见操作：键值对的增删查改


## leetcode 

### 349、两个数组的交集
```javascript
var intersection = function(nums1, nums2) {
    let map = new Map()
    nums1.forEach(n => {
        map.set(n, true)
    })
    const res = []
    nums2.forEach(n => {
        if(map.get(n)) {
            res.push(n)
            map.delete(n)
        }
    })
    return res
};
```

### 20、有效的括号
```javascript
var isValid = function(s) {
    if(s.length % 2 === 1) {return false}

    const stack = []
    const map = new Map()
    map.set('(',')')
    map.set('{','}')
    map.set('[',']')
    for(let i = 0; i< s.length; i++) {
        const c = s[i]
        
        if(map.has(c)) {
            stack.push(c)
        } else {
            const t = stack[stack.length -1];

            if(map.get(t)=== c) {
                stack.pop()
            } else {
                return false
            }
        }
    }
    return stack.length === 0;
};
```

### 1、两数之和
```javascript
var twoSum = function(nums, target) {
    const map = new Map()
    for(let i = 0;i<nums.length; i++) {
        const n = nums[i]
        const n2 = target - n
        if(map.has(n2)) {
            return [map.get(n2), i]
        } else {
            map.set(n, i)
        }
    }
};
```

### 3、无重复字符的最长子串
```javascript
var lengthOfLongestSubstring = function(s) {
    let l = 0;
    let res = 0;
    const map = new Map()
    for(let r = 0;r<s.length;r++) {
        if(map.has(s[r]) && map.get(s[r]) >= l) {
            l = map.get(s[r]) + 1
        }
        res = Math.max(res, r - l + 1)
        map.set(s[r], r)
    }
    return res
};
```

解题思路：
双指针维护一个滑动窗口

### 76、最小覆盖字串
```javascript
var minWindow = function(s, t) {
    let l = 0
    let r = 0
    const need = new Map()
    for(let c of t) {
        need.set(c, need.has(c)? need.get(c) + 1:1)
    }
    let needType = need.size
    let res = ''
    while(r < s.length) {
        const c = s[r]
        
        if(need.has(c)) {
            need.set(c, need.get(c) -1)
            if(need.get(c) === 0) needType -= 1
        }
        while(needType === 0) {
            let newRes = s.substring(l, r+1)
            if(!res  || newRes.length < res.length) res = newRes
            const c2 = s[l]
            if(need.has(c2)) {
                need.set(c2, need.get(c2) + 1)
                if( need.get(c2) === 1) {
                    needType += 1
                }
            }
            l += 1;
        }
        r += 1;
    }
    return res;
};
```

时间复杂度 O(m + n), m是t的长度，n是s的长度
空间复杂度 O(k), k是t里面不同字符的个数

