# 第9章 数据结构之“图” graph

## 目录
- 9-1 图简介 (04:53)
- 9-2 图的深度广度优先遍历 (13:28)
- 9-3 LeetCode：65. 有效数字 (17:51)
- 9-4 LeetCode：417. 太平洋大西洋水流问题 (22:29)
- 9-5 LeetCode：133. 克隆图 (22:09)

## 总结
- 图是**网络结构**的抽象模型，是一组由**边**连接的**节点**。
- 图可以表示任意二院关系，比如道路，航线
- js中没有图，但是可以用Object和Array构件图
- 图的表示法：林杰矩阵、邻接表
- 深度优先遍历：尽可能深的搜索图的分支
- 广度优先遍历：先访问离根节点最近的节点

## leetcode
### 65. 有效数字

```javascript
var isNumber = function(s) {
    const graph = {
        0:{ 'blank': 0, 'sign': 1, '.': 2, 'digit': 6 },
        1:{ 'digit': 6, '.': 2 },
        2:{ 'digit': 3 },
        3:{ 'digit': 3, 'e': 4 },
        4:{ 'digit': 5, 'sign': 7 },
        5:{ 'digit': 5 },
        6:{ 'digit': 6, '.': 3,  'e': 4 },
        7:{ 'digit': 5 },
    }
    let state = 0
    for(c of s.trim()) {
        if(c >= '0' && c <= '9') {
            c = 'digit'
        } else if(c === ' ') {
            c = 'blank'
        } else if(c === '+' || c === '-') {
            c = 'sign'
        }
        state = graph[state][c]
        if(state === undefined) {
            return false
        }
    }
    if([3, 5, 6].includes(state)) {
        return true
    }
    return false
};
```

时间复杂度：O(n)
空间复杂度：O(1)

### 417. 太平洋大西洋水流问题
```javascript
var pacificAtlantic = function (matrix) {
    if (!matrix || !matrix[0]) { return []; }
    const m = matrix.length
    const n = matrix[0].length
    const flow1 = Array.from({ length: m }, () => new Array(n).fill(false))
    const flow2 = Array.from({ length: m }, () => new Array(n).fill(false))

    const dfs = (r, c, flow) => {
        flow[r][c] = true;
        [[r - 1, c], [r + 1, c], [r, c - 1], [r, c + 1]].forEach(([nr, nc]) => {
            if (
                // 保证在矩阵中
                nr >= 0 && nr < m &&
                nc >= 0 && nc < n &&
                // 防止死循环
                !flow[nr][nc] &&
                // 保证逆流而上
                matrix[nr][nc] >= matrix[r][c]
            ) {
                dfs(nr, nc, flow)
            }
        })
    }

    // 沿着海岸线逆流而上
    for (let r = 0; r < m; r += 1) {
        dfs(r, 0, flow1)
        dfs(r, n - 1, flow2)
    }
    for (let c = 0; c < n; c += 1) {
        dfs(0, c, flow1)
        dfs(m - 1, c, flow2)
    }

    //收集能流到两个大洋里的坐标
    const res = []
    for (let r = 0; r < m; r++) {
        for (let c = 0; c < n; c++) {
            if (flow1[r][c] && flow2[r][c]) {
                res.push([r, c])
            }
        }
    }
    return res
};
```

时间复杂度：O(m * n)
空间复杂度：O(m * n)

### 133、克隆图
深度优先遍历
```javascript
var cloneGraph = function (node) {
    if (!node) return
    const visited = new Map()
    const dfs = n => {
        const nCopy = new Node(n.val)
        visited.set(n, nCopy);
        (n.neighbors || []).forEach(ne => {
            if (!visited.has(ne)) {
                dfs(ne)
            }
            nCopy.neighbors.push(visited.get(ne));
        })
    }
    dfs(node)
    return visited.get(node)
};
```

时间复杂度: O(n)
空间复杂度：O(n)

广度优先遍历
```javascript
var cloneGraph = function (node) {
    if (!node) return
    const visited = new Map();
    visited.set(node, new Node(node.val));
    const q = [node]
    while(q.length) {
        const n = q.shift();
        (n.neighbors || []).forEach(ne => {
            if(!visited.has(ne)) {
                q.push(ne)
                visited.set(ne, new Node(ne.val))
            }
            visited.get(n).neighbors.push(visited.get(ne))
        })
    }
    return visited.get(node)
};
```

时间复杂度: O(n)
空间复杂度：O(n)
