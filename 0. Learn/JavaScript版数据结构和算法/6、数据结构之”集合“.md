# 第6章 数据结构之“集合” Set

## 目录
- 6-1 集合简介 (08:54)
- 6-2 LeetCode：349. 两个数组的交集 (08:58)
- 6-3 前端与集合：使用 ES6 中 Set (12:41)

## 总结
迭代Set：
- 多种迭代方式
for...in语句以任意顺序遍历一个对象的除Symbol以外的可枚举属性
Set.prototype.entries()
Set.prototype.forEach()
- Set和Array互转
```javascript
const myArr = [...mySet]
const myArr = Array.from(mySet)
const mySet = new Set(myArr)
```

数据结构之“集合”篇 技术总结
- 集合是一种无序且唯一的数据结构
- ES6中有集合，名为Set
- 集合的常见操作：去重、判断某元素是否在集合中、求交集...

## leetcode

### 349、两个数组的交集

```javascript
var intersection = function(nums1, nums2) {
    return [...new Set(nums1)].filter(n => nums2.includes(n));
};
```

学习到的点：
- new 对象可以在里面进行
- 上面的时间复杂度是时间复杂度 O(mn)

