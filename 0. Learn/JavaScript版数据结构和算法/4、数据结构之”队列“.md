# 第4章 数据结构之“队列”

## 目录
- 4-1 队列简介 (04:19)
- 4-2 什么场景用队列 (05:48)
- 4-3 LeetCode：933. 最近的请求次数 (07:16)
- 4-4 前端与队列：JS 异步中的任务队列 (04:37)

## 总结
- 栈是一个先进先出的数据结构
- js中没有队列，但可以用Array来实现栈的所有功能
- 栈的应用场景：js异步中的任务队列、计算最近请求次数
- 栈的常用操作：`push`, `shift`, `queue[0]`

## 经典重现
```
setTimeout(() => console.log(1), 0);
console.log(2);
```

输入结果:21

因为JS引擎是单线程的，Event Loop遇到异步任务就丢给WebAPIs来处理，接着丢到Callback Queue。等前面的event执行完了，才会继续放到JS引擎中执行。

