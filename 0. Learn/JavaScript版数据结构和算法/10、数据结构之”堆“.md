# 第10章 数据结构之“堆” heap

## 目录
- 10-1 堆简介 (08:35)
- 10-2 JavaScript 实现：最小堆类 (19:38)
- 10-3 LeetCode：215. 数组中的第 K 个最大元素 (06:49)
- 10-4 LeetCode：347. 前 K 个高频元素 (16:40)
- 10-5 LeetCode：23. 合并K个排序链表 (17:00)

## 总结
- 堆是一种特殊的完全二叉树
- 所有的节点都大于等于（最大堆）或者小于等于（最小堆）它的子节点
- JS中通常用数组来表示堆
- 左侧子节点的位置是2 * index + 1
- 右侧子节点的位置是2 * index + 2
- 父节点位置是   (index - 1) / 2

![js中的堆](./images/js中的堆.jpg)

## 简介
### 堆是什么？
- 堆是一种特殊的完全二叉树
- 所有的节点都大于等于（最大堆）或者小于等于（最小堆）它的子节点

### JS中的堆
- JS中通常用数组来表示堆
- 左侧子节点的位置是2 * index + 1
- 右侧子节点的位置是2 * index + 2
- 父节点位置是   (index - 1) / 2

![js中的堆](./images/js中的堆.jpg)

### 堆的应用
- 堆能高效、快速地找到最大值和最小值，时间复杂度：O(1)。
- 找出第K个最大（小）元素

## leetcode

### 215、数组中的第K个最大元素

```javascript
var findKthLargest = function(nums, k) {
    const h = new MinHeap()
    nums.forEach(n => {
        h.insert(n)
        if(h.size() > k) {
            h.pop()
        }
    })
    return h.peek()
};
```

时间复杂度：O(n * logk)
空间复杂度：O(k)

## javascript实现
### 最小堆类

```javascript
class MinHeap {
    constructor() {
        this.heap = [];
    }
    swap(i1, i2) {
        const temp = this.heap[i1];
        this.heap[i1] = this.heap[i2];
        this.heap[i2] = temp;
    }
    getParentIndex(i) {
        return (i - 1) >> 1;
    }
    getLeftIndex(i) {
        return i * 2 + 1;
    }
    getRightIndex(i) {
        return i * 2 + 2;
    }
    shiftUp(index) {
        if (index == 0) { return; }
        const parentIndex = this.getParentIndex(index);
        if (this.heap[parentIndex] > this.heap[index]) {
            this.swap(parentIndex, index);
            this.shiftUp(parentIndex);
        }
    }
    shiftDown(index) {
        const leftIndex = this.getLeftIndex(index);
        const rightIndex = this.getRightIndex(index);
        if (this.heap[leftIndex] < this.heap[index]) {
            this.swap(leftIndex, index);
            this.shiftDown(leftIndex);
        }
        if (this.heap[rightIndex] < this.heap[index]) {
            this.swap(rightIndex, index);
            this.shiftDown(rightIndex);
        }
    }
    insert(value) {
        this.heap.push(value);
        this.shiftUp(this.heap.length - 1);
    }
    pop() {
        this.heap[0] = this.heap.pop();
        this.shiftDown(0);
    }
    peek() {
        return this.heap[0];
    }
    size() {
        return this.heap.length;
    }
}

const h = new MinHeap();
h.insert(3);
h.insert(2);
h.insert(1);
h.pop();
```

