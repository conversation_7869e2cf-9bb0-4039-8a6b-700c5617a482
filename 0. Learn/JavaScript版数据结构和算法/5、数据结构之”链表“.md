# 第5章 数据结构之“链表”

## 目录
- 5-1 链表简介 (08:42)
- 5-2 LeetCode：237.删除链表中的节点 (04:48)
- 5-3 LeetCode：206.反转链表 (08:52)
- 5-4 LeetCode：2. 两数相加 (12:39)
- 5-5 LeetCode：83. 删除排序链表中的重复元素 (07:08)
- 5-6 LeetCode：141. 环形链表 (09:05)
- 5-7 前端与链表：JS 中的原型链 (22:02)
- 5-8 前端与链表：使用链表指针获取 JSON 的节点值 (03:20)

## 总结
- 链表是多个元素组成的元素
- 元素存储不连续，用next指针连在一起
- js中没有链表，但可以用Object来模拟链表
- 栈的常用操作：修改next，遍历链表
- js中的原型链也是一个链表
- 使用链表指针可以获取JSON的节点值

## leetcode

### 83、删除排序链表中的重复元素

输入: 1->1->2->3->3
输出: 1->2->3

```javascript
var deleteDuplicates = function (head) {
    let p = head
    while (p && p.next) {
        if (p.val === p.next.val) {
            p.next = p.next.next
        } else {
            p = p.next
        }
    }
    return head
};
```

注意的是，如果两个重复的时候，不要前进

### 141、环形链表

**快慢指针法**，如果两个运动员一快一慢的在跑圈，快的肯定会遇上慢的，而且刚好快一圈。

```javascript
var hasCycle = function(head) {
    let p1 = head
    let p2 = head
    while(p1 && p2 && p2.next) {
        p1 = p1.next
        p2 = p2.next.next
        if(p1 === p2) {
            return true
        }
    }
    return false
};
```

## JS 中的原型链

- 如果A沿着原型链能找到B.prototype，那么A instanceof B 为true
- 如果在A对象上没有找到x属性，那么会沿着原型链找x属性

模拟instanceof
```
const instanceof = (A, B) => {
  let p = A;
  while(p) 
}
```

