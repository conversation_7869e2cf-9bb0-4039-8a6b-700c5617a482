# 第8章 数据结构之“树” tree

## 目录
- 8-1 树简介 (04:07)
- 8-2 深度与广度优先遍历 (15:14)
- 8-3 二叉树的先中后序遍历 (16:49)
- 8-4 二叉树的先中后序遍历（非递归版） (15:17)
- 8-5 LeetCode：104. 二叉树的最大深度 (10:38)
- 8-6 LeetCode：111. 二叉树的最小深度 (07:30)
- 8-7 LeetCode：102. 二叉树的层序遍历 (15:48)
- 8-8 LeetCode：94. 二叉树的中序遍历 (08:45)
- 8-9 LeetCode：112. 路径总和 (11:01)
- 8-10 前端与树：遍历 JSON 的所有节点值 (06:05)
- 8-11 前端与树：渲染 Antd 中的树组件 (04:22)

## 总结
- 树是一种分层数据的抽象模型
- 前端工作中常见的树包括：DOM树、级联选择、树形控件
- JS中没有树，但是可以用Object和Array构建树
- 树的常用操作：深度/广度优先遍历、先中后序遍历

深度优先遍历：尽可能深的搜索树的分支
广度优先遍历：先访问离根节点最近的节点

深度优先遍历算法口诀
- 访问根节点
- 对根节点的children挨个进行深度优先遍历

```javascript
const dfs = root => {
  console.log(root.val)
  root.children.forEach(dfs)
}
```

广度优先遍历算法口诀
- 新建一个队列，把根节点入队
- 把队头出队并访问
- 把队头的children挨个入队
- 重复第2、3步，直到队列为空

```javascript
const bfs = root => {
  const q = [root]
  while(q.length>0) {
    const n = q.shift()
    console.log(n)
    n.children.forEach(child => {
      q.push(child)
    })
  }
}
```

二叉树的先中后序遍历
- 树中每个节点最多只能有两个节点
- 在js中通常用Object来模拟二叉树

先序遍历算法口诀:根左右

![先序遍历算法口诀](./images/先序遍历算法口诀.jpg)

递归版：
```javascript
const preOrder = root => {
  if(!root) return
  console.log(root.val)
  preOrder(root.left)
  preOrder(root.right)
}
```

非递归版：
```javascript
const preorder = (root) => {
  if (!root) { return; }
  const stack = [root];
  while (stack.length) {
    const n = stack.pop();
    console.log(n.val);
    if (n.right) stack.push(n.right);
    if (n.left) stack.push(n.left);
  }
};
```

中序遍历算法口诀: 左根右

![中序遍历算法口诀](./images/中序遍历算法口诀.jpg)

递归版：
```javascript
const inorder = (root) => {
  if (!root) { return; }
  inorder(root.left);
  console.log(root.val);
  inorder(root.right);
};
```

非递归版：
```javascript
const inorder = (root) => {
  if (!root) { return; }
  const stack = [];
  
  while(stack.length || p) {
    while(p) {
      stack.push(p)
      p = p.left
    }
    const n = stack.pop()
    console.log(n)
    p = p.right
  }
};
```

后序遍历算法口诀: 左右根

![后序遍历算法口诀](./images/后序遍历算法口诀.jpg)

```javascript
const postorder = (root) => {
  if (!root) { return; }
  postorder(root.left);
  postorder(root.right);
  console.log(root.val);
};
```

非递归版：
```javascript
const preorder = (root) => {
  if (!root) { return; }
  const outputStack = [];
  const stack = [root];
  while (stack.length) {
    const n = stack.pop();
    outputStack.push(n);
    if (n.left) stack.push(n.left);
    if (n.right) stack.push(n.right);
  }
  while(outputStack.length) {
    const n = outputStack.pop();
    console.log(n.val);
  }
};
```

## leetcode

### 104、二叉树的最大深度
```javascript
var maxDepth = function(root) {
    let res = 0
    const dfs = (n, l) => {
        if(!n) {return}
        if(!n.left && !n.right) {
            res = Math.max(res, l)
        }
        dfs(n.left, l + 1 )
        dfs(n.right, l + 1)
    }
    dfs(root, 1)
    return res
};
```

方法：深度优先遍历法
时间复杂度: O(n)
空间复杂度：里面有一个隐形的堆栈结构，最好情况是 O(logn)， 最坏情况是 O(n)

### 111、二叉树的最小深度
```javascript
var minDepth = function(root) {
    if(!root) {return 0}
    const q = [[root, 1]]
    while(q.length) {
        const [n, l] = q.shift()
        if(!n.left && !n.right) {
            return l;
        }
        if(n.left) q.push([n.left, l+1] )
        if(n.right) q.push([n.right, l+1])
    }
};
```

方法：广度优先遍历法
时间复杂度: 最坏情况O(n) n是节点数量
空间复杂度：最快情况O(n) n是节点数量

### 102、二叉树的层序遍历
```javascript
var levelOrder = function (root) {
    if (!root) return []
    let q = [root]
    const res = []
    while (q.length) {
        let len = q.length
        res.push([])
        while (len--) {
            const n = q.shift()
            res[res.length - 1].push(n.val)
            if (n.left) q.push(n.left)
            if (n.right) q.push(n.right)
        }

    }
    return res;
};
```

时间复杂度: 最坏情况O(n)
空间复杂度：最快情况O(n)

### 94、二叉树的中序遍历
递归版：
```javascript
var inorderTraversal = function(root) {
    const res = []
    const rec = (n) => {
        if(!n) return;
        if(n.left) rec(n.left)
        res.push(n.val)
        if(n.right) rec(n.right)
    }
    rec(root)

    return res
};
```

非递归版：
```javascript
var inorderTraversal = function (root) {
    const res = []
    const stack = []
    let p = root
    while (stack.length || p) {
        while (p) {
            stack.push(p)
            p = p.left
        }
        const n = stack.pop()
        res.push(n.val)
        p = n.right
    }

    return res
};
```

### 112、路径总和
```javascript
var hasPathSum = function (root, sum) {
    if (!root) return false;
    let res = false
    const dfs = (n, s) => {
        console.log(n.val)
        if (!n.left && !n.right && s === sum) {
            res = true
        }
        if (n.left) dfs(n.left, s + n.left.val)
        if (n.right) dfs(n.right, s + n.right.val)
    }
    dfs(root, root.val)
    return res
};
```

还挺简单的，用深度优先遍历dfs就差不多完事了

## 前端与树

### 遍历JSON的所有节点值
```javascript
const json = {
    a: { b: { c: 1}},
    d: [1, 2]
}

constdfs = (n, path) => {
    console.log(n, path)
    Object.keys(n).forEach(k => {
        dfs(n[k], path.concat(k))
    })
}

dfs(json, []);
```

还是深度优先遍历，算是其中一个变种。不好理解

### 渲染 Antd中的树组件
```javascript
    dfs = (n) => {
        <TreeNode title={n.name} key={n.key}>
            {n.children.map(this.dfs}
        </TreeNode>
    }
```