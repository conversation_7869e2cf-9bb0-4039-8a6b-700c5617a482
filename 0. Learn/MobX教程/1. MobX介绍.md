# MobX介绍
MobX的哲学: 任何源自应用状态的东西都应该自动地获得。

## 核心概念

- Observable state(可观察的状态)

MobX 为现有的数据结构(如对象，数组和类实例)添加了可观察的功能。 通过使用 @observable 装饰器(ES.Next)来给你的类属性添加注解就可以简单地完成这一切。

- Computed values(计算值)

使用 MobX， 你可以定义在相关数据发生变化时自动更新的值。 通过@computed 装饰器或者利用 (extend)Observable 时调用 的getter / setter 函数来进行使用。(当然，这里也可以再次使用 decorate 来替代 @ 语法)。

- Reactions(反应)

Reactions 和计算值很像，但它不是产生一个新的值，而是会产生一些副作用，比如打印到控制台、网络请求、递增地更新 React 组件树以修补DOM、等等。 简而言之，reactions 在 [响应式编程](https://en.wikipedia.org/wiki/Reactive_programming)和[命令式编程](https://en.wikipedia.org/wiki/Imperative_programming)之间建立沟通的桥梁。

- React 组件

如果你用 React 的话，可以把你的(无状态函数)组件变成响应式组件，方法是在组件上添加 observer 函数/ 装饰器. observer由 mobx-react 包提供的。

- 自定义 reactions

使用autorun、reaction 和 when 函数即可简单的创建自定义 reactions，以满足你的具体场景。

- MobX 会对什么作出响应?
为什么每次 unfinishedTodoCount 变化时都会打印一条新消息？答案就是下面这条经验法则:

MobX 会对在执行跟踪函数期间读取的任何现有的可观察属性做出反应。

想深入了解 MobX 是如何知道需要对哪个可观察属性进行响应，请查阅 理解 [MobX 对什么有反应](https://cn.mobx.js.org/best/react.html)。

## 要点
但使用 MobX 将一个应用变成响应式的可归纳为以下三个步骤:

1. 定义状态并使其可观察

可以用任何你喜欢的数据结构来存储状态，如对象、数组、类。 循环数据结构、引用，都没有关系。 只要确保所有会随时间流逝而改变的属性打上 mobx 的标记使它们变得可观察即可。

```javascript
import {observable} from 'mobx';

var appState = observable({
    timer: 0
});
```

2. 创建视图以响应状态的变化

我们的 appState 还没有观察到任何的东西。 你可以创建视图，当 appState 中相关数据发生改变时视图会自动更新。 MobX 会以一种最小限度的方式来更新视图。 事实上这一点可以节省了你大量的样板文件，并且它有着[令人匪夷所思的高效](https://www.mendix.com/blog/making-react-reactive-pursuit-high-performing-easily-maintainable-react-apps/)。

通常来说，任何函数都可以成为可以观察自身数据的响应式视图，MobX 可以在任何符合ES5的JavaScript环境中应用。 但是在这所用的示例是 ES6版本的 React 组件视图。

```javascript
import {observer} from 'mobx-react';

@observer
class TimerView extends React.Component {
    render() {
        return (
            <button onClick={this.onReset.bind(this)}>
                Seconds passed: {this.props.appState.timer}
            </button>
        );
    }

    onReset() {
        this.props.appState.resetTimer();
    }
};

ReactDOM.render(<TimerView appState={appState} />, document.body);
```

3. 更改状态

第三件要做的事就是更改状态。 也就是你的应用究竟要做什么。 不像一些其它框架，MobX 不会命令你如何如何去做。 这是最佳实践，但关键要记住一点: **MobX 帮助你以一种简单直观的方式来完成工作**。

下面的代码每秒都会修改你的数据，而当需要的时候UI会自动更新。 无论是在**改变**状态的控制器函数中，还是在应该**更新**的视图中，都没有明确的关系定义。 使用 observable 来装饰你的**状态**和**视图**，这足以让 MobX检测所有关系了。

```javascript
appState.resetTimer = action(function reset() {
    appState.timer = 0;
});

setInterval(action(function tick() {
    appState.timer += 1;
}), 1000);
```

只有在严格模式(默认是不启用)下使用 MobX 时才需要 action 包装。 建议使用 action，因为它将帮助你更好地组织应用，并表达出一个函数修改状态的意图。 同时,它还自动应用事务以获得最佳性能。

## 概念和原则

### 概念
1. State(状态)
状态 是驱动应用的数据。 通常有像待办事项列表这样的领域特定状态，还有像当前已选元素的视图状态。 记住，状态就像是有数据的excel表格。

2. Derivations(衍生)
**任何**源自状态并且**不会再有任何进一步的相互作用的**东西就是衍生。 衍生以多种形式存在:

- 用户界面
- 衍生数据，比如剩下的待办事项的数量。
- 后端集成，比如把变化发送到服务器端。

MobX 区分了两种类型的衍生:

- **Computed values**(计算值) - 它们是永远可以使用纯函数(pure function)从当前可观察状态中衍生出的值。
- **Reactions**(反应) - Reactions 是当状态改变时需要自动发生的副作用。需要有一个桥梁来连接命令式编程(imperative programming)和响应式编程(reactive programming)。或者说得更明确一些，它们最终都需要实现I / O 操作。

刚开始使用 MobX 时，人们倾向于频繁的使用 reactions。 黄金法则: **如果你想创建一个基于当前状态的值时，请使用 computed**。

回到excel表格这个比喻中来，公式是计算值的衍生。但对于用户来说，能看到屏幕给出的反应则需要部分重绘GUI。

3. Actions(动作)
动作 是任一一段可以改变状态的代码。用户事件、后端数据推送、预定事件、等等。 动作类似于用户在excel单元格中输入一个新的值。

在 MobX 中可以显式地定义动作，它可以帮你把代码组织的更清晰。 如果是在严格模式下使用 MobX的话，MobX 会强制只有在动作之中才可以修改状态。

### 原则

MobX 支持单向数据流，也就是动作改变状态，而状态的改变会更新所有受影响的视图。

当状态改变时，所有衍生都会进行原子级的自动更新。因此永远不可能观察到中间值。

所有衍生默认都是同步更新。这意味着例如动作可以在改变状态之后直接可以安全地检查计算值。

计算值 是**延迟更新**的。任何不在使用状态的计算值将不会更新，直到需要它进行副作用（I / O）操作时。 如果视图不再使用，那么它会自动被垃圾回收。

所有的计算值都应该是纯净的。它们不应该用来改变状态。