# API概览

## 核心API
### observable(value)
Observable 值可以是JS基本数据类型、引用类型、普通对象、类实例、数组和映射。

`observable` 也可以用作属性的装饰器。它需要启用装饰器而且它是 `extendObservable(this, { property: value })` 的语法糖。

### 装饰器(Decorators)
可用的装饰器列表:
- observable.deep: 所有 observable 都使用的默认的装饰器。它可以把任何指定的、非原始数据类型的、非 observable 的值转换成 observable。
- observable.ref: 禁用自动的 observable 转换，只是创建一个 observable 引用。
- observable.shallow: 只能与集合组合使用。 将任何分配的集合转换为浅 observable (而不是深 observable)的集合。 换句话说, 集合中的值将不会自动变为 observable。
- computed: 创建一个衍生属性, 参见 computed
- action: 创建一个动作, 参见 action
- action.bound: 创建有范围的动作, 参见 action

### Computed values(计算值)

### Actions(动作)

### Flow

用来处理异步action

```javascript
import { configure } from 'mobx';

// 不允许在动作外部修改状态
configure({ enforceActions: true });

class Store {
    @observable githubProjects = [];
    @observable state = "pending"; // "pending" / "done" / "error"


    fetchProjects = flow(function* fetchProjects() { // <- 注意*号，这是生成器函数！
        this.githubProjects = [];
        this.state = "pending";
        try {
            const projects = yield fetchGithubProjectsSomehow(); // 用 yield 代替 await
            const filteredProjects = somePreprocessing(projects);

            // 异步代码自动会被 `action` 包装
            this.state = "done";
            this.githubProjects = filteredProjects;
        } catch (error) {
            this.state = "error";
        }
    })
}
```

Flows 可以撤销

Flows 支持异步迭代器
```javascript
async function* someNumbers() {
    yield Promise.resolve(1)
    yield Promise.resolve(2)
    yield Promise.resolve(3)
}

const count = mobx.flow(async function*() {
    // 使用 await 来循环异步迭代器
    for await (const number of someNumbers()) {
        total += number
    }
    return total
})

const res = await count() // 6

```

### Reactions(反应) & Derivations(衍生)
- observer
- autorun
用法：`autorun(() => { sideEffect }, options)` 。`autorun` 负责运行所提供的 `sideEffect` 并追踪在`sideEffect`运行期间访问过的 `observable` 的状态。 将来如果有其中一个已使用的 observable 发生变化，同样的`sideEffect`会再运行一遍。 `autorun` 返回一个清理函数用来取消副作用。
- when
用法: `when(() => condition, () => { sideEffect }, options)` 。 `condition` 表达式会自动响应任何它所使用的 observable。 一旦表达式返回的是真值，副作用函数便会立即调用，但只会调用一次。
