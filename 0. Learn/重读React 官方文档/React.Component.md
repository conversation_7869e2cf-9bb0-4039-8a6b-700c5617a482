# React.Component

## 概览
### 组件的生命周期
**挂载**，当组件实例被创建并插入 DOM 中时，其生命周期调用顺序如下：
- `constructor()`
- `static getDerivedStateFromProps()`
- `render()`
- `componentDidMount()`

**更新**，当组件的 props 或 state 发生变化时会触发更新。组件更新的生命周期调用顺序如下：
- `static getDerivedStateFromProps()`
- `shouldComponentUpdate()`
- `render()`
- `getSnapshotBeforeUpdate()`
- `componentDidUpdate()`

**卸载**，当组件从 DOM 中移除时会调用如下方法：
- `componentWillUnmount()`

**错误处理**，当渲染过程，生命周期，或子组件的构造函数中抛出错误时，会调用如下方法：
- `static getDerivedStateFromError()`
- `componentDidCatch()`

### 其他 APIs
组件还提供了一些额外的 API：
- `setState()`
- `forceUpdate()`

### class 属性
- `defaultProps`
- `displayName`

### 实例属性
- `props`
- `state`

## 参考

### 常用的生命周期方法

#### render()
render() 方法是 class 组件中唯一必须实现的方法。

render() 函数应该为纯函数，这意味着在不修改组件 state 的情况下，每次调用时都返回相同的结果，并且它不会直接与浏览器交互。

如果 shouldComponentUpdate() 返回 false，则不会调用 render()。

#### constructor()
```javascript
constructor(props)
```

在 React 组件挂载之前，会调用它的构造函数。在为 React.Component 子类实现构造函数时，应在其他语句之前前调用 super(props)。否则，this.props 在构造函数中可能会出现未定义的 bug。

通常，在 React 中，构造函数仅用于以下两种情况：
- 通过给 this.state 赋值对象来初始化内部 state。
- 为事件处理函数绑定实例(?, 暂时不理解)

要避免在构造函数中引入任何副作用或订阅。如遇到此场景，请将对应的操作放置在 componentDidMount 中。

将props的值复制给state会怎样？

如此做毫无必要（你可以直接使用 this.props.color），同时还产生了 bug（更新 prop 中的 color 时，并不会影响 state）。

只有在你刻意忽略 prop 更新的情况下使用。此时，应将 prop 重命名为 initialColor 或 defaultColor。必要时，你可以修改它的 key，以强制“重置”其内部 state。

请参阅关于避免派生状态的博文，以了解出现 state 依赖 props 的情况该如何处理。

#### componentDidMount()
componentDidMount() 会在组件挂载后（插入 DOM 树中）立即调用。依赖于 DOM 节点的初始化应该放在这里。如需通过网络请求获取数据，此处是实例化请求的好地方。

这个方法是比较适合添加订阅的地方。如果添加了订阅，请不要忘记在 componentWillUnmount() 里取消订阅

你可以在 componentDidMount() 里直接调用 setState()。**它将触发额外渲染，但此渲染会发生在浏览器更新屏幕之前**。如此保证了即使在 render() 两次调用的情况下，用户也不会看到中间状态。

#### componentDidUpdate()
```javascript
componentDidUpdate(prevProps, prevState, snapshot)
```

componentDidUpdate() 会在更新后会被立即调用。首次渲染不会执行此方法。

当组件更新后，可以在此处对 DOM 进行操作。如果你对更新前后的 props 进行了比较，也可以选择在此处进行网络请求。（例如，当 props 未发生变化时，则不会执行网络请求）。

```javascript
componentDidUpdate(prevProps) {
  // 典型用法（不要忘记比较 props）：
  if (this.props.userID !== prevProps.userID) {
    this.fetchData(this.props.userID);
  }
}
```

你也可以在 componentDidUpdate() 中直接调用 setState()，但请注意它必须被包裹在一个条件语句里，正如上述的例子那样进行处理，否则会导致死循环。

如果组件实现了 getSnapshotBeforeUpdate() 生命周期（不常用），则它的返回值将作为 componentDidUpdate() 的第三个参数 “snapshot” 参数传递。否则此参数将为 undefined。

#### componentWillUnmount()
componentWillUnmount() 会在组件卸载及销毁之前直接调用。在此方法中执行必要的清理操作，例如，清除 timer，取消网络请求或清除在 componentDidMount() 中创建的订阅等。

componentWillUnmount() 中不应调用 setState()，因为该组件将永远不会重新渲染。组件实例卸载后，将永远不会再挂载它。

#### shouldComponentUpdate()
```javascript
shouldComponentUpdate(nextProps, nextState)
```

根据 shouldComponentUpdate() 的返回值，判断 React 组件的输出是否受当前 state 或 props 更改的影响。默认行为是 state 每次发生变化组件都会重新渲染。大部分情况下，你应该遵循默认行为。

此方法仅作为性能优化的方式而存在。不要企图依靠此方法来“阻止”渲染，因为这可能会产生 bug。你应该考虑使用内置的 PureComponent 组件，而不是手动编写shouldComponentUpdate()。PureComponent 会对 props 和 state 进行浅层比较，并减少了跳过必要更新的可能性。

如果你一定要手动编写此函数，请注意，返回 false 并不会阻止子组件在 state 更改时重新渲染。

我们不建议在 shouldComponentUpdate() 中进行深层比较或使用 JSON.stringify()。这样非常影响效率，且会损害性能。

#### static getDerivedStateFromProps()
```javascript
static getDerivedStateFromProps(props, state)
```

getDerivedStateFromProps 会在调用 render 方法之前调用，并且在初始挂载及后续更新时都会被调用。它应返回一个对象来更新 state，如果返回 null 则不更新任何内容。

此方法适用于罕见的用例，即 state 的值在任何时候都取决于 props。例如，实现 <Transition> 组件可能很方便，该组件会比较当前组件与下一组件，以决定针对哪些组件进行转场动画。

- 如果你需要执行副作用（例如，数据提取或动画）以响应 props 中的更改，请改用 componentDidUpdate。
- 如果只想在 prop 更改时重新计算某些数据，请使用 memoization helper 代替。
- 如果你想在 prop 更改时“重置”某些 state，请考虑使组件完全受控或使用 key 使组件完全不受控 代替。

此方法无权访问组件实例。如果你需要，可以通过提取组件 props 的纯函数及 class 之外的状态，在getDerivedStateFromProps()和其他 class 方法之间重用代码。

请注意，不管原因是什么，都会在每次渲染前触发此方法。这与 UNSAFE_componentWillReceiveProps 形成对比，后者仅在父组件重新渲染时触发，而不是在内部调用 setState 时。

#### getSnapshotBeforeUpdate()
```javascript
getSnapshotBeforeUpdate(prevProps, prevState)
```

getSnapshotBeforeUpdate() 在最近一次渲染输出（提交到 DOM 节点）之前调用。它使得组件能在发生更改之前从 DOM 中捕获一些信息（例如，滚动位置）。此生命周期的任何返回值将作为参数传递给 componentDidUpdate()。

#### Error boundaries

#### static getDerivedStateFromError()
```javascript
static getDerivedStateFromError(error)
```

#### componentDidCatch()
```javascript
componentDidCatch(error, info)
```

#### setState()
```javascript
setState(updater, [callback])
```

后调用的 setState() 将覆盖同一周期内先调用 setState 的值，因此商品数仅增加一次。如果后续状态取决于当前状态，我们建议使用 updater 函数的形式代替：

```javascript
this.setState((state) => {
  return {quantity: state.quantity + 1};
});
```

#### forceUpdate()
```javascript
component.forceUpdate(callback)
```

默认情况下，当组件的 state 或 props 发生变化时，组件将重新渲染。如果 render() 方法依赖于其他数据，则可以调用 forceUpdate() 强制让组件重新渲染。

#### props

this.props 包括被该组件调用者定义的 props

#### state

组件中的 state 包含了随时可能发生变化的数据。state 由用户自定义，它是一个普通 JavaScript 对象。

## 思考
- PureComponent 跟 Component 有啥区别？
- 事件处理函数怎么绑定实例
- 实例是啥
- 将props的值复制给state会怎样？
- 构造函数现在的super被划伤删除符号是怎么回事？
- 派生states是什么，怎么避免？
- getSnapshotBeforeUpdate怎么会用到？
