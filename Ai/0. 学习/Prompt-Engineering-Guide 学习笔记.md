# Prompt-Engineering-Guide 学习笔记

## 目录
- Introduction 介绍
- Basic Prompting 基本提示
- Advanced Prompting 高级提示
- Applications 应用
- ChatGPT 
- Adversarial Prompting 对抗性提示
- Reliability 可靠性
- Miscellaneous Topics 杂项
- Papers 论文
- Tools 工具
- Datasets 数据集
- Additional Readings 附加阅读

## Basic Prompting 基本提示

在之前的指南中，我们介绍了并提供了一个简单的提示示例。

在这个指南中，我们将提供更多关于提示如何使用的示例，并介绍对于更高级指南至关重要的关键概念。

通常，学习概念的最佳方式是通过实例。以下是一些示例，展示了如何使用精心设计的提示来执行各种有趣和不同的任务。

- 文本摘要
- 信息提取
- 问答系统
- 文本分类
- 对话
- 代码生成
- 推理
- Python笔记本

 a prompt could combine instructions, context, input, and output indicators to get improved results

## Advanced Prompting 高级提示

