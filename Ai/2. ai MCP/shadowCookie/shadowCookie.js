const express = require('express');
const axios = require('axios');
const app = express();
const PORT = 5000;

// 目标内网服务地址（替换为你的实际地址）
const INTERNAL_SERVICE = 'http://内部服务地址';

// 代理所有请求
app.all('*', async (req, res) => {
  try {
    // 透传原始 Cookie
    const headers = {
      ...req.headers,
      cookie: req.headers.cookie || '', // 直接传递原始 Cookie 字符串
      host: new URL(INTERNAL_SERVICE).host // 修正 Host 避免干扰
    };

    // 转发请求到内网服务
    const response = await axios({
      method: req.method,
      url: INTERNAL_SERVICE + req.path,
      headers: headers,
      data: req.body,
      params: req.query,
      responseType: 'arraybuffer' // 确保正确处理二进制响应
    });

    // 透传 Set-Cookie 和其他头
    const resHeaders = { ...response.headers };
    if (resHeaders['set-cookie']) {
      res.setHeader('set-cookie', resHeaders['set-cookie']);
    }
    delete resHeaders['content-encoding']; // 避免编码冲突

    res.writeHead(response.status, resHeaders);
    res.end(Buffer.from(response.data));
  } catch (error) {
    console.error('代理错误:', error.message);
    res.status(500).send('代理服务异常');
  }
});

// 启动服务
app.listen(PORT, () => {
  console.log(`本地 MCP 运行在 http://localhost:${PORT}`);
});