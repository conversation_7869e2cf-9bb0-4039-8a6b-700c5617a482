// ==UserScript==
// @name         React补货页面接口提示(最终调试版)
// @namespace    http://tampermonkey.net/
// @version      3.0
// @description  完全适配React的动态渲染和定位问题
// @match        https://wms-test01.dotfashion.cn/*
// @grant        GM_addStyle
// @grant        GM_log
// ==/UserScript==

(function () {
  'use strict';
  const DEBUG = true; // 调试模式开关

  // ================== 配置区 ==================
  const pageData = {
    "pagePath": "/in-warehouse/replenish",
    "buttons": [
      {
        "buttonName": "下发任务",
        "apiInfo": {
          "url": "/wws/front/replenish_task/assignTask",
          "permissionConditions": "勾选的单据状态必须是0待下发，单据类型必须是4补货/6回货/9冻结移位",
          "limitConditions": "",
          "relatedApis": []
        }
      },
      {
        "buttonName": "任务指派",
        "apiInfo": {
          "url": "/wws/front/task_assignment",
          "permissionConditions": "勾选的单据状态必须是0待下发 1待下架，单据类型必须是4补货，下架方式不为自动化下架，库区=amr库区参数配置范围内的，指派置灰",
          "limitConditions": "需要同一子仓",
          "relatedApis": [
            {
              "url": "/osm/front/operator_group/query_group_by_subwarehouse"
            }
          ]
        }
      },
      {
        "buttonName": "作废",
        "apiInfo": {
          "url": "/wws/front/replenish_task/cancelTask",
          "permissionConditions": "勾选的单据状态必须是0待下发 1待下架",
          "limitConditions": "最多选择20条",
          "relatedApis": []
        }
      },
      {
        "buttonName": "导出",
        "apiInfo": {
          "url": "/wws/front/replenish_task/export_list",
          "permissionConditions": "页面加载完成后",
          "limitConditions": "不在简单搜索模式下",
          "relatedApis": []
        }
      },
      {
        "buttonName": "导入自动化移位任务",
        "apiInfo": {
          "url": "/wgs/front/file_import/record/wws/auto_shift_task_import",
          "permissionConditions": "",
          "limitConditions": "",
          "relatedApis": [
            {
              "url": "/wws/front/auto_shift_task/download_template"
            }
          ]
        }
      },
      {
        "buttonName": "批量作废",
        "apiInfo": {
          "url": "/wws/front/replenish_task/batch_cancel_task",
          "permissionConditions": "状态必须为待下发/待下架，下架子仓/库区必须有值，如果下架子仓选中则单选，单据类别必填，单据类型必填，创建时间必填",
          "limitConditions": "",
          "relatedApis": [
            {
              "url": "/wws/front/replenish_task/load_pre_handle_task_list"
            },
            {
              "url": "/wws/front/replenish_task/load_cancel_task_list"
            }
          ]
        }
      },
      {
        "buttonName": "批量下发",
        "apiInfo": {
          "url": "/wws/front/replenish_task/batch_assign_task",
          "permissionConditions": "状态必须为待下发，下架子仓/库区必须有值，单据类别必填，单据类型必填，创建时间必填",
          "limitConditions": "",
          "relatedApis": [
            {
              "url": "/wws/front/replenish_task/load_pre_handle_task_list"
            },
            {
              "url": "/wws/front/replenish_task/load_batch_task_list"
            }
          ]
        }
      },
      {
        "buttonName": "下载导入模版",
        "apiInfo": {
          "url": "/wws/front/auto_shift_task/download_template",
          "permissionConditions": "",
          "limitConditions": "",
          "relatedApis": []
        }
      }
    ]
  };

  // ================== 样式增强 ==================
  GM_addStyle(`
      /* 调试边框 */
      .so-button[data-api-matched] {
          outline: 2px solid #52c41a !important;
          position: relative;
      }

      /* 提示框核心样式 */
      .api-tooltip {
          position: fixed !important;
          background: white !important;
          border: 2px solid #1890ff !important;
          border-radius: 4px !important;
          padding: 12px !important;
          max-width: 400px !important;
          z-index: 2147483647 !important; /* 最大合法值 */
          box-shadow: 0 3px 6px -4px rgba(0,0,0,0.12), 0 6px 16px 0 rgba(0,0,0,0.08) !important;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto !important;
          opacity: 1 !important;
          pointer-events: none !important;
          transition: opacity 0.2s !important;
      }

      /* 隐藏滚动条影响 */
      .api-tooltip::before {
          content: '';
          position: fixed;
          top: -100vh;
          left: -100vw;
          right: -100vw;
          bottom: -100vh;
          z-index: -1;
      }
  `);

  // ================== 工具函数 ==================
  function debugLog(...args) {
    if (DEBUG) console.log('[API-DEBUG]', ...args);
  }

  // ================== 核心逻辑 ==================
  // 创建全局提示框
  const tooltip = document.createElement('div');
  tooltip.className = 'api-tooltip';
  tooltip.style.display = 'none';
  document.body.appendChild(tooltip);
  debugLog('提示框元素已创建:', tooltip);

  // 构建按钮映射表（不区分大小写和空格）
  const buttonMap = new Map();
  pageData.buttons.forEach(btn => {
    const key = btn.buttonName.trim().toLowerCase().replace(/\s+/g, '');
    buttonMap.set(key, btn.apiInfo);
    debugLog('映射表添加:', key, '->', btn.apiInfo.url);
  });

  // 按钮处理函数
  function processButton(button) {
    try {
      // 防重复处理
      if (button.dataset.apiProcessed === 'true') return;

      // 获取标准化文本
      const btnText = Array.from(button.querySelectorAll('span, div'))
        .reduce((acc, el) => acc + el.textContent.trim() + ' ', '')
        .trim()
        .toLowerCase()
        .replace(/\s+/g, '');

      debugLog('处理按钮:', {
        element: button,
        rawText: button.innerText,
        normalized: btnText
      });

      if (buttonMap.has(btnText)) {
        // 标记匹配成功
        button.dataset.apiMatched = 'true';
        button.dataset.apiProcessed = 'true';
        debugLog('✅ 按钮匹配成功:', btnText);

        // 事件绑定
        button.addEventListener('mouseenter', showTooltip);
        button.addEventListener('mousemove', updateTooltipPosition);
        button.addEventListener('mouseleave', hideTooltip);

        // 调试样式
        if (DEBUG) {
          button.style.setProperty('outline', '2px solid #52c41a', 'important');
        }
        return true;
      }
      return false;
    } catch (e) {
      debugLog('处理按钮时出错:', e);
      return false;
    }
  }

  // ================== 事件处理 ==================
  function showTooltip(e) {
    try {
      const button = e.currentTarget;
      const btnText = button.innerText.trim();
      const normalizedKey = btnText.toLowerCase().replace(/\s+/g, '');
      const apiInfo = buttonMap.get(normalizedKey);

      debugLog('触发显示提示框:', apiInfo.url);

      // 计算精准定位
      const rect = button.getBoundingClientRect();
      const viewportWidth = window.innerWidth;

      let posX = rect.right + 15;
      let posY = rect.top - 5;

      // 右侧越界检测
      if (posX + tooltip.offsetWidth > viewportWidth) {
        posX = rect.left - tooltip.offsetWidth - 15;
      }

      tooltip.style.cssText = `
              display: block !important;
              left: ${posX + window.scrollX}px !important;
              top: ${posY + window.scrollY}px !important;
              opacity: 1 !important;
          `;

      tooltip.innerHTML = `
              <div style="color: #1890ff; margin-bottom: 8px; font-weight: 500;">${btnText}</div>
              <div style="margin-bottom: 6px;"><strong>接口地址：</strong><code>${apiInfo.url}</code></div>
              ${apiInfo.permissionConditions ? `<div style="margin-bottom: 4px;"><strong>权限条件：</strong>${apiInfo.permissionConditions}</div>` : ''}
              ${apiInfo.limitConditions ? `<div style="margin-bottom: 4px;"><strong>限制条件：</strong>${apiInfo.limitConditions}</div>` : ''}
              ${apiInfo.relatedApis?.length ? `<div><strong>关联接口：</strong><br>${apiInfo.relatedApis.map(a => `• ${a.url}`).join('<br>')}</div>` : ''}
          `;

    } catch (error) {
      console.error('提示框显示失败:', error);
    }
  }

  function updateTooltipPosition(e) {
    const mouseX = e.clientX;
    const mouseY = e.clientY;
    tooltip.style.left = `${mouseX + 15}px`;
    tooltip.style.top = `${mouseY + 15}px`;
  }

  function hideTooltip() {
    tooltip.style.opacity = '0';
    setTimeout(() => tooltip.style.display = 'none', 200);
  }

  // ================== DOM观察器 ==================
  const observer = new MutationObserver(mutations => {
    debugLog('检测到DOM变化，开始扫描按钮...');
    document.querySelectorAll('.so-button:not([data-api-processed])').forEach(processButton);
  });

  // ================== 初始化 ==================
  function init() {
    debugLog('脚本初始化开始');

    // 初始扫描
    document.querySelectorAll('.so-button').forEach(processButton);

    // 启动观察器
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false,
      characterData: false
    });

    // 定时强制刷新(调试用)
    if (DEBUG) {
      setInterval(() => {
        debugLog('定时强制刷新按钮状态');
        document.querySelectorAll('.so-button').forEach(btn => {
          btn.dataset.apiProcessed = 'false';
        });
      }, 3000);
    }
  }

  // 启动脚本
  if (document.readyState === 'complete') {
    init();
  } else {
    window.addEventListener('load', init);
  }
})();