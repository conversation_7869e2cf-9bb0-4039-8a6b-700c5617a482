// ==UserScript==
// @name         React补货页面接口提示(调试版)
// @namespace    http://tampermonkey.net/
// @version      2.1
// @description  带调试功能的接口提示脚本
// @match        https://wms-test01.dotfashion.cn*
// @grant        GM_addStyle
// @grant        GM_log
// ==/UserScript==

(function() {
  'use strict';
  const DEBUG = true; // 调试模式开关

  // 完整数据替换区=============================================
  const pageData = {
      "pagePath": "/in-warehouse/replenish",
      "buttons": [
      {
        "buttonName": "下发任务",
        "apiInfo": {
          "url": "/wws/front/replenish_task/assignTask",
          "permissionConditions": "勾选的单据状态必须是0待下发，单据类型必须是4补货/6回货/9冻结移位",
          "limitConditions": "",
          "relatedApis": []
        }
      },
      {
        "buttonName": "任务指派",
        "apiInfo": {
          "url": "/wws/front/task_assignment",
          "permissionConditions": "勾选的单据状态必须是0待下发 1待下架，单据类型必须是4补货，下架方式不为自动化下架，库区=amr库区参数配置范围内的，指派置灰",
          "limitConditions": "需要同一子仓",
          "relatedApis": [
            {
              "url": "/osm/front/operator_group/query_group_by_subwarehouse"
            }
          ]
        }
      },
      {
        "buttonName": "作废",
        "apiInfo": {
          "url": "/wws/front/replenish_task/cancelTask",
          "permissionConditions": "勾选的单据状态必须是0待下发 1待下架",
          "limitConditions": "最多选择20条",
          "relatedApis": []
        }
      },
      {
        "buttonName": "导出",
        "apiInfo": {
          "url": "/wws/front/replenish_task/export_list",
          "permissionConditions": "页面加载完成后",
          "limitConditions": "不在简单搜索模式下",
          "relatedApis": []
        }
      },
      {
        "buttonName": "导入自动化移位任务",
        "apiInfo": {
          "url": "/wgs/front/file_import/record/wws/auto_shift_task_import",
          "permissionConditions": "",
          "limitConditions": "",
          "relatedApis": [
            {
              "url": "/wws/front/auto_shift_task/download_template"
            }
          ]
        }
      },
      {
        "buttonName": "批量作废",
        "apiInfo": {
          "url": "/wws/front/replenish_task/batch_cancel_task",
          "permissionConditions": "状态必须为待下发/待下架，下架子仓/库区必须有值，如果下架子仓选中则单选，单据类别必填，单据类型必填，创建时间必填",
          "limitConditions": "",
          "relatedApis": [
            {
              "url": "/wws/front/replenish_task/load_pre_handle_task_list"
            },
            {
              "url": "/wws/front/replenish_task/load_cancel_task_list"
            }
          ]
        }
      },
      {
        "buttonName": "批量下发",
        "apiInfo": {
          "url": "/wws/front/replenish_task/batch_assign_task",
          "permissionConditions": "状态必须为待下发，下架子仓/库区必须有值，单据类别必填，单据类型必填，创建时间必填",
          "limitConditions": "",
          "relatedApis": [
            {
              "url": "/wws/front/replenish_task/load_pre_handle_task_list"
            },
            {
              "url": "/wws/front/replenish_task/load_batch_task_list"
            }
          ]
        }
      },
      {
        "buttonName": "下载导入模版",
        "apiInfo": {
          "url": "/wws/front/auto_shift_task/download_template",
          "permissionConditions": "",
          "limitConditions": "",
          "relatedApis": []
        }
      }
    ]
  };

  // 样式增强===================================================
  GM_addStyle(`
      /* 调试边框 - 匹配成功时显示绿色边框 */
      .so-button[data-api-debug="matched"] {
          box-shadow: 0 0 0 2px limegreen !important;
      }

      /* 工具提示样式 */
      .api-tooltip {
          position: fixed;
          background: #fff;
          border: 2px solid #1677ff;
          padding: 12px;
          border-radius: 8px;
          z-index: 9999;
          max-width: 500px;
          font-family: monospace;
          font-size: 13px;
          pointer-events: none;
          opacity: 0.95;
      }
  `);

  // 调试日志函数
  function debugLog(...args) {
      if(DEBUG) console.log('[API提示脚本]', ...args);
  }

  // 初始化工具提示=============================================
  const tooltip = document.createElement('div');
  tooltip.className = 'api-tooltip';
  tooltip.style.display = 'none';
  document.body.appendChild(tooltip);
  debugLog('工具提示元素已创建');

  // 构建按钮映射===============================================
  const buttonMap = new Map();
  pageData.buttons.forEach(btn => {
      const key = btn.buttonName.trim().toLowerCase().replace(/\s+/g, '');
      buttonMap.set(key, btn.apiInfo);
      debugLog(`映射表添加: ${key} -> ${btn.apiInfo.url}`);
  });

  // 核心处理函数===============================================
  function processButton(button) {
      try {
          // 获取按钮文本（兼容React多层级结构）
          const textNodes = Array.from(button.querySelectorAll('*'))
              .filter(n => n.childNodes.length === 1 && n.childNodes[0].nodeType === Node.TEXT_NODE)
              .map(n => n.textContent.trim());

          const btnText = textNodes.join(' ') || button.innerText.trim();
          const normalizedText = btnText.toLowerCase().replace(/\s+/g, '');
          
          debugLog('检测到按钮:', {
              element: button,
              rawText: btnText,
              normalized: normalizedText
          });

          if(buttonMap.has(normalizedText)) {
              // 添加调试标记
              button.style.setProperty('border', '2px solid limegreen', 'important');
              button.dataset.apiDebug = "matched";
              
              debugLog('✅ 匹配成功', {
                  text: normalizedText,
                  apiInfo: buttonMap.get(normalizedText)
              });

              // 事件绑定
              button.addEventListener('mouseenter', handleMouseEnter);
              button.addEventListener('mousemove', handleMouseMove);
              button.addEventListener('mouseleave', handleMouseLeave);
              
              return true;
          }
          return false;
      } catch (e) {
          debugLog('处理按钮时出错:', e);
          return false;
      }
  }

  // 事件处理函数===============================================
  function handleMouseEnter(e) {
      const button = e.currentTarget;
      const normalizedText = button.dataset.apiDebugText;
      const apiInfo = buttonMap.get(normalizedText);

      tooltip.innerHTML = `
          <div style="color: #1677ff; margin-bottom: 8px;"><strong>${button.innerText.trim()}</strong></div>
          <div><code>${apiInfo.url}</code></div>
          ${apiInfo.permissionConditions ? `<div>权限: ${apiInfo.permissionConditions}</div>` : ''}
          ${apiInfo.limitConditions ? `<div>限制: ${apiInfo.limitConditions}</div>` : ''}
      `;
      
      tooltip.style.display = 'block';
      debugLog('显示提示', apiInfo);
  }

  function handleMouseMove(e) {
      tooltip.style.left = `${e.pageX + 20}px`;
      tooltip.style.top = `${e.pageY + 20}px`;
  }

  function handleMouseLeave() {
      tooltip.style.display = 'none';
  }

  // 动态检测机制===============================================
  const observer = new MutationObserver(mutations => {
      debugLog('DOM变化检测到', mutations.length, '个变更');
      
      // 每次DOM变化时全量检查
      const buttons = document.querySelectorAll('.so-button');
      debugLog('当前页面按钮数量:', buttons.length);
      
      buttons.forEach(button => {
          if(!button.dataset.apiProcessed) {
              const result = processButton(button);
              button.dataset.apiProcessed = result ? 'true' : 'false';
          }
      });
  });

  // 启动观察===================================================
  function start() {
      debugLog('脚本启动');
      
      // 立即执行首次扫描
      document.querySelectorAll('.so-button').forEach(button => {
          button.dataset.apiProcessed = 'false';
      });
      
      // 开始观察DOM变化
      observer.observe(document.body, {
          childList: true,
          subtree: true,
          attributes: false,
          characterData: false
      });
      
      // 每5秒强制刷新一次（用于调试SPA）
      if(DEBUG) {
          setInterval(() => {
              debugLog('定时强制刷新');
              document.querySelectorAll('.so-button').forEach(button => {
                  button.dataset.apiProcessed = 'false';
              });
          }, 5000);
      }
  }

  // 页面加载完成后启动
  if(document.readyState === 'complete') {
      start();
  } else {
      window.addEventListener('load', start);
  }
})();