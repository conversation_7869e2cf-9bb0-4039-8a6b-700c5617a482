// ==UserScript==
// @name         React补货页面接口提示
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  适配React动态渲染的按钮接口提示
// @match        https://wms-test01.dotfashion.cn/#/in-warehouse/replenish*
// @grant        GM_addStyle
// ==/UserScript==

(function() {
  'use strict';

  // 完整按钮数据（替换为你的data.json内容）
  const pageData = {
      "pagePath": "/in-warehouse/replenish",
      "buttons": [
        {
          "buttonName": "下发任务",
          "apiInfo": {
            "url": "/wws/front/replenish_task/assignTask",
            "permissionConditions": "勾选的单据状态必须是0待下发，单据类型必须是4补货/6回货/9冻结移位",
            "limitConditions": "",
            "relatedApis": []
          }
        },
        {
          "buttonName": "任务指派",
          "apiInfo": {
            "url": "/wws/front/task_assignment",
            "permissionConditions": "勾选的单据状态必须是0待下发 1待下架，单据类型必须是4补货，下架方式不为自动化下架，库区=amr库区参数配置范围内的，指派置灰",
            "limitConditions": "需要同一子仓",
            "relatedApis": [
              {
                "url": "/osm/front/operator_group/query_group_by_subwarehouse"
              }
            ]
          }
        },
        {
          "buttonName": "作废",
          "apiInfo": {
            "url": "/wws/front/replenish_task/cancelTask",
            "permissionConditions": "勾选的单据状态必须是0待下发 1待下架",
            "limitConditions": "最多选择20条",
            "relatedApis": []
          }
        },
        {
          "buttonName": "导出",
          "apiInfo": {
            "url": "/wws/front/replenish_task/export_list",
            "permissionConditions": "页面加载完成后",
            "limitConditions": "不在简单搜索模式下",
            "relatedApis": []
          }
        },
        {
          "buttonName": "导入自动化移位任务",
          "apiInfo": {
            "url": "/wgs/front/file_import/record/wws/auto_shift_task_import",
            "permissionConditions": "",
            "limitConditions": "",
            "relatedApis": [
              {
                "url": "/wws/front/auto_shift_task/download_template"
              }
            ]
          }
        },
        {
          "buttonName": "批量作废",
          "apiInfo": {
            "url": "/wws/front/replenish_task/batch_cancel_task",
            "permissionConditions": "状态必须为待下发/待下架，下架子仓/库区必须有值，如果下架子仓选中则单选，单据类别必填，单据类型必填，创建时间必填",
            "limitConditions": "",
            "relatedApis": [
              {
                "url": "/wws/front/replenish_task/load_pre_handle_task_list"
              },
              {
                "url": "/wws/front/replenish_task/load_cancel_task_list"
              }
            ]
          }
        },
        {
          "buttonName": "批量下发",
          "apiInfo": {
            "url": "/wws/front/replenish_task/batch_assign_task",
            "permissionConditions": "状态必须为待下发，下架子仓/库区必须有值，单据类别必填，单据类型必填，创建时间必填",
            "limitConditions": "",
            "relatedApis": [
              {
                "url": "/wws/front/replenish_task/load_pre_handle_task_list"
              },
              {
                "url": "/wws/front/replenish_task/load_batch_task_list"
              }
            ]
          }
        },
        {
          "buttonName": "下载导入模版",
          "apiInfo": {
            "url": "/wws/front/auto_shift_task/download_template",
            "permissionConditions": "",
            "limitConditions": "",
            "relatedApis": []
          }
        }
      ]
  };

  // 样式优化
  GM_addStyle(`
      .api-tooltip {
          position: fixed;
          background: #fff;
          border: 1px solid #d9d9d9;
          padding: 12px;
          border-radius: 6px;
          box-shadow: 0 3px 6px -4px rgba(0,0,0,0.12), 0 6px 16px 0 rgba(0,0,0,0.08);
          z-index: 9999;
          max-width: 420px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto;
          font-size: 13px;
          pointer-events: none;
          animation: fadeIn 0.2s;
      }
      @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-5px); }
          to { opacity: 1; transform: translateY(0); }
      }
      .api-info strong {
          display: block;
          margin: 8px 0 4px;
          color: #1d39c4;
          font-weight: 500;
      }
      .api-info p {
          margin: 0 0 8px;
          color: #595959;
          line-height: 1.6;
      }
  `);

  // 创建工具提示
  const tooltip = document.createElement('div');
  tooltip.className = 'api-tooltip';
  document.body.appendChild(tooltip);

  // 构建按钮映射
  const buttonMap = new Map(pageData.buttons.map(btn => [btn.buttonName.trim(), btn.apiInfo]));

  // React组件更新处理
  let isObserving = false;
  const activeButtons = new WeakSet();

  function handleButton(element) {
      if (activeButtons.has(element)) return;

      const btnText = Array.from(element.querySelectorAll('span,div'))
          .map(el => el.innerText.trim())
          .join(' ')
          .replace(/\s+/g, ' ');

      if (buttonMap.has(btnText)) {
          activeButtons.add(element);

          // 事件绑定
          const showTip = (e) => {
              const apiInfo = buttonMap.get(btnText);
              tooltip.innerHTML = `
                  <div class="api-info">
                      <strong>API Path:</strong>
                      <p>${apiInfo.url}</p>
                      ${apiInfo.permissionConditions ? `
                      <strong>权限条件:</strong>
                      <p>${apiInfo.permissionConditions}</p>
                      ` : ''}
                      ${apiInfo.limitConditions ? `
                      <strong>限制条件:</strong>
                      <p>${apiInfo.limitConditions}</p>
                      ` : ''}
                      ${apiInfo.relatedApis?.length ? `
                      <strong>关联接口:</strong>
                      <p>${apiInfo.relatedApis.map(api => api.url).join('<br>')}</p>
                      ` : ''}
                  </div>
              `;
              tooltip.style.display = 'block';
              tooltip.style.left = `${e.pageX + 20}px`;
              tooltip.style.top = `${e.pageY + 20}px`;
          };

          const hideTip = () => {
              tooltip.style.display = 'none';
          };

          element.addEventListener('mouseenter', showTip);
          element.addEventListener('mouseleave', hideTip);
          element.addEventListener('mousemove', (e) => {
              tooltip.style.left = `${e.pageX + 20}px`;
              tooltip.style.top = `${e.pageY + 20}px`;
          });
      }
  }

  // 优化后的观察器配置
  const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
              mutation.addedNodes.forEach(node => {
                  if (node.nodeType === 1) { // Element node
                      if (node.matches('.so-button')) {
                          handleButton(node);
                      }
                      node.querySelectorAll?.('.so-button').forEach(handleButton);
                  }
              });
          }
      });
  });

  // 启动观察
  const startObserving = () => {
      if (!isObserving) {
          observer.observe(document.body, {
              childList: true,
              subtree: true,
              attributes: false,
              characterData: false
          });
          isObserving = true;
      }
      // 初始绑定
      document.querySelectorAll('.so-button').forEach(handleButton);
  };

  // 处理路由切换
  const originalPushState = history.pushState;
  history.pushState = function(...args) {
      originalPushState.apply(this, args);
      setTimeout(startObserving, 300); // 等待React完成渲染
  };

  // 处理SPA初始化
  if (document.readyState === 'complete') {
      startObserving();
  } else {
      window.addEventListener('load', startObserving, { once: true });
  }
})();