# 炼丹记录

## prompt

请分析[页面路径]下的handle.jsx、reducers.js和server.js文件，整理出页面上所有按钮对应的接口关系。

需要确保：
1. 分析所有在handle.jsx中存在的按钮
2. 查找每个按钮点击事件触发的处理函数
3. 在reducers.js中找到对应的处理逻辑
4. 在server.js中找到调用的接口定义
5. 补全完整的接口URL，注意环境变量映射(如process.env.XXX_URI)在config/.legorc.ts文件中

输出格式为严格的JSON，必须包含以下结构：
```json
{
  "pagePath": "页面路径",
  "buttons": [
    {
      "buttonName": "按钮名称",
      "apiInfo": {
        "url": "完整接口URL包含环境变量解析后的前缀",
        "permissionConditions": "按钮启用条件描述",
        "limitConditions": "使用限制条件，如有",
        "relatedApis": [
          {
            "url": "完整接口URL",
          }
        ]
      }
    }
  ]
}
```

请确保输出的JSON结构完全符合上述格式，不要添加其他字段，也不要遗漏必要字段。如果某字段没有相关信息，使用空字符串或空数组。

## 1

编辑器：cursor 
模型: 3.7 
prompt:对话

输出：
```json
{
  "pageName": "补货任务管理页面",
  "pagePath": "packages/base/src/component/in-warehouse/replenish",
  "buttons": [
    {
      "buttonName": "下发任务",
      "apiInfo": {
        "url": "/wws/front/replenish_task/assignTask",
        "method": "POST",
        "functionName": "assignTask",
        "requestParams": {
          "ids": "选中任务ID数组"
        },
        "permissionConditions": "选中的任务状态为0待下发，且单据类型为4补货/6回货/9冻结移位"
      }
    },
    {
      "buttonName": "作废",
      "apiInfo": {
        "url": "/wws/front/replenish_task/cancelTask",
        "method": "POST",
        "functionName": "cancelTask",
        "requestParams": {
          "ids": "选中任务ID数组"
        },
        "permissionConditions": "选中的任务状态为0待下发或1待下架",
        "limitConditions": "最多选择20条记录"
      }
    },
    {
      "buttonName": "任务指派",
      "apiInfo": {
        "url": "/wws/front/task_assignment",
        "method": "POST",
        "functionName": "taskUnderServerAPI",
        "requestParams": {
          "ids": "任务ID数组",
          "underUser": "作业人员",
          "underGroup": "用户组ID"
        },
        "relatedApis": [
          {
            "name": "获取用户组列表",
            "url": "/osm/front/operator_group/query_group_by_subwarehouse",
            "method": "POST",
            "functionName": "queryGroupBySubwarehouseAPI",
            "requestParams": {
              "subWarehouseId": "子仓ID"
            }
          },
          {
            "name": "获取作业人员列表",
            "url": "/osm/front/operator_group/query_user_by_id",
            "method": "POST",
            "functionName": "queryUserByIdAPI",
            "requestParams": {
              "id": "用户组ID"
            }
          }
        ],
        "permissionConditions": "选中的任务状态为0待下发或1待下架，单据类型为4补货，下架方式不为自动化下架(2)，且库区不在AMR库区配置范围内，并且选中记录必须为同一子仓"
      }
    },
    {
      "buttonName": "导出",
      "apiInfo": {
        "url": "/wws/front/replenish_task/export_list",
        "method": "POST",
        "functionName": "exportList",
        "requestParams": {
          "billCategory": "单据类别",
          "warehouseId": "仓库ID",
          "replenishmentCodes": "任务单号数组",
          "其他查询条件": "同查询条件"
        }
      }
    },
    {
      "buttonName": "导入自动化移位任务",
      "apiInfo": {
        "url": "/wgs/front/file_import/record/wws/auto_shift_task_import",
        "method": "POST",
        "functionName": "formdataPost",
        "requestParams": {
          "file": "上传的Excel文件",
          "function_node": "58"
        }
      }
    },
    {
      "buttonName": "下载导入模版",
      "apiInfo": {
        "url": "/wws/front/auto_shift_task/download_template",
        "method": "POST",
        "functionName": "downloadAPI",
        "requestParams": {}
      }
    }
  ]
}
```

评价：
基本完成，但有缺漏

## 2
编辑器：vscode copilot
模型: 3.7
prompt:
```
页面路径：src/component/in-warehouse/replenish

请分析[页面路径]下的handle.jsx、reducers.js和server.js文件，整理出页面上所有按钮对应的接口关系。

需要确保：

分析所有在handle.jsx中存在的按钮
查找每个按钮点击事件触发的处理函数
在reducers.js中找到对应的处理逻辑
在server.js中找到调用的接口定义
补全完整的接口URL，注意环境变量映射(如process.env.XXX_URI)在config/.legorc.ts文件中
输出格式为JSON，包含以下信息：

页面名称和路径
每个按钮的名称
每个按钮对应的接口信息(完整URL、方法、参数说明)
按钮的使用条件或权限限制
相关依赖接口(如获取下拉数据)
请确保输出的JSON结构清晰，便于后续使用。
```


输出
```json
{
  "pageName": "补货任务页面",
  "pagePath": "/in-warehouse/replenish",
  "buttons": [
    {
      "name": "下发任务",
      "api": {
        "url": "/wws/front/replenish_task/assignTask",
        "method": "POST",
        "params": {
          "ids": ["Selected task ids"]
        }
      },
      "conditions": [
        "Must have selected rows",
        "All selected tasks must have status=0 (待下发)",
        "All selected tasks must have billCategory=4 (补货) or 6 (回货) or 9 (冻结移位)"
      ],
      "dependentAPIs": []
    },
    {
      "name": "任务指派",
      "api": {
        "url": "/wws/front/task_assignment",
        "method": "POST",
        "params": {
          "ids": ["Selected task ids"],
          "underUser": "Selected operator username",
          "underGroup": "Selected operator group ID"
        }
      },
      "conditions": [
        "Must have selected rows",
        "All selected tasks must be from the same subWarehouse",
        "All selected tasks must have status=0 (待下发) or 1 (待下架)",
        "All selected tasks must have billCategory=4 (补货)",
        "All selected tasks must not have underWay=2 (自动化下架)",
        "All selected tasks must not have areaId included in the AMR_AREA_IDS configuration"
      ],
      "dependentAPIs": [
        {
          "name": "获取用户组列表",
          "url": "/osm/front/operator_group/query_group_by_subwarehouse",
          "method": "POST",
          "params": {
            "subWarehouseId": "Subwarehouse ID"
          }
        },
        {
          "name": "获取作业人员列表",
          "url": "/common/queryUserById",
          "method": "POST",
          "params": {
            "id": "Group ID"
          }
        }
      ]
    },
    {
      "name": "作废",
      "api": {
        "url": "/wws/front/replenish_task/cancelTask",
        "method": "POST",
        "params": {
          "ids": ["Selected task ids"]
        }
      },
      "conditions": [
        "Must have selected rows",
        "All selected tasks must have status=0 (待下发) or 1 (待下架)",
        "Maximum 20 tasks can be selected at once"
      ],
      "dependentAPIs": []
    },
    {
      "name": "导出",
      "api": {
        "url": "/wws/front/replenish_task/export_list",
        "method": "POST",
        "params": {
          "limit": "Search criteria",
          "billCategory": "Bill category code",
          "warehouseId": "Warehouse ID",
          "replenishmentCodes": ["Task numbers"]
        }
      },
      "conditions": [
        "Not in simple search mode",
        "Form validation must pass"
      ],
      "dependentAPIs": []
    },
    {
      "name": "导入自动化移位任务",
      "api": {
        "url": "/wgs/front/file_import/record/wws/auto_shift_task_import",
        "method": "POST",
        "params": {
          "file": "Excel file object",
          "function_node": "58"
        }
      },
      "conditions": [
        "File must be selected",
        "File must have .xls or .xlsx extension"
      ],
      "dependentAPIs": [
        {
          "name": "下载模板",
          "url": "/wws/front/auto_shift_task/download_template",
          "method": "POST",
          "params": {}
        }
      ]
    },
    {
      "name": "批量作废",
      "api": {
        "url": "/wws/front/replenish_task/batch_cancel_task",
        "method": "POST",
        "params": {
          "limit": "Search criteria",
          "billCategory": "Bill category code",
          "warehouseId": "Warehouse ID",
          "replenishmentCodes": ["Task numbers"],
          "totalNumLimit": "Number limit (optional)",
          "totalTaskLimit": "Task limit (optional)"
        }
      },
      "conditions": [
        "Status must be [0] (待下发) or [1] (待下架) or [0,1]",
        "Either subWarehouseId or areaId must be selected",
        "If subWarehouseId is selected, only one must be selected",
        "billCategory must not be '-1'",
        "orderType must be selected",
        "timeType must be 1 (创建时间)",
        "beginTime and endTime must be selected"
      ],
      "dependentAPIs": [
        {
          "name": "获取批量作废任务信息",
          "url": "/wws/front/replenish_task/load_pre_handle_task_list",
          "method": "POST",
          "params": {
            "limit": "Search criteria",
            "billCategory": "Bill category code",
            "warehouseId": "Warehouse ID",
            "replenishmentCodes": ["Task numbers"]
          }
        },
        {
          "name": "获取批量作废任务进度",
          "url": "/wws/front/replenish_task/load_cancel_task_list",
          "method": "POST",
          "params": {
            "limit": "Search criteria",
            "billCategory": "Bill category code",
            "warehouseId": "Warehouse ID",
            "replenishmentCodes": ["Task numbers"]
          }
        }
      ]
    },
    {
      "name": "批量下发",
      "api": {
        "url": "/wws/front/replenish_task/batch_assign_task",
        "method": "POST",
        "params": {
          "limit": "Search criteria",
          "billCategory": "Bill category code",
          "warehouseId": "Warehouse ID",
          "replenishmentCodes": ["Task numbers"],
          "totalNumLimit": "Number limit (optional)",
          "totalTaskLimit": "Task limit (optional)"
        }
      },
      "conditions": [
        "Status must be [0] (待下发)",
        "Either subWarehouseId or areaId must be selected",
        "billCategory must not be '-1'",
        "orderType must be selected",
        "timeType must be 1 (创建时间)",
        "beginTime and endTime must be selected"
      ],
      "dependentAPIs": [
        {
          "name": "获取批量下发任务信息",
          "url": "/wws/front/replenish_task/load_pre_handle_task_list",
          "method": "POST",
          "params": {
            "limit": "Search criteria",
            "billCategory": "Bill category code",
            "warehouseId": "Warehouse ID",
            "replenishmentCodes": ["Task numbers"]
          }
        },
        {
          "name": "获取批量下发任务进度",
          "url": "/wws/front/replenish_task/load_batch_task_list",
          "method": "POST",
          "params": {
            "limit": "Search criteria",
            "billCategory": "Bill category code",
            "warehouseId": "Warehouse ID",
            "replenishmentCodes": ["Task numbers"]
          }
        }
      ]
    },
    {
      "name": "更换操作人",
      "api": {
        "url": "/wws/front/change_assignment",
        "method": "POST",
        "params": {
          "id": "Task ID",
          "underUser": "Operator username"
        }
      },
      "conditions": [
        "Task must have billCategory=4 (补货) or 6 (理货)",
        "Task must have underWay=0 (逐件下架) or 1 (整箱下架)",
        "Task must have status=2 (下架中)"
      ],
      "dependentAPIs": []
    }
  ],
  "environmentVariables": {
    "BASE_URI": "/wms/front",
    "WWS_URI": "/wws/front",
    "OSM_FRONT": "/osm/front",
    "WGS_FRONT": "/wgs/front"
  },
  "initialDataAPIs": [
    {
      "name": "获取数据字典",
      "url": "/basic/dictionary/dictSelect",
      "method": "POST",
      "params": {
        "catCode": [
          "REPLENISH_TASK_STATUS",
          "REPLENISH_TYPE",
          "BILL_CATEGORY",
          "REPLENISHMENT_TASK_TYPE",
          "UNDER_WAY",
          "REGION_ENUM",
          "NATIONAL_LINE_TYPE",
          "COUNTRY_TYPE",
          "BH_PRIORITY_LEVEL",
          "STORAGE_TAG",
          "REPLENISH_ISSUED_WAY",
          "PARENT_STORE_TYPE_ENUM",
          "OP_MODE_ENUM"
        ]
      }
    },
    {
      "name": "获取优先级字典",
      "url": "/common/common/priorityDict",
      "method": "POST"
    },
    {
      "name": "获取AMR库区参数配置",
      "url": "/common/common/getConfigByCode",
      "method": "POST",
      "params": {
        "param": "AMR_AREA_IDS"
      }
    },
    {
      "name": "查询子仓数据",
      "url": "/nav/getPermissionSubWarehouse",
      "method": "POST",
      "params": {
        "warehouseId": "Warehouse ID",
        "enabled": 1
      }
    },
    {
      "name": "查询园区列表",
      "url": "/lib/dealFunc/queryParkList",
      "method": "POST",
      "params": {
        "warehouseId": "Warehouse ID"
      }
    }
  ],
  "searchAPI": {
    "name": "查询补货任务列表",
    "url": "/wws/front/replenish_task/list",
    "method": "POST",
    "params": {
      "limit": "Search criteria",
      "billCategory": "Bill category code",
      "warehouseId": "Warehouse ID",
      "replenishmentCodes": ["Task numbers"],
      "pageNum": "Page number",
      "pageSize": "Page size",
      "queryResource": "Simple search mode (0 or 1)"
    }
  }
}
```

## 3
```json
{
  "pagePath": "/in-warehouse/replenish",
  "buttons": [
    {
      "buttonName": "下发任务",
      "apiInfo": {
        "url": "/wws/front/replenish_task/assignTask",
        "permissionConditions": "勾选的单据状态必须是0待下发，单据类型必须是4补货/6回货/9冻结移位",
        "limitConditions": "",
        "relatedApis": []
      }
    },
    {
      "buttonName": "任务指派",
      "apiInfo": {
        "url": "/wws/front/task_assignment",
        "permissionConditions": "勾选的单据状态必须是0待下发 1待下架，单据类型必须是4补货，下架方式不为自动化下架，库区=amr库区参数配置范围内的，指派置灰",
        "limitConditions": "需要同一子仓",
        "relatedApis": [
          {
            "url": "/osm/front/operator_group/query_group_by_subwarehouse"
          }
        ]
      }
    },
    {
      "buttonName": "作废",
      "apiInfo": {
        "url": "/wws/front/replenish_task/cancelTask",
        "permissionConditions": "勾选的单据状态必须是0待下发 1待下架",
        "limitConditions": "最多选择20条",
        "relatedApis": []
      }
    },
    {
      "buttonName": "导出",
      "apiInfo": {
        "url": "/wws/front/replenish_task/export_list",
        "permissionConditions": "页面加载完成后",
        "limitConditions": "不在简单搜索模式下",
        "relatedApis": []
      }
    },
    {
      "buttonName": "导入自动化移位任务",
      "apiInfo": {
        "url": "/wgs/front/file_import/record/wws/auto_shift_task_import",
        "permissionConditions": "",
        "limitConditions": "",
        "relatedApis": [
          {
            "url": "/wws/front/auto_shift_task/download_template"
          }
        ]
      }
    },
    {
      "buttonName": "批量作废",
      "apiInfo": {
        "url": "/wws/front/replenish_task/batch_cancel_task",
        "permissionConditions": "状态必须为待下发/待下架，下架子仓/库区必须有值，如果下架子仓选中则单选，单据类别必填，单据类型必填，创建时间必填",
        "limitConditions": "",
        "relatedApis": [
          {
            "url": "/wws/front/replenish_task/load_pre_handle_task_list"
          },
          {
            "url": "/wws/front/replenish_task/load_cancel_task_list"
          }
        ]
      }
    },
    {
      "buttonName": "批量下发",
      "apiInfo": {
        "url": "/wws/front/replenish_task/batch_assign_task",
        "permissionConditions": "状态必须为待下发，下架子仓/库区必须有值，单据类别必填，单据类型必填，创建时间必填",
        "limitConditions": "",
        "relatedApis": [
          {
            "url": "/wws/front/replenish_task/load_pre_handle_task_list"
          },
          {
            "url": "/wws/front/replenish_task/load_batch_task_list"
          }
        ]
      }
    },
    {
      "buttonName": "下载导入模版",
      "apiInfo": {
        "url": "/wws/front/auto_shift_task/download_template",
        "permissionConditions": "",
        "limitConditions": "",
        "relatedApis": []
      }
    }
  ]
}
```