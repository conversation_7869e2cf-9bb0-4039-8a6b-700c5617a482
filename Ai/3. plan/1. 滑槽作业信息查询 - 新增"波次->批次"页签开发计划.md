# 滑槽作业信息查询 - 新增"波次->批次"页签开发计划

## 功能概述
为滑槽作业信息查询页面新增"波次->批次"页签，实现基于波次到批次的滑槽作业信息查询功能。

## 技术实现方案

### 1. 页面结构改造
 - 将现有单页面结构改为Tabs结构
- **保留原有功能作为第一个页签（重要：尽量保持页面原有代码，减少改动）**
- 新增"波次->批次"页签

### 2. 目录结构
```
src/pages/business-management/chute-operate-info/
├── components/
│   ├── wave-batch/           # 新增：波次->批次页签组件
│   │   ├── index.tsx         # 主组件
│   │   ├── components/
│   │   │   ├── header.tsx    # 查询表单
│   │   │   ├── handle.tsx    # 操作按钮（导出）
│   │   │   └── list.tsx      # 数据列表
│   │   ├── hooks/
│   │   │   └── use-overall.ts # 业务逻辑
│   │   ├── server.ts         # API调用
│   │   └── type.ts           # 类型定义
│   └── original-tab/         # 原有功能组件（保持原有代码结构）
│       └── index.tsx         # 将原有页面内容封装为组件
├── index.tsx                 # 主入口（改造为Tabs结构）
└── plan.md                   # 本开发计划
```

**注意事项：**
- `original-tab` 目录用于封装原有页面功能，**尽量保持原有代码不变**
- 只需将原有的页面内容提取为一个组件，减少重构工作量
- 避免大规模重构原有代码结构

## 数据展示字段

### 列表字段定义
| 列表名称       | 数据来源                                | 数据说明 | 备注 | 数据示例                  |
| -------------- | --------------------------------------- | -------- | ---- | ------------------------- |
| 滑槽编号       | 初始化时写入                            | —        | —    | HC001                     |
| 绑定批次周转箱 | 滑槽与批次拣货箱建立绑定/解绑关系时写入 | —        | —    | YFSS20491021              |
| 绑定批次号     | 波次开始分拣/结束分拣时写入             | —        | —    | SS24021124431             |
| 绑定波次号     | 波次开始分拣/结束分拣时写入             | —        | —    | SS2402112443              |
| 设备编码       | 初始化时写入                            | —        | —    | LBYF001                   |
| 设备名称       | 初始化时写入                            | —        | —    | 芦苞一分自动化分拣设备001 |
| 子仓           | 初始化时写入                            | —        | —    | 芦苞东百1号仓             |
| 园区           | 初始化时写入                            | —        | —    | 芦苞东百                  |
| 仓库           | 初始化时写入                            | —        | —    | 佛山仓                    |

### 表格列配置
```typescript
const columns: TYPE.Table.ColumnItem<IListItem>[] = [
  { title: t('滑槽编号'), render: 'chuteCode', width: 120 },
  { title: t('绑定批次周转箱'), render: 'bindBatchContainerCode', width: 160 },
  { title: t('绑定批次号'), render: 'batchCode', width: 140 },
  { title: t('绑定波次号'), render: 'wellenCode', width: 140 },
  { title: t('设备编码'), render: 'equipmentCode', width: 120 },
  { title: t('设备名称'), render: 'equipmentName', width: 180 },
  { title: t('子仓'), render: 'subWarehouse', width: 120 },
  { title: t('园区'), render: 'park', width: 100 },
  { title: t('仓库'), render: 'warehouse', width: 100 },
];
```

## 查询条件配置

### 查询表单字段
| 搜索名称           | 类型             | 是否默认展示 | 数据来源（枚举值/接口）                 | 默认值 | 说明描述                                                                                                                                                                        |
| ------------------ | ---------------- | ------------ | --------------------------------------- | ------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **仓库**           | 输入下拉框，单选 | 是           | 子仓管理-仓库                           | 佛山仓 | 1. 该搜索项必填<br>2. 模糊搜索，输入支持中英文大小写<br>3. 存在级联：若园区和子仓已有数据，重新选择仓库则园区和子仓数据清空                                                     |
| **园区**           | 输入下拉框，单选 | 否           | 子仓管理-园区                           | 全部   | 1. 模糊搜索，输入支持中英文大小写<br>2. 存在级联：仅能选择已选仓库下的数据，若子仓已有数据，重新选择园区则子仓数据清空                                                          |
| **子仓**           | 输入下拉框，多选 | 否           | 子仓管理-子仓                           | 全部   | 1. 模糊搜索，输入支持中英文大小写<br>2. 存在级联：<br> - 若园区未选，则可搜索该仓库下所有子仓数据<br> - 若园区已选，则仅能选已选园区下的数据                                    |
| **设备名称**       | 输入下拉框，单选 | 否           | 分拣机管理-设备名称                     | 全部   | 1. 模糊搜索，输入支持中英文大小写<br>2. 存在级联：<br> - 若子仓未选，则设备名称可搜索所有子仓下设备<br> - 若子仓已选，则仅可选对应子仓设备<br> - 重新选择子仓时设备名称数据清空 |
| **滑槽编号**       | 输入下拉框，多选 | 否           | （波次->批次）滑槽作业信息查询-滑槽编号 | 全部   | 1. 模糊搜索，输入支持中英文大小写                                                                                                                                               |
| **绑定批次周转箱** | 输入文本框       | 否           | —                                       | —      | 1. 精确搜索，输入支持中英文大小写<br>2. 支持输入多个查询，上限50个                                                                                                              |
| **绑定批次号**     | 输入文本框       | 否           | —                                       | —      | 1. 精确搜索，输入支持中英文大小写<br>2. 支持输入多个查询，上限50个                                                                                                              |
| **绑定波次号**     | 输入文本框       | 否           | —                                       | —      | 1. 精确搜索，输入支持中英文大小写<br>2. 支持输入多个查询，上限50个                                                                                                              |

### 表单验证规则
- 仓库：必填字段
- 批次周转箱、批次号、波次号：支持多个输入，上限50个
- 级联关系：仓库->园区->子仓->设备名称

## API接口

### 查询接口
- https://soapi.sheincorp.cn/application/3894/routes/post_wacs_front_ac_chute_work_query_list/doc
- **接口路径**: `/wacs/front/ac_chute_work/query_list`
- **请求方法**: POST
- **接口说明**: 滑槽作业信息表列表查询

### 导出接口
- https://soapi.sheincorp.cn/application/3894/routes/post_wacs_front_ac_chute_work_export/doc
- **接口路径**: `/wacs/front/ac_chute_work/export`
- **请求方法**: POST
- **接口说明**: 滑槽作业信息表导出

### 请求参数类型
```typescript
export interface WaveBatchQueryFormReq {
  /** 绑定批次号 */
  batchCode?: string;
  /** 绑定批次号列表，支持多选 */
  batchCodes?: string[];
  /** 绑定批次周转箱 */
  bindBatchContainerCode?: string;
  /** 绑定批次周转箱列表，支持多选 */
  bindBatchContainerCodes?: string[];
  /** 滑槽编号 */
  chuteCode?: string;
  /** 滑槽编号列表 */
  chuteCodes?: string[];
  /** 设备编码 */
  equipmentCode?: string;
  /** 当前页码 */
  pageNum?: number;
  /** 页宽度 */
  pageSize?: number;
  /** 园区 */
  parkId?: string;
  /** 业务场景 */
  scene?: string;
  /** 子仓id */
  subWarehouseId?: number;
  /** 仓库id */
  warehouseId?: number;
  /** 绑定波次号 */
  wellenCode?: string;
  /** 绑定波次号列表，支持多选 */
  wellenCodes?: string[];
}
```

### 响应数据类型
```typescript
export interface WaveBatchListItem {
  /** 绑定批次号 */
  batchCode?: string;
  /** 绑定批次周转箱 */
  bindBatchContainerCode?: string;
  /** 滑槽编号 */
  chuteCode?: string;
  /** 创建时间 */
  createTime?: string;
  /** 设备编码 */
  equipmentCode?: string;
  /** 设备名称 */
  equipmentName?: string;
  /** id */
  id?: number;
  /** 更新时间 */
  lastUpdateTime?: string;
  /** 园区 */
  park?: string;
  /** 园区id */
  parkId?: string;
  /** 业务场景 */
  scene?: string;
  /** 子仓 */
  subWarehouse?: string;
  /** 子仓id */
  subWarehouseId?: number;
  /** 仓库 */
  warehouse?: string;
  /** 仓库id */
  warehouseId?: number;
  /** 绑定波次号 */
  wellenCode?: string;
}
```

## 实现步骤

### 第一步：页面结构改造（最小化改动原则）
1. 修改主入口文件 `index.tsx`，引入Tabs组件
2. **将现有页面内容封装为 `components/original-tab/index.tsx` 组件（保持原有代码结构）**
3. 创建新的页签结构，第一个页签使用原有功能组件
4. **重要：尽量保持原有代码逻辑和结构不变，只做最小必要的封装**

### 第二步：新增波次->批次组件
1. 参考 `sys-management/sorting-management/chute-bind-container-query/components/wave-batch/` 组件结构
2. 创建查询表单组件 `components/wave-batch/components/header.tsx`
3. 创建数据列表组件 `components/wave-batch/components/list.tsx`
4. 创建操作按钮组件 `components/wave-batch/components/handle.tsx`
5. 创建主组件 `components/wave-batch/index.tsx`

### 第三步：API服务层
1. 新增API调用函数 `components/wave-batch/server.ts`
2. 定义请求/响应类型 `components/wave-batch/type.ts`
3. 处理数据转换

### 第四步：业务逻辑
1. 实现查询表单管理 `components/wave-batch/hooks/use-overall.ts`
2. 实现分页逻辑
3. 实现导出功能
4. 添加表单验证
5. 实现级联选择逻辑

### 第五步：样式和优化
1. 调整样式适配
2. 添加loading状态
3. 错误处理
4. 性能优化

## 参考文件
- 参考页面：`src/pages/sys-management/sorting-management/chute-bind-container-query/`
- 目标页面：`src/pages/business-management/chute-operate-info/`
- 组件结构参考：`chute-bind-container-query/components/wave-batch/`

## 多语言支持
- 使用 `t()` 函数进行多语言支持
- 所有用户界面文本都需要通过 `t()` 函数包装
- 保持与现有代码风格一致

## 开发注意事项

### TypeScript 类型问题
- **TS报错可以暂时忽略**，重点确保依赖引用正确
- 优先保证功能正常运行，类型问题后续优化
- 确保所有import路径和组件引用正确

### 代码改动原则
- **最小化改动**：尽量保持原有页面代码结构不变
- **渐进式重构**：只对必要部分进行调整
- **功能优先**：确保原有功能正常运行后再添加新功能

## 预估工作量
- **开发时间**: 2-3个工作日
- **测试时间**: 1个工作日
- **主要工作**: 组件封装、新页签开发、API集成、功能测试
- **重点**: 保持原有功能稳定性，减少回归风险