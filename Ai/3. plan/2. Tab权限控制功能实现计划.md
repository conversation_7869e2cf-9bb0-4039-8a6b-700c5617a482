# Tab权限控制功能实现计划

## 功能概述
为以下两个页面添加tab权限控制功能，参考 `src/pages/business-management/split-container-query/index.tsx` 的实现方式：

1. **滑槽容器绑定查询** (`/sys-management/sorting-management/chute-bind-container-query`)
2. **滑槽作业信息查询** (`/business-management/chute-operate-info`)

## 权限控制要求
- 每个tab做权限入口限制
- 默认选中具有权限的第一个tab
- 如果用户没有任何tab权限，显示错误提示

## 实现步骤

### 第一步：修改滑槽容器绑定查询页面

**文件路径**: `/sys-management/sorting-management/chute-bind-container-query/index.tsx`

**权限URL配置**:
- 包裹分拣: `/sys-management/sorting-management/chute-bind-container-query tab:package-sorting`
- 波次组->波次: `/sys-management/sorting-management/chute-bind-container-query tab:wave-group`
- 波次->批次: `/sys-management/sorting-management/chute-bind-container-query tab:wave-batch`

**实现内容**:
1. 导入必要的依赖：`useState`, `useMount`, `checkUrlPermissionAPI`, `Modal`
2. 添加权限状态管理：`packageSortingPermission`, `waveGroupPermission`, `waveBatchPermission`
3. 添加当前tab状态管理：`tabKey`
4. 在 `useMount` 中并行检查三个tab的权限
5. 根据权限结果设置默认选中的tab
6. 如果没有任何权限，显示错误弹窗
7. 根据权限状态条件渲染 `Tabs.Panel`
8. 添加 `i18n` 导出

### 第二步：修改滑槽作业信息查询页面

**文件路径**: `/business-management/chute-operate-info/index.tsx`

**权限URL配置**:
- 波次组->波次: `/business-management/chute-operate-info tab:wave-group`
- 波次->批次: `/business-management/chute-operate-info tab:wave-batch`

**实现内容**:
1. 导入必要的依赖：`useState`, `useMount`, `checkUrlPermissionAPI`, `Modal`
2. 添加权限状态管理：`waveGroupPermission`, `waveBatchPermission`
3. 修改当前tab状态管理逻辑
4. 在 `useMount` 中并行检查两个tab的权限
5. 根据权限结果设置默认选中的tab
6. 如果没有任何权限，显示错误弹窗
7. 根据权限状态条件渲染 `Tabs.Panel`

## 技术实现细节

### 权限检查逻辑
```javascript
Promise.all([
  checkUrlPermissionAPI({
    url: 'permission-url-1',
    methodType: '',
  }),
  checkUrlPermissionAPI({
    url: 'permission-url-2', 
    methodType: '',
  }),
  // ...
]).then((res) => {
  const permission1 = res[0]?.code !== '400109';
  const permission2 = res[1]?.code !== '400109';
  // 设置权限状态
  // 设置默认选中tab
  // 处理无权限情况
});
```

### 默认Tab选择逻辑
- 按照tab顺序（从左到右）检查权限
- 选中第一个有权限的tab作为默认tab
- 如果所有tab都没有权限，显示错误提示

### 条件渲染
```javascript
{
  permission && (
    <Tabs.Panel tab={t('Tab名称')}>
      <Component />
    </Tabs.Panel>
  )
}
```

## 测试验证

1. **权限测试**：
   - 测试用户有全部tab权限的情况
   - 测试用户只有部分tab权限的情况
   - 测试用户没有任何tab权限的情况

2. **默认选择测试**：
   - 验证默认选中第一个有权限的tab
   - 验证tab切换功能正常

3. **错误处理测试**：
   - 验证无权限时的错误提示显示

## 注意事项

1. 保持与参考页面一致的错误处理方式
2. 确保权限URL格式正确
3. 保持原有的样式和布局不变
4. 确保国际化功能正常工作
5. 权限检查失败时的降级处理

## 文件修改清单

- [ ] `/sys-management/sorting-management/chute-bind-container-query/index.tsx`
- [ ] `/business-management/chute-operate-info/index.tsx`

## 完成标准

- [ ] 两个页面都实现了tab权限控制
- [ ] 默认选中逻辑正确
- [ ] 错误处理正常
- [ ] 功能测试通过
- [ ] 代码review通过