# WMS项目开发规范和架构说明

## 项目概述
这是一个基于React + Redux + Redux-Saga的仓储管理系统(WMS)，使用了自定义的`rrc-loader-helper`库来增强Redux-Saga功能。

## 核心架构模式

### 1. Redux-Saga增强语法

#### yield '' 语法
```javascript
const { pageInfo, limit } = yield '';
```
**说明**: `yield ''` 是项目中获取当前组件state的特殊语法，等价于获取当前reducer的完整state对象。这是`rrc-loader-helper`库提供的增强功能。

#### yield 'nav' 语法  
```javascript
const { warehouseId } = yield 'nav';
```
**说明**: `yield 'nav'` 用于获取nav组件的state，主要用于获取当前选中的仓库信息等全局状态。

#### yield this.methodName() 语法
```javascript
yield this.changeData({ loading: false });
yield this.search();
```
**说明**: 在generator函数中调用同一个reducer中的其他方法，支持链式调用。

### 2. 状态管理模式

#### Reducer结构标准
```javascript
export default {
  state: defaultState,
  // 同步方法：直接修改state
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 异步方法：使用generator函数
  * search() {
    const { pageInfo, limit } = yield '';
    // 异步逻辑
  }
}
```

#### 默认状态结构
```javascript
const defaultState = {
  formRef: {}, // 表单引用
  loading: 1, // 0:加载中 1:加载成功 2:加载失败
  limit: defaultLimit, // 搜索条件
  pageInfo: { // 分页信息
    pageNum: 1,
    pageSize: getSize(),
    count: 0,
    pageSizeList: [20, 50, 100]
  },
  list: [], // 列表数据
  selectedRows: [], // 选中行
  // 模态框相关
  modalVisible: false,
  modalData: {}
};
```

### 3. 分页和搜索模式

#### 标准搜索方法
```javascript
* search() {
  const { pageInfo, limit } = yield '';
  const { warehouseId } = yield 'nav'; // 获取仓库ID
  const params = {
    ...limit,
    pageNum: pageInfo.pageNum,
    pageSize: pageInfo.pageSize,
    warehouseId
  };
  markStatus('loading'); // 设置loading状态
  const { code, info, msg } = yield getListAPI(clearEmpty(paramTrim(params)));
  if (code === '0') {
    yield this.changeData({
      list: info.data,
      pageInfo: { ...pageInfo, count: info.meta.count },
      selectedRows: []
    });
  } else {
    Modal.error({ title: msg });
  }
}
```

#### 分页变更处理
```javascript
* handlePaginationChange(data = {}) {
  const { pageInfo } = yield '';
  yield this.changeData({
    pageInfo: { ...pageInfo, ...data }
  });
  yield this.search();
}
```

### 4. 编辑功能模式

#### 新增/编辑模态框
- 复用同一个模态框组件
- 使用`isEditMode`和`editingId`区分新增/编辑状态
- 编辑时使用`getDetailAPI`获取详情数据
- 提交时使用`registerEditAPI`处理新增和编辑

#### 标准编辑流程
```javascript
// 打开编辑模态框
* openEditModal(action) {
  const { id } = action;
  if (id) {
    // 编辑模式：获取详情
    const { code, info } = yield getDetailAPI({ id });
    if (code === '0') {
      yield this.changeData({
        modalVisible: true,
        isEditMode: true,
        editingId: id,
        modalData: info
      });
    }
  } else {
    // 新增模式
    yield this.changeData({
      modalVisible: true,
      isEditMode: false,
      editingId: null,
      modalData: { ...defaultModalData }
    });
  }
}

// 提交数据
* submitData() {
  const { modalData, isEditMode, editingId } = yield '';
  const params = isEditMode ? { ...modalData, id: editingId } : modalData;
  const { code, msg } = yield registerEditAPI(params);
  if (code === '0') {
    yield this.changeData({ modalVisible: false });
    yield this.handlePaginationChange({ pageNum: 1 });
  } else {
    Modal.error({ title: msg });
  }
}
```

### 5. 过滤条件模式

参考`src/component/domestic-salary/basic-info/personal-info`的header组件结构：
- 文本输入框使用标准Input组件
- 多选下拉使用Select组件的multiple模式
- 时间选择使用DatePicker组件
- 搜索条件存储在`limit`对象中

### 6. 工具函数和中间件

#### markStatus函数
```javascript
markStatus('loading'); // 设置全局loading状态
```

#### 数据处理函数
```javascript
clearEmpty(paramTrim(params), [0, '0', false]); // 清理空值和trim字符串
```

#### 分页大小获取
```javascript
getSize(); // 获取全局默认分页大小
```

### 7. API调用模式

#### 标准API响应结构
```javascript
{
  code: '0', // 成功标识
  info: {
    data: [], // 列表数据
    meta: { count: 100 } // 分页信息
  },
  msg: '错误信息' // 错误时的提示
}
```

#### 错误处理
```javascript
if (code === '0') {
  // 成功处理
} else {
  Modal.error({ title: msg });
}
```

### 8. 文件组织结构

```
src/component/模块名/
├── index.jsx          # 主组件
├── reducers.js        # 状态管理
├── server.js          # API接口
└── style.less         # 样式文件
```

### 9. 命名约定

- 组件文件：使用kebab-case命名
- 方法名：使用camelCase命名
- 常量：使用UPPER_SNAKE_CASE命名
- 状态字段：使用camelCase命名

### 10. 开发注意事项

1. **状态获取**: 在generator函数中使用`yield ''`获取当前state
2. **仓库信息**: 需要仓库信息时使用`yield 'nav'`
3. **Loading管理**: 使用`markStatus('loading')`统一管理loading状态
4. **错误处理**: 统一使用`Modal.error({ title: msg })`显示错误
5. **分页重置**: 操作成功后通常重置到第一页
6. **表单校验**: 使用formRef进行表单校验
7. **数据清理**: API调用前使用`clearEmpty`和`paramTrim`清理数据

这些规范确保了项目代码的一致性和可维护性，特别是`yield ''`这种特殊语法的正确使用。
