# 页面URL与页面名映射处理脚本

## 功能说明

这个脚本用于处理Excel表格中的页面数据，通过JSON权限节点数据为每个页面别名添加对应的页面名称。

### 主要功能
1. 读取Excel表格数据（包含页面别名、浏览次数等信息）
2. 读取JSON权限节点数据（树形结构）
3. 构建页面别名到页面名的映射关系
4. 在Excel数据的页面别名列后新增页面名列
5. 输出处理后的Excel文件

## 文件说明

- `page_mapping_processor.py` - 主处理脚本
- `permission_nodes.json` - 权限节点JSON数据文件（示例）
- `create_sample_excel.py` - 创建示例Excel文件的脚本
- `input_data.xlsx` - 输入Excel文件（需要您提供或使用示例）
- `output_data.xlsx` - 输出Excel文件（脚本生成）

## 使用方法

### 1. 安装依赖

首先确保安装了必要的Python库：

```bash
pip install pandas openpyxl
```

### 2. 准备数据文件

#### 准备Excel文件
将您的Excel文件命名为 `input_data.xlsx` 并放在脚本同一目录下。

Excel文件格式应该包含以下列：
- 序号
- 页面别名（这是关键列，用于匹配）
- 浏览次数(PV)
- PV占比
- 用户数(UV)
- UV占比

示例数据：
```
序号  页面别名                                    浏览次数(PV)  PV占比  用户数(UV)  UV占比
1     inbound/reject-check-manage/reject-check-scan  37703      0.089   323       0.199
2     index                                          31856      0.075   1549      0.954
3     qms/receipt-management/receipt                 30925      0.073   356       0.219
4     basic-functions/barcode-print                  30554      0.072   747       0.460
```

#### 准备JSON文件
将权限节点JSON数据保存为 `permission_nodes.json` 文件。

JSON文件应该是一个数组，包含树形结构的权限节点，每个节点包含：
- `title`: 页面标题
- `rule`: 页面规则（URL路径，会去除开头的"/"）
- `typeName`: 节点类型（只处理"菜单"类型）
- `children`: 子节点数组

### 3. 运行脚本

#### 方法一：直接运行主脚本
```bash
python page_mapping_processor.py
```

#### 方法二：先创建示例文件（如果需要测试）
```bash
# 创建示例Excel文件
python create_sample_excel.py

# 然后运行主脚本
python page_mapping_processor.py
```

### 4. 查看结果

脚本运行成功后，会生成 `output_data.xlsx` 文件，其中包含：
- 原有的所有列
- 在页面别名列后新增的"页面名"列

示例输出：
```
序号  页面别名                      页面名                    浏览次数(PV)  PV占比  用户数(UV)  UV占比
1     inbound/reject-check-manage/reject-check-scan  [未匹配]              37703      0.089   323       0.199
2     index                         [未匹配]              31856      0.075   1549      0.954
3     qms/receipt-management/receipt  入库管理/收货管理/收货单管理  30925      0.073   356       0.219
4     basic-functions/barcode-print  [未匹配]              30554      0.072   747       0.460
```

## 映射规则说明

1. **只处理菜单节点**：脚本只会处理JSON中 `typeName` 为 "菜单" 的节点
2. **去除URL前缀**：会自动去除 `rule` 字段开头的 "/" 符号
3. **构建层级路径**：页面名由各级菜单的 `title` 用 "/" 连接而成
4. **精确匹配**：页面别名必须与处理后的 `rule` 完全匹配

### 映射示例

JSON数据：
```json
{
  "title": "入库管理",
  "rule": "/qms",
  "typeName": "菜单",
  "children": [
    {
      "title": "收货管理", 
      "rule": "/qms/receipt-management",
      "typeName": "菜单",
      "children": [
        {
          "title": "收货单管理",
          "rule": "/qms/receipt-management/receipt",
          "typeName": "菜单"
        }
      ]
    }
  ]
}
```

生成的映射：
- `qms` → `入库管理`
- `qms/receipt-management` → `入库管理/收货管理`
- `qms/receipt-management/receipt` → `入库管理/收货管理/收货单管理`

## 注意事项

1. **文件编码**：确保Excel和JSON文件使用UTF-8编码
2. **文件路径**：所有文件应放在脚本同一目录下
3. **列名匹配**：脚本假设页面别名在Excel的第二列
4. **数据完整性**：确保JSON数据结构完整，包含必要的字段
5. **权限要求**：确保脚本有读写文件的权限

## 故障排除

### 常见问题

1. **"Excel文件不存在"**
   - 检查文件名是否为 `input_data.xlsx`
   - 检查文件是否在正确的目录下

2. **"JSON文件不存在"**
   - 检查文件名是否为 `permission_nodes.json`
   - 检查JSON格式是否正确

3. **"未找到匹配"**
   - 检查页面别名是否与JSON中的rule字段匹配
   - 确认JSON节点的typeName是否为"菜单"

4. **"加载文件失败"**
   - 检查文件是否被其他程序占用
   - 检查文件权限
   - 检查文件格式是否正确

### 调试信息

脚本运行时会输出详细的调试信息，包括：
- 文件加载状态
- 映射关系数量
- 匹配成功/失败的记录
- 处理进度

## 自定义配置

如果需要修改文件名或路径，可以编辑 `page_mapping_processor.py` 中的 `main()` 函数：

```python
def main():
    processor = PageMappingProcessor()
    
    # 修改这些文件路径
    excel_file = "your_excel_file.xlsx"
    json_file = "your_json_file.json" 
    output_file = "your_output_file.xlsx"
    
    # ... 其余代码
```
