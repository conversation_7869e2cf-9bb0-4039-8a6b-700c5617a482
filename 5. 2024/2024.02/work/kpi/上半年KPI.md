# 上半年KPI

## 对比
先拿文贵跟文辉的，确定下大体的方向，再细化具体的指标

### 文贵的2023年下半年KPI

小组开发效率
1、仓储需求平均研测总时效不超过5天，业务需求平均研测研发总时效不超过7天
2、业务需求超过15天完成的不超过3%
3、业务需求时效小于7天的占比从61%提升到80%
4、业务需求剩余工作量小于2周（研发状态的需求数/近4周周平均上线需求数）
5、不出现因为前端导致的项目延期

小组开发质量
1、前端入库P4生产故障不超过3个，mttr：2h
2、仓储部测试阶段开发单位人日缺陷低于14%

生产应急
1、仓储前端生产故障演练平均达到1次/月
2、8月底前仓储前端生产预案兜底开关规范化
3、仓储前端侧生产故障P5故障比例≥40%(总共8个)
4、前端入库侧规范流程自动化管理工具2个以上

### 文辉的2023年下半年KPI

1. 保障系统的可用性
  1. 前端侧系统全年可用性达到：
    wms：99.99% 52.56min
    mot：99.99%
    wsk：99.99%
    ips：99.9%
2. 推进部门和组内的预案和演练常态化
  1. 仓储研发部生产故障演练平均达到9次/月
  2. 黑五前推进仓储研发部自动化、一键化预案工具产出2次以上。
  3. 8月底前完成生产故障预案的覆盖率，检查故障复盘流程，能否产出预案流程
  4. 下半年预案、演练规范流程各迭代≥3次。
3. 提升前端的开发效率
  1. 仓储需求平均研测总时效不超过5天，业务需求平均研测研发总时效不超过7天
  2. 业务需求超过15天完成的不超过3%
  3. 业务需求时效小于7天的占比从61%提升到80%
  4. 业务需求剩余工作量小于2周（研发状态的需求数/近4周周平均上线需求数）
  5. 不出现因为前端导致的项目延期
4. 保障前端侧系统的质量
  1. P4生产故障不超过8个，mttr：2h
  2. 涉及高危功能清单的功能能在四周内准出
  3. 仓储部测试阶段开发单位人日缺陷低于14%
  4. 仓储前端侧生产故障P5故障比例≥40%
5. 提高前端团队的安全意识
  1. 每月进行安全考试，通过率>100%
  2. 强化团队安全意识，每月进行一次安全宣贯
  3. 安全漏洞时效保持 危急&高危：1d  中危：2d  低危 2d
  4. 完成奇安信到xcheck的替换，并保证不存在安全漏洞
6. 提升前端侧团队内部管理
  1. 8月底综合前端侧流程规范与仓储侧解耦。
  2. 9月底综合前端侧开发架构统一。
  3. 前端侧各小组负责人B角培养。
  4. 赋能前端侧预案、质量、架构演进专项负责人员3名以上。
  5. 前端侧规范流程自动化管理工具3个以上。



## 已经做了的
- BBL 架构建设
  - 规范文档输出
  - 辅助工具
- 前端代码规范化
  - husky 配置整理
- 企微机器人jira任务推送
- gitlab 代码检测
- LCP 优化建设


主动推动拉美履约BBL管理体系的优化与完善，确保自闭环拉美履约需求。包括：输入规范文档，输出辅助工具

建立前端代码规范化系统，包括：husky 配置整理，gitlab 代码检测




