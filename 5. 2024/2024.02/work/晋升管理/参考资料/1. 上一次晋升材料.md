# 1. 关键产出
1. 近2年来担任wms库内业务前端负责人，日常参与库内需求的评审、开发、测试、上线等工作，同时负责库内需求的管理工作，包括需求分配、技术指导、团队建设等工作；精通wms库内业务，输出了库内核心业务：盘点、理货、补货的前端巡检文档；

2. 曾担任「WMS打印优化项目」的项目经理，主导推进了项目的开发、测试、上线等工作，为出库核心打包业务提供本地打印功能，提升了核心业务的稳定性；

3. 自行产出并维护需求发版脚本dutyRobot，提升了团队发版效率；

4. 产出并维护3个业务组件：StretchMenu、WmsSearchArea、createStorage

# 2. 专业能力
1. 为支持黑五活动，提升出库打包业务的稳定性，提出前端项目「wms 打印优化」，提供了wms打印的本地降级方案。通过zpl打印机驱动，实现了本地打印功能，避免网络服务不稳定导致的打印失败问题。在2022年黑五活动中提供了技术支持，保证了打包核心业务的稳定性。

2. 针对公共JSON配置的序列化出错导致系统白屏问题，提供了前端本地存储管理方案并输出相应的toolKit: createStorage，在仓储系统中得到了广泛的应用，例如：wms/mot/wsk/wms-outbound/wms-inbound等。

3. 主动关注前端发版的流程效率问题，提出了前端发版维护的自动化方案，自行开发了dytuRobot 脚本工具。通过gitlab和jira的集成，使用Node.js 实现了前端发版维护的自动化，提升了前端发版的效率，降低了发版的风险。 

## 3. 三能力

### 3.1 能辨
在2022年pdf 服务网络问题出现后，积极响应，提出了「wms打印优化」项目，保障了出库核心打包业务，并在2022年黑五前上线

### 3.2 能为
主动关注到公司在推进海外仓库导致wms前端项目可能导致的发版效率降低和可能导致的发版出错风险，主动开发dutyRobot 脚本工具，从提出到第一版落地仅需一周，并持续进行版本迭代，满足需求

#### 3.3 能燃
在2023.07-2023.08 期间，支援aidc 部门完成ccc项目的v3.0.0 版本重构。在技术栈与wms部门差异较大的情况下，快速上手系统的开发工作，在1个月内按时完成了3个需求，为ccc项目的上线提供了技术支持，并获得了认同