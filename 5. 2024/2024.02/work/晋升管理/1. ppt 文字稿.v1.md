# 新的晋升材料 文字稿



## 1. 个人履历概述
目前担任拉美履约仓储前端主负责人
曾担任国内仓储库内业务线前端主负责人
5年以上前端开发经验
本科学位，17年毕业于东莞理工学院

## 2. 业绩贡献
过去1年的业绩贡献：
1. 主导建立拉美履约BBL管理体系，实现了拉美履约需求自闭环；
2. 构建拉美前端代码规范化生态，实现了代码规范与自动化工具链深度融合，驱动开发效能提升；
3. 打造拉美履约研发团队的Jira自动化推送流程，提升了团队效能；
4. 策划并自主研发了自动化发版工具 dutyRobot，提升了前端团队发布效率，有效降低人为误操作风险；
5. 主导wsk-na和wsk-la应用的系统独立部署，为北美履约系统和拉美履约系统的独立部署提供了基础；
6. 参与架构建设工作，包括mot、wsk 应用的paas ci配置标准化和网关发布等；
7. 开发并持续维护业务组件：StretchMenu 拉伸菜单、WmsSearchArea、wms-toolkit: createStorage 等。

## 2. 专业技能和问题解决

### 2-1 拉美履约BBL管理体系

1. 定义问题
如何提升需求中翻译词条的时效和准确性。

2. 拆分问题
需求词条的收集和整理是否高效？
翻译质量的保障机制有哪些？
技术工具如何支撑词条的自动化收集？
BBL应用中心是否能支持工作流的闭环？

3. 确保完整性
上述拆分确保覆盖了翻译词条管理的所有关键环节，从词条收集到发布的全流程。

4. 分析和解决
4.1. 规划化工作流程
与产品、后端、测试多方共同制定了一套标准化的工作流程，确保了多语言翻译管理的高效性和准确性。
4.2. 跨部门沟通与合作
积极与架构组BBL团队进行沟通协调，就功能迭代方向和优先级达成一致。
4.3. 技术创新与工具开发
主导开发了一系列的开发工具，包括一个Chrome插件和一个VSCode编辑器插件。这些工具能够自动导出上线需求中的未翻译词条以及系统中的全量词条，大幅减少手工操作的时间和出错率，提升了团队时效。

5. 整合结果
整合技术创新和流程优化的方案，形成一套完整的拉美履约BBL管理体系。

### 2-2 dutyRobot 自动化发版工具

1. 定义问题
如何通过自动化工具提升前端发版的效率和准确性，同时降低发版过程中的人为错误和风险。

2. 拆分问题
发版流程的痛点分析：确定当前发版流程中存在哪些效率低下和容易出错的环节，分析造成这些问题的根本原因。
自动化工具的功能需求：确定自动化工具需要实现哪些功能来解决上述痛点，评估这些功能的可行性和优先级。
技术实现方案：设计dutyRobot的架构，包括与现有系统的集成方式。探索可用的技术和框架来实现所需功能。
风险与挑战：识别在开发和实施dutyRobot过程中可能遇到的风险和挑战，并规划应对策略。

3. 确保完整性
确保上述拆解的子问题覆盖了从发版流程的痛点分析到dutyRobot工具的设计、实现及风险管理的所有关键方面。

4. 分析和解决
4.1 发版流程的痛点分析：通过访谈和数据分析，识别发版流程中的低效环节，如分支的创建、合并、需求信息的统计、漏合分支的检查等。
4.2 自动化工具的功能需求：确定dutyRobot需要包含的关键功能，如批量创建分支、批量创建mr，输出发版信息表格等，以及这些功能的实现优先级。
4.3 技术实现方案：选择合适的技术栈（如Node.js、GitLab API、Jira API 等），设计dutyRobot的架构，确保其易于集成和扩展。
4.4 风险与挑战：识别可能的风险，如安全性问题、开发管理问题，并制定相应的预防和应对措施。

5. 项目敏捷开发
5.1 增量开发和迭代改进
小步快跑，分阶段交付产品功能。应用于dutyRobot，比如引入inquirer增强用户引导、增加根据版本号生成发版表、引入dotenv提升系统稳定性等，然后快速部署这些改动以获取实际用户的反馈。

5.2 用户反馈和持续改进
定期收集和分析来自开发团队的反馈，用以指导后续迭代的重点，帮助团队优先处理最影响用户体验和工作效率的问题。同时，也可以基于用户的实际使用情况来评估新功能的有效性和实用性。

5.3 灵活应对变化
在dutyRobot的持续优化过程中，为了配合仓储全球化战略，及时推出地区配置化功能，支撑海外前端团队的发版需求。

6. 整合结果
将各个子问题的解决方案整合成一个全面的实施计划，确保dutyRobot能够有效提升发版效率和准确性，同时降低运营风险。

同时，结合“敏捷开发”方法论，推动了dutyRobot的持续优化。不仅能够更加贴近用户需求，还能够提升团队的工作效率和适应性，灵活调整开发方向响应公司变化。

## 3. 三能案例阐述

参照STAR模式，结合三能2.0举证工作中最能体现自己“三能”能力的案例，案例要素包括：
1、场景（Situation/Task-任务场景简述）；
2、行为（Action-采取的行动和举措）；
3、结果（Result-输出的成果产出）。

### 3.1. 能辨（分析/学习）
场景
在构建拉美前端代码规范化生态的过程中，面对的挑战: 
1. 如何在一仓多包结构的项目中（如wms-la和mot-la）实现统一配置，确保代码规范化的工具链（commitlint/lint-staged/husky/ESLint/Prettier等）能够高效且无缝地集成; 
2. 如何引入代码自动化检查机制（如使用gitlab API、企业微信机器人推送、babel AST等）,减少了codereview中的人力投入。
行为
1. 分析现状：通过代码审查和调研，分析存在的主要问题和不一致的代码实践。
2. 工具链的灵活应用：通过定制化的脚本和配置，确保了不同包之间可以共享大部分配置，同时也保留了根据具体包需求进行调整的灵活性。
3. 代码检查的高效性: 通过集成gitlab API、企业微信机器人推送和babel AST的自定义代码检查机制，能够更深层次的业务逻辑检查。通过企微推送，及时有效的保证代码质量。
结果
成功实现了代码规范与自动化工具链的深度融合，显著提升了开发效率和代码质量，减少了codereview中的人力投入。

### 3.2. 能为（规划/执行）
jira自动化推送流程

场景
在优化拉美履约研发团队需求时效的过程中，我们面临的主要挑战是如何通过自动化提高项目管理的效率和团队的响应速度。为此，我们决定开发一个结合Jira API和企微机器人推送的自动化推送机制。

行为
简化规划：通过分析团队需求，我们规划了一个简单而有效的解决方案：通过集成Jira API来自动捕捉需求的状态，并通过企微机器人实时推送给团队成员。

快速执行：快速开发来实现这一流程，通过设置定时任务触发脚本，随即通过企微API推送相关信息给指定的群组。

结果
这个自动化推送机制的实施，显著提升了团队的工作效率和项目管理的时效性：

提高了任务处理速度：团队成员能够实时接收到任务的最新动态，快速响应和调整自己的工作计划。
减少了沟通成本：自动化的推送减少了团队成员查看Jira的次数，降低了项目管理的沟通成本。

通过这个项目，成功提升了团队的时效性和项目管理效率，为拉美履约研发团队的快速发展提供了有力支持。

### 3.3. 能燃（沟通力/协助力）



## 4. 晋升后岗位规划

1. 系统性能优化
推出拉美前端系統LCP优化体系，前端系统整体指标优化20%以上。

2. 稳定性建设
建立和完善拉美前端系统的稳定性建设体系，包括预案演练、告警梳理、巡检机制等。

3. 团队建设
新人带教流程：制定一套前端侧的新人带教流程，包括但不限于技术栈培训、项目实战演练、文化融入等，确保新人能够快速融入团队。
培训考核体系：建立前端侧的新人培训考核体系，通过阶段性的考核和评估，确保新人在技术能力、项目执行、团队协作等方面达到团队要求。

4. 技术探索
赋能工具开发：ai与前端工作流的结合，探索ai对前端研发效能的提升。











