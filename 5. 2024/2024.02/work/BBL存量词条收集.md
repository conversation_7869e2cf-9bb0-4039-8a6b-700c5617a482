# BBL 存量词条收集

目前已经获取到对应的权限节点了

接下来怎么在遍历中避开不在其中的节点呢？

拿到文件的对应路由，然后在遍历中进行判断，如果不在其中，就跳过

有点难啊，因为这个有多层级的

突然想到，页面路径有个特点，就是路径下会有个me.json文件，可以通过这个来判断

遍历所有的me.json，列出它们的路径，再跟权限节点进行对比，如果不在其中，就console.log出来


/promise/air-freight-schedule

/Users/<USER>/Documents/work/LA/wms-la/packages/base/src/component/promise/air-freight-schedule/jsx/header.jsx
/Users/<USER>/Documents/work/LA/wms-la/packages/base/src/component/promise/air-freight-schedule/jsx/list.jsx
/Users/<USER>/Documents/work/LA/wms-la/packages/base/src/component/promise/air-freight-schedule/me.json
/Users/<USER>/Documents/work/LA/wms-la/packages/base/src/component/promise/air-freight-schedule/reducers.js
/Users/<USER>/Documents/work/LA/wms-la/packages/base/src/component/promise/air-freight-schedule/server.js
/Users/<USER>/Documents/work/LA/wms-la/packages/base/src/component/promise/air-freight-schedule/style.less
/Users/<USER>/Documents/work/LA/wms-la/packages/base/src/component/promise/air-freight-schedule/view.jsx

风险点：
应该找出tree 中‘有节点’的节点，这样删起来更加针对性，现在明显删多了
