# 如何从0到1搭建一个丐版ai agent 
soapi 尝试

解决的痛点：前端页面的server文件基本上都是一个基于soapi上的文档写的，接口命名非常规范。可以考虑自动化生成server文件。

## 原则
一定要保持“糙”，不要过度设计，因为ai模型的迭代速度非常快，任何的调优都可能会被后续的模型迭代所抹平。

## 第零步：确认

先确认一下，ai能不能接这个活，如果不能，那就不要浪费时间了。

## 第一步：找人
“我跪下了哥，求你帮我办个事儿”

找chatgpt的应用负责人开放gpt api 的私人密钥权限，然后在本地测试一下，看看能不能跑通。

因为gpt api 其实是有私人密钥权限的，但还没有贯宣，所以需要找人开放权限。

## 第二步：准备action

举个例子，比如说，我想要一个soapi agent，批量输入一些接口地址，然后生成对应的server文件。

## 第三步：调优

既要保持“糙”，又要保持可调优。最简单的做法的留有日志

## 第四步：持续迭代

更多的点子，更多的action

对接soapi获取接口文档，对接axure 获取prd，对接gitlab获取代码...

确认需求改动点，
