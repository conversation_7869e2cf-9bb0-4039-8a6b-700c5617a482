import fetch from 'node-fetch';
import jsdom from 'jsdom';
import { writeFile } from 'node:fs/promises';


const { JSDOM } = jsdom;

// jira的cookie
const wikiToken = 'JSESSIONID=F88D73AE7E90FF6978647596DEF683EE'
// 初始wiki的页面pageId，https://wiki.dotfashion.cn/pages/viewpage.action?pageId=1075075692
const queue = ['1220722544'];
const pages = [];

// 对象转成queryString
const obj2QueryStr = (paramObject) => {
    return Object.keys(paramObject)
        .map((key) => `${key}=${paramObject[key]}`)
        .join('&');
}

// 请求子节点
const getChildContent = async (param) => {
    const queryStr = obj2QueryStr(param);
    const text = await fetch(`https://wiki.dotfashion.cn/plugins/pagetree/naturalchildren.action?${queryStr}`, {
        method: 'GET',
        headers: {
            cookie: wikiToken
        }
    }).then(r => r.text());
    parseHtml(text);
}

// 解析html
const parseHtml = (htmlText) => {
    const dom = new JSDOM(htmlText);
    const document = dom.window.document;
    let lis = document.querySelectorAll('ul > li');
    for (let v of lis) {
        let hasChild = v.querySelector('div.plugin_pagetree_childtoggle_container > a.plugin_pagetree_childtoggle')
        let href = v.querySelector('div:nth-child(2) > span > a').href;
        pages.push(`https://wiki.dotfashion.cn${href}`);
        // 如果有子节点，推入队列
        if (hasChild) {
            let pageId = v.querySelector('div.plugin_pagetree_childtoggle_container > a').getAttribute('data-page-id');
            queue.push(pageId);
        }
    }
}

// 线程数量
const thread = 4;

// 标准化请求参数
const normalizeParam = (pageId) => {
    return {
        decorator: 'none',
        excerpt: false,
        sort: 'position',
        reverse: false,
        disableLinks: false,
        expandCurrent: true,
        placement: 'sidebar',
        hasRoot: true,
        pageId: pageId, //当前节点id
        treeId: 0,
        startDepth: 0,
        mobile: false,
        _: Date.now(),
    }
}

const taskRunner = async (isBreak) =>  {
    let pageId = queue.shift();
    while (pageId) {
        console.time(`捞取${pageId}`)
        const param = normalizeParam(pageId);
        const content = await getChildContent(param);
        parseHtml(content);
        console.timeEnd(`捞取${pageId}`)
        pageId = !isBreak ? queue.shift() : null;
    }
}

const createConcurrent = async () => {
    console.time('开始获取');
    // 初始获取大于线程数数据
    await taskRunner(true);
    const tasks = new Array(thread).fill(null).map(taskRunner);
    await Promise.all(tasks);
    await writeFile('./wiki-pages.json', JSON.stringify(pages, null, 2), 'utf-8');
    console.timeEnd('开始获取');
}
createConcurrent().then(() => {
    console.log('任务全部完成!')
});
