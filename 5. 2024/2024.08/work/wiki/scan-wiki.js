import fetch from 'node-fetch';
import jsdom from 'jsdom';
import { writeFile } from 'node:fs/promises';
// wiki-page.json文件是wiki页面的url列表
import wikiPages from './pages/4.json' assert { type: "json" };

// wiki页面的cookie
const wikiToken = 'JSESSIONID=6D387FF18ABAD2A613ECFD9CB7143A6A'

const { JSDOM } = jsdom;
const result = [];
let completedTasks = 0; // 已完成的任务数

// 使用JSDOM解析HTML
const parseHTML = (html) => {
    const dom = new JSDOM(html);
    const document = dom.window.document;
    const content = document.querySelector('#main-content')?.textContent;
    return content;
}

const checkContent = (pageUrl, html) => {
    let wikiObj = { page: pageUrl, dangerList: [] };
    const content = parseHTML(html);
    if (!content) {
        wikiObj.dangerList.push({ key: 'emptyPage', match: 'emptyPage', index: 0 })
    } else {
        const regex1 = /\b((AKID)[a-zA-Z0-9]{32})(?:['"\n\r\s`]|;|$)/ig;
        const regex2 = /(?:secret|security|secret_key)(?:[0-9a-z\-_\t .]{0,20})(?:[\s'"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:['"\s=`]){0,5}([a-zA-Z0-9]{32})(?:['"\n\r\s`]|;|$)/ig;
        const regex3 = /\b((LTAI)[a-z0-9]{12,20})(?:['"\n\r\s`]|;|$)/ig;
        const regex4 = /(?:secret|security|secret_key|secretKey)(?:[0-9a-z\-_\t .]{0,20})(?:[\s'"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:['"\s=`]){0,5}([a-zA-Z0-9]{30})(?:['"\n\r\s`]|;|$)/ig;
        const regex5 = /\b((A3T[A-Z0-9]|AKIA|AGPA|AIDA|AROA|AIPA|ANPA|ANVA|ASIA)[a-zA-Z0-9]{16})(?:['"\n\r\s`]|;|$)/ig;
        const regex6 = /(?:secret|security|secret_key|secretKey)(?:[0-9a-z\-_\t .]{0,20})(?:[\s'"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:['"\s=`]){0,5}([a-zA-Z0-9\+\~\/\=\-\_\.]{40})(?:['"\n\r\s`]|;|$)/ig;
        const regex7 = /\b((GOOG|GOOGL)[A-Za-z0-9]{56,57})(?:['"\n\r\s`]|;|$)/ig;
        const regex8 = /(?:secret|security|secret_key|secretKey)(?:[0-9a-z\-_\t .]{0,20})(?:[\s'"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:['"\s=`]){0,5}([a-zA-Z0-9\+\~\/\=\-\_\.]{40})(?:['"\n\r\s`]|;|$)/ig;
        const regex9 = /(?:client_id|clientId)(?:[0-9a-z\-_\t .]{0,20})(?:[\s'"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:['"\s=`]){0,5}([0-9a-z]{8}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{12})(?:['"\n\r\s`]|;|$)/ig;
        const regex10 = /(?:client_secret|clientSecret)(?:[0-9a-z\-_\t .]{0,20})(?:[\s'"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:['"\s=`]){0,5}([a-zA-Z0-9\+\~\/\=\-\_\.]{40})(?:['"\n\r\s`]|;|$)/ig;
        const regex11 = /(?:token|secret|client|passwd|password|auth|access|pwd|aclPwd)(?:[0-9a-z\-_\t .]{0,20})(?:[\s'"]){0,3}(?:=|>|:{1,3}=|\|\|:|<=|=>|:|\?=)(?:['"\s=`]){0,5}([0-9a-z\-_.=]{10,150})(?:['"\n\r\s`]|;|$)/ig;
        const check = (regx, key) => {
            let match = content.matchAll(regx);
            for (let v of match) {
                if (v) {
                    wikiObj.dangerList.push({ key, match: v, index: v.index })
                }
            }
        }
        check(regex1, 'regex1');
        check(regex2, 'regex2');
        check(regex3, 'regex3');
        check(regex4, 'regex4');
        check(regex5, 'regex5');
        check(regex6, 'regex6');
        check(regex7, 'regex7');
        check(regex8, 'regex8');
        check(regex9, 'regex9');
        check(regex10, 'regex10');
        check(regex11, 'regex11');
    }
    if (wikiObj.dangerList.length) {
        result.push(wikiObj);
    }
}

const loadSource = async (pageUrl) => {
    const htmlText = await fetch(pageUrl, {
        credentials: 'include',
        headers: {
            cookie: wikiToken
        }
    }).then(r => r.text());
    return htmlText;
}

// 生成调用loadSource队列，3线程调用
const queue = wikiPages;
const totalTasks = queue.length; // 任务总数
const thread = 3;

// 任务队列，每次完成后会再次触发，直到任务队列为空
const startTask = async () => {
    if (queue.length) {
        const taskUrl = queue.shift();
        console.log(`start check ${taskUrl}`);
        console.time(`check ${taskUrl}`)
        const htmlText = await loadSource(taskUrl);
        checkContent(taskUrl, htmlText);
        console.timeEnd(`check ${taskUrl}`)

        completedTasks += 1; // 更新已完成的任务数
        console.log(`Progress: ${completedTasks}/${totalTasks}`); // 输出当前进度

        // 避免栈溢出
        startTask();
    } else {
        console.timeEnd('scan-wiki');
        console.log(Date.now());
        await writeFile('./wiki-result.json', JSON.stringify(result, null, 2), 'utf-8');
    }
}

// 创建线程
const createConcurrent = () => {
    for (let i = 0; i < thread; i++) {
        startTask();
    }
}

console.log(Date.now());
console.time('scan-wiki');
createConcurrent();
