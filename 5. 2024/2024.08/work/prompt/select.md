请帮我改造代码，把所有的select 组件参数按照handleSelectProps 来改造，用于整合select组件的参数，包括: title, multiple, keygen, renderItem, clearable, absolute, autoAdapt, format, onFilter, compressed 这些参数.不包含placeholder 这个参数

其中keygen, renderItem有默认值，keygen默认值为"dictCode"，renderItem默认值为"dictNameZh";
clearable，absolute，autoAdapt这三个参数，都是默认值为true; format 参数等于keygen参数;onFilter参数是一个函数，接收一个text参数，返回一个函数，这个函数接收一个d参数，返回一个布尔值，用于过滤select的选项; compressed 参数是multiple参数的值。如果multiple为true，则compressed为true。

回答只需要输出改造后的代码片段2，不需要代码片段1的代码，不要省略代码

代码片段1: packages/micro-inbound/src/lib/business-component-helper.js
```
/**
 * Select 通用默认值
 */
const handleSelectProps = (props = {}) => {
  const {
    title,
    multiple,
    keygen = "dictCode",
    renderItem = "dictNameZh",
  } = props;

  return {
    label: title,
    keygen: keygen,
    format: keygen,
    renderItem: renderItem,
    onFilter: (text) => (d) =>
      d[renderItem].toLowerCase().includes(text.toLowerCase()),
    multiple: multiple ? true : false,
    compressed: multiple ? true : false,
    clearable: true,
    absolute: true,
    autoAdapt: true,
  };
};

export {
  handleSelectProps,
}
```

代码片段2：
