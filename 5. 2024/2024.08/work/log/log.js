const xlsx = require('xlsx');
const fs = require('fs');

const typeList = [
  { id: 'copy', name: '复制粘贴' },
  { id: 'format', name: '输入格式有误' },
  { id: 'check_code', name: '验证码' },
  { id: 'stamp', name: '输入超时' },
  { id: 'temp', name: '空' },
]

const pathList = [
  { id: 'outbound-mgt/package/parcel-package', name: '合并打包'},
  { id: 'inbound-mgt/inbound-big-check', name: '大件稽查'},
  { id: 'inbound-mgt/inbound-weight-check', name: '重量稽查'},
]

// 从文件中读取JSON数据
const jsonData = fs.readFileSync('data.json', 'utf8');
const data = JSON.parse(jsonData);

const outputData = data.data.map((item) => {
  return {
    "类型": typeList.find((type) => type.id === item.type).name || item.type,
    "输入内容": item.input,
    "工位": item.workLocation,
    "用户名": item.username,
    "仓库": item.warehouseId,
    "页面": pathList.find((path) => path.id === item.path).name || item.path,
    timestamp: item.timestamp,
  }
})

// 创建一个新的工作表
const worksheet = xlsx.utils.json_to_sheet(outputData);

// 创建一个新的工作簿
const workbook = xlsx.utils.book_new();
xlsx.utils.book_append_sheet(workbook, worksheet, "Sheet1");

// 保存工作簿到文件
xlsx.writeFile(workbook, 'output.xlsx');

console.log("Data has been written to output.xlsx");
