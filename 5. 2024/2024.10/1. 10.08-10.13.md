# 10 月份 第 2 周
## 本周 todo
- (?)apisix 
- (bruce zou) 预案接口
- 子仓集货词条优化

## 2024.10.08 周二
todo
- (?)apisix 
- (bruce zou) 预案接口
- 子仓集货词条优化

sam 今日todo
- 不升起键盘
- bug 复盘分析
- 二分

我的今日todo
- (alex)云配置 ✅
- 开会 ✅
- 发版 ✅
- (alex)wsk 统计

## 2024.10.09 周三
todo
- 需求评审 ✅
- OFC-208000 3P - Smart Call - 打包呼叫看板
  - 看板优化
- feat/dragTab

- 词条 

- 拼接词条
  - 子仓集货
- 声效优化



## 2024.10.10 周四
todo
- 发版
- OFC-209968 wsk支持时差/新增拣货时间 
  - 开发 ✅
- 拼接词条问题探索 
  - 导出缺失词条（基于页面
  - 导出页面所有词条（包括现有的跟缺失的）
  

t函数正则:

t\('([^']+)',? ?('(?:[^']|\\')*')?\)

帮我改写下代码

1. 导出的文档第一列是t函数的内容，第二列是第一个参数（也就是词条），第三列是文件路径
2. 支持跨行匹配

目前已经能输出分析文档了，下一步是做什么？

part1 场景：产品让我导出这个页面的所有词条

目前已经满足 ✅

part2 场景：分辨捞出来的词条是不是缺失词条，这个感觉应该是另一个工具：




## 2024.10.11 周五
todo
- inputmode 研究


## 2024.10.12 周六
todo
- 月绩效
- inputmode 研究 ✅

研究透了，改动很小

知识分享。⼩组内(1分1个，群聊)、部⻔内(3分1个，⾃发5分，会议)。总分12分。

主动优化系统，总分6分。包括但不限于大事务，长事务，RT，LCP，效能方面，必须由自身主动发现发起的，报障、上级指派不包含在内，优化必须以完成上线且有效；


### sam哥的
wms-la 跨页面通信 技术调研
https://wiki.dotfashion.cn/pages/viewpage.action?pageId=1493707482

【技术调研】part1: mot-la 系统的虚拟键盘聚焦 全局设置
https://wiki.dotfashion.cn/pages/viewpage.action?pageId=1500502551

【OFC-205217】PDA操作页面统一增加扫描条码成功后的提示音【WMS-LA】
https://wiki.dotfashion.cn/pages/viewpage.action?pageId=1512575113

【 OFC-206039】全流程看板 页面词条优化
https://wiki.dotfashion.cn/pages/viewpage.action?pageId=1506871534


### 我自己的


## 2024.10.13 周日
休息日