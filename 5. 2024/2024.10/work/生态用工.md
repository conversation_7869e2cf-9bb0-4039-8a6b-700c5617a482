# 生态用工

分支: feat/attendance OFC-215580

- wms
  - 人员打卡记录（新功能需迁移） ✅
  - 人员考勤报表（新功能需迁移）✅
  - 我的团队（新功能需迁移）
  - 组员安排管理 ✅

- wms
  - 人员打卡记录（新功能需迁移）
  - 人员考勤报表（新功能需迁移）
  - 组员安排管理
  - 我的团队（新功能需迁移）
  - 补点名审核（新功能需迁移）
- wsk
  - 组员排班
  - 组员安排管理
  - 组员点名

- wms
  - 人员打卡记录（新功能需迁移）
  - 人员考勤报表（新功能需迁移）
  - 我的团队（新功能需迁移）
  - 组员安排管理

- wms
  - 补点名审核（新功能需迁移）
- wsk
  - 组员排班
  - 组员安排管理
  - 组员点名


补点名功能（新功能需迁移）

## la
/nav/jsx/our-team

【新增】人员考勤报表 ✅
packages/base/src/component/management-sys/attendance-list/me.json

【新增】人员打卡记录 ✅
packages/base/src/component/management-sys/attendence-records/me.json

【新增】人员班次配置 ✅
packages/base/src/component/management-sys/personnel-shift-config/me.json


## eu 欧洲
【新增】我的团队
src/component/nav/jsx/our-team

【新增】人员考勤报表
https://wms-eu-test01.dotfashion.cn/#/management-sys/attendance-list

【新增】人员班次配置
https://wms-eu-test01.dotfashion.cn/#/management-sys/personnel-shift-config

【新增】人员打卡记录
https://wms-eu-test01.dotfashion.cn/#/management-sys/attendence-records

## me 中东
OFC-215083

OFC-215223 我的团队

【新增】我的团队

【新增】人员考勤报表

【新增】人员班次配置

【新增】人员打卡记录


/front/schedule/my_teams_attendance_rate_week_trend
/front/schedule/my_teams_scheduling
/front/schedule/query_schedule_staff_detail

/front/schedule/gch/peer_avg_attendance_rate
/front/schedule/gch/dept_leader_rate_trend
/front/schedule/gch/team_leader_rate_trend

/front/dept/query  
 @Amigo Zheng(Amigo Zheng) 帮我看看这几个参数的传参逻辑
warehouseId 是不是一定有？
displayOutside  needAccessControl  needStaffNum  有没有传false的逻辑

