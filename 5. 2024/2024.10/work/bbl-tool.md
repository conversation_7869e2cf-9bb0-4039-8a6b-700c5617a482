# BBL 工具

1. 根据接口请求，获取最新版本号
https://cloud-now.sheincorp.cn/trans/api/snapVersion?npid=242

接口返回
{
    "code": 0,
    "data": {
        "version": "diih9z1r8a",
        "language": [
            "CN",
            "US",
            "PT",
            "ES"
        ]
    }
}

2. 获取最新版本号，在根据版本号获取对应的 json 文件

https://assets.dotfashion.cn/webassets/babel_tower_snap/frontend/production/242/diih9z1r8a/CN.json

{
  "code": 0,
  "data": {
    "(已完成任务数/任务总数)": {CN: "(已完成任务数/任务总数)", nid: 2143318}
    // ...
  }
}