# 打包机

EnterWorkNumber 组件 
用于接口返回isPermitted 时，弹窗，触发NavStore.working 方法

NavStore
/micro-outbound/src/component/nav/reducers.js
/micro-outbound/src/component/nav/jsx/work.jsx

Work 组件，是基于url 触发的，要将精准出库扫描剥离出来

工位：
2432434

触发清空数据
clearPageData

/wpoc/front/precise_package/scan_container
/wpoc/front/precise_package/scan_barcode
/wpoc/front/precise_package/scan_material
/wpoc/front/precise_package/check_weight
/wpoc/front/precise_package/scan_check_code

{
    "code": "502917",
    "msg": "当前用户未上机，请重新操作上机",
    "info": null
}

- 扫箱号
  - containerCodeSearch 
    - checkContainerCodeApi
      - /wpoc/front/precise_package/scan_container
- 扫描商品条码
  - barcodeSearch
    - checkBarcodeApi
      - /wpoc/front/precise_package/scan_barcode
- 扫描耗材条码
  - materialSearch
    - checkMaterialApi
      - /wpoc/front/precise_package/scan_material
- 称重
  - weightSearch
    - checkWeightApi
      - /wpoc/front/precise_package/check_weight
- 扫描校验码
  - codeCheckingSearch
    - codeCheckingApi
      - /wpoc/front/precise_package/scan_check_code

todo ✅
扫描商品条码 ,自动填充耗材条码EXP28AAAA，并自动回车


scanBarcodeInfo
非单品 un_single_package_info.package_weight
单品：single_package_info.weight 

DBJ-23

DBJ-22  工位  HBS820  箱号  1000171451 商品条码   @Amigo Zheng(Amigo Zheng)

/wkb/rank/query_today

当前用户未上机，请重新操作上机

打包过程中当前上机工位被其他操作原顶下线，点击确定关闭提示弹窗，清空界面数据

#操作页面的箱号/商品条码/耗材条码/称重/校验码文本框置灰，提交异常/操作配置按钮置灰，右上角下机按钮文案变更为上机，显示上机弹窗并清空上机弹窗的工位条码文本框，如操作3-2


联调数据
2432434 123456778  打包工位
DBJ-23 DBJ-22  打包机工位


"BXEF11197", S200089-0/
			"BXEF11063",
			"BXEF11136",
			"BXEF11032"

      EXP28AAAA