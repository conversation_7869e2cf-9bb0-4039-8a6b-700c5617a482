# sam哥 改进计划

针对sam哥的问题点，一一改进：

## 问题点1: sam哥的需求超期
建监控

前端过程管理规则: 

0. 「工作流」：前端任务工作流：评估->开发->联调/自测->导出BBL词条->code review->提测
1. 「预估工时」：前端任务的预估工时是以amigo的开发时间为基础，乘于个人的开发效率系数
   1. sam哥的开发效率系数，目前是1.5（后续根据实际情况调整）
2. 「开发评估」：排期2天及以上的开发任务，需要在任务开发前期评估开发时间
   1. 如果评估的开发时间跟预估工时相差较大，说明跟amigo预估的开发方案存在出入，需要及时沟通；
   2. 如果确认了开发方案后，还是存在较大的差异，可以上升至TL层面进行二次评估
3. 「风险预警」：任务开发过程中，存在风险的，需要及时沟通，提前预警
  
## 问题点2：经常忘事
改进措施：

1. 让sam哥写todo, 并且周期性跟sam哥同步事项
   1. 起到提醒作用
   2. 是让sam哥自己养成习惯

## 问题点3：沟通
改进措施：

暂无

## 问题点4: 产出不足

改进措施：

1. ai 效率工具：github copilot/cursor 
2. mock 工具