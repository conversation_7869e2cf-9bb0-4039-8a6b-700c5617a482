function findTitlesByRules(data, paths) {
  // Recursively search the tree structure
  function searchTree(tree, path) {
    for (const node of tree) {
      if (node.rule === path) {
        return node.title;
      }
      if (node.children && node.children.length > 0) {
        const result = searchTree(node.children, path);
        if (result) {
          return result;
        }
      }
    }
    return null;
  }

  // Find titles for all provided paths
  return paths.map(path => {
    return {
      path,
      title: searchTree(data, path)
    };
  });
}

// Test data
const data = [

  {
    "id": "8051",
    "title": "JIRA单跟进",
    "methodType": "post",
    "rule": "/jira-follow-up",
    "pid": "0",
    "type": "1",
    "remark": "",
    "remarkPositionIdList": [],
    "children": [
      {
        "id": "8061",
        "title": "JIRA单跟进",
        "methodType": "post",
        "rule": "/jira-follow-up/list",
        "pid": "8051",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": []
      }
    ]
  },
  {
    "id": "62158",
    "title": "包裹管理系统",
    "methodType": "get",
    "rule": "/packages-management-system",
    "pid": "0",
    "type": "1",
    "remark": "产品经理-Alan Lai",
    "remarkPositionIdList": [],
    "children": [
      {
        "id": "61906",
        "title": "运营监控",
        "methodType": "get",
        "rule": "/board",
        "pid": "62158",
        "type": "1",
        "remark": "产品经理-Alan Lai",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "61907",
            "title": "海外仓储全流程看板",
            "methodType": "get",
            "rule": "/board-mgt/oversea-main-board",
            "pid": "61906",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "61878",
        "title": "包裹管理",
        "methodType": "get",
        "rule": "/package-mgt",
        "pid": "62158",
        "type": "1",
        "remark": "产品经理-Alan Lai",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "61817",
            "title": "出库包裹管理",
            "methodType": "get",
            "rule": "/package-mgt/package-list",
            "pid": "61878",
            "type": "1",
            "remark": "产品经理-Alex Chen",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61818",
            "title": "子包裹管理",
            "methodType": "get",
            "rule": "/package-mgt/child-package-list",
            "pid": "61878",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61819",
            "title": "合单建议管理",
            "methodType": "get",
            "rule": "/package-mgt/combine-suggest",
            "pid": "61878",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61863",
            "title": "强制齐套记录",
            "methodType": "get",
            "rule": "/package-mgt/force-order-record",
            "pid": "61878",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61918",
            "title": "入库稽查列表",
            "methodType": "get",
            "rule": "/package-mgt/warehouse-inspection-list",
            "pid": "61878",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62386",
            "title": "跨境包裹管理",
            "methodType": "get",
            "rule": "/package-mgt/cross-border-parcel",
            "pid": "61878",
            "type": "1",
            "remark": "产品经理-Alex Chen",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "61730",
        "title": "出库管理",
        "methodType": "get",
        "rule": "/outbound-mgt",
        "pid": "62158",
        "type": "1",
        "remark": "产品经理-Alan Lai",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "62387",
            "title": "发货管理",
            "methodType": "get",
            "rule": "/shipping-mgt",
            "pid": "61730",
            "type": "1",
            "remark": "产品经理-Alex Chen",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "62388",
                "title": "出库包裹装箱",
                "methodType": "get",
                "rule": "/shipping-mgt/package-boxing",
                "pid": "62387",
                "type": "1",
                "remark": "产品经理-Alex Chen",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "61731",
            "title": "拣货作业管理",
            "methodType": "get",
            "rule": "/outbound-mgt/picking-management",
            "pid": "61730",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [
              36
            ],
            "children": [
              {
                "id": "61732",
                "title": "拣货组管理",
                "methodType": "get",
                "rule": "/outbound-mgt/picking-management/picking-group-management",
                "pid": "61731",
                "type": "1",
                "remark": "",
                "remarkPositionIdList": [
                  36
                ],
                "children": []
              },
              {
                "id": "61733",
                "title": "拣货任务查询",
                "methodType": "get",
                "rule": "/outbound-mgt/picking-management/picking-task-query",
                "pid": "61731",
                "type": "1",
                "remark": "",
                "remarkPositionIdList": [
                  36
                ],
                "children": []
              },
              {
                "id": "61735",
                "title": "拣货任务明细",
                "methodType": "get",
                "rule": "/outbound-mgt/picking-management/picking-task-detail",
                "pid": "61731",
                "type": "1",
                "remark": "",
                "remarkPositionIdList": [
                  36
                ],
                "children": []
              },
              {
                "id": "61969",
                "title": "拣货容器状态查询",
                "methodType": "get",
                "rule": "/outbound-mgt/picking-management/pick-container-status-query",
                "pid": "61731",
                "type": "1",
                "remark": "",
                "remarkPositionIdList": [
                  36
                ],
                "children": []
              }
            ]
          },
          {
            "id": "61739",
            "title": "分播管理",
            "methodType": "get",
            "rule": "/outbound-mgt/sowing-management",
            "pid": "61730",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "61740",
                "title": "分播明细管理",
                "methodType": "get",
                "rule": "/outbound-mgt/sowing-management/sowing-detail",
                "pid": "61739",
                "type": "1",
                "remark": "产品经理-Alan Lai",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "62203",
                "title": "一分明细管理",
                "methodType": "post",
                "rule": "/outbound-mgt/sowing-management/first-sowing-detail",
                "pid": "61739",
                "type": "1",
                "remark": "产品经理-Alan Lai",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "61834",
            "title": "打包",
            "methodType": "get",
            "rule": "/outbound-mgt/package",
            "pid": "61730",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "61835",
                "title": "合并打包",
                "methodType": "get",
                "rule": "/outbound-mgt/package/parcel-package",
                "pid": "61834",
                "type": "1",
                "remark": "产品经理-Alan Lai",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "61836",
                "title": "合并打包日志查询",
                "methodType": "get",
                "rule": "/outbound-mgt/package/package-record",
                "pid": "61834",
                "type": "1",
                "remark": "产品经理-Alan Lai",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "62168",
            "title": "发货大箱装箱查询",
            "methodType": "get",
            "rule": "/outbound-mgt/delivery-box",
            "pid": "61730",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62169",
            "title": "发货大箱装箱明细",
            "methodType": "get",
            "rule": "/outbound-mgt/delivery-box-detail",
            "pid": "61730",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62191",
            "title": "批次管理",
            "methodType": "get",
            "rule": "/outbound-mgt/picking-management/batch-manage",
            "pid": "61730",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "61931",
        "title": "入库管理",
        "methodType": "get",
        "rule": "/inbound-mgt",
        "pid": "62158",
        "type": "1",
        "remark": "产品经理-Alan Lai",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "61932",
            "title": "重量稽查",
            "methodType": "get",
            "rule": "/inbound-mgt/inbound-weight-check",
            "pid": "61931",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61944",
            "title": "上架明细管理",
            "methodType": "get",
            "rule": "/inbound-mgt/shelf-detail-management",
            "pid": "61931",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61949",
            "title": "收货明细管理",
            "methodType": "get",
            "rule": "/inbound-mgt/receive-detail-management",
            "pid": "61931",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61968",
            "title": "大件稽查",
            "methodType": "get",
            "rule": "/inbound-mgt/inbound-big-check",
            "pid": "61931",
            "type": "1",
            "remark": "你好",
            "remarkPositionIdList": [
              1
            ],
            "children": []
          },
          {
            "id": "62140",
            "title": "转运管理",
            "methodType": "get",
            "rule": "/inbound-mgt/transport-manage",
            "pid": "61931",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "62123",
                "title": "分拣中心仓库关系管理",
                "methodType": "get",
                "rule": "/inbound-mgt/sort-center-warehouse-relation",
                "pid": "62140",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "62249",
                "title": "转运车辆管理",
                "methodType": "get",
                "rule": "/inbound-mgt/transfer-vehicle",
                "pid": "62140",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "62248",
                "title": "转运车辆任务查询",
                "methodType": "get",
                "rule": "/inbound-mgt/transfer-vehicle-task",
                "pid": "62140",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "62115",
                "title": "转运上架周转箱标签打印",
                "methodType": "get",
                "rule": "/inbound-mgt/transport-shelf-print",
                "pid": "62140",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "62096",
                "title": "转运上架周转箱任务明细",
                "methodType": "get",
                "rule": "/inbound-mgt/transport-shelf-detail-management",
                "pid": "62140",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "62099",
                "title": "转运上架周转箱任务管理",
                "methodType": "get",
                "rule": "/inbound-mgt/transport-shelf-management",
                "pid": "62140",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "62360",
            "title": "体积重稽查",
            "methodType": "get",
            "rule": "/inbound-mgt/inbound-volume-re-check",
            "pid": "61931",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "62298",
        "title": "库内管理",
        "methodType": "get",
        "rule": "/stock-mgt",
        "pid": "62158",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "62295",
            "title": "包裹库存查询",
            "methodType": "get",
            "rule": "/package-mgt/package-stock-inquiry",
            "pid": "62298",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62300",
            "title": "包裹移位单",
            "methodType": "get",
            "rule": "/package-mgt/package-shift-order",
            "pid": "62298",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "61880",
        "title": "异常管理",
        "methodType": "get",
        "rule": "/exception-mgt",
        "pid": "62158",
        "type": "1",
        "remark": "产品经理-Alan Lai",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "61813",
            "title": "异常查询",
            "methodType": "get",
            "rule": "/exception-mgt/exception-search",
            "pid": "61880",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61814",
            "title": "异常明细查询",
            "methodType": "get",
            "rule": "/exception-mgt/exception-details-search",
            "pid": "61880",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62320",
            "title": "异常暂存查询",
            "methodType": "get",
            "rule": "/exception-mgt/exception-temporary-search",
            "pid": "61880",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "52442",
        "title": "系统配置",
        "methodType": "get",
        "rule": "/system",
        "pid": "62158",
        "type": "1",
        "remark": "产品经理-Alan Lai",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "61736",
            "title": "出库配置",
            "methodType": "get",
            "rule": "/system/outbound-cfg",
            "pid": "52442",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "62185",
                "title": "拣货组生成策略",
                "methodType": "post",
                "rule": "/system/outbound-cfg/picking-group-strategy",
                "pid": "61736",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "62179",
                "title": "圈单规则配置",
                "methodType": "post",
                "rule": "/system/outbound-cfg/circle-order-rule-config",
                "pid": "61736",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "61738",
                "title": "拣货组配置",
                "methodType": "get",
                "rule": "/system/outbound-cfg/picking-group-configuration",
                "pid": "61736",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "61910",
                "title": "库位合单建议上限配置",
                "methodType": "get",
                "rule": "/system/outbound-cfg/location-suggest-limit-cfg",
                "pid": "61736",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "61871",
                "title": "超时自动齐套配置",
                "methodType": "post",
                "rule": "/system/outbound-cfg/force-order-configuration",
                "pid": "61736",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "61900",
                "title": "海外平台集运仓履约时效配置",
                "methodType": "get",
                "rule": "/system/outbound-cfg/oversea-time-config",
                "pid": "61736",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "62334",
                "title": "超时异常下架配置",
                "methodType": "get",
                "rule": "/system/outbound-cfg/exception-take-down-config",
                "pid": "61736",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "61915",
            "title": "入库配置",
            "methodType": "get",
            "rule": "/system/inbound-cfg",
            "pid": "52442",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "61916",
                "title": "入库稽查配置",
                "methodType": "get",
                "rule": "/system/inbound-cfg/inbound-check",
                "pid": "61915",
                "type": "1",
                "remark": "产品经理-Alan Lai",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          }
        ]
      }
    ]
  },
  {
    "id": "62159",
    "title": "商品管理系统",
    "methodType": "get",
    "rule": "/products-management-system",
    "pid": "0",
    "type": "1",
    "remark": "备注信息",
    "remarkPositionIdList": [],
    "children": [
      {
        "id": "2000",
        "title": "入库管理",
        "methodType": "get",
        "rule": "/qms",
        "pid": "62159",
        "type": "1",
        "remark": "产品经理-曾秋娟",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "2030",
            "title": "收货管理",
            "methodType": "get",
            "rule": "/qms/receipt-management",
            "pid": "2000",
            "type": "1",
            "remark": "产品经理-曾秋娟",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2035",
                "title": "收货单管理",
                "methodType": "get",
                "rule": "/qms/receipt-management/receipt/list",
                "pid": "2030",
                "type": "1",
                "remark": "wmsdotfashion",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2041",
                "title": "收货单明细查询",
                "methodType": "get",
                "rule": "/qms/receipt-management/detail/_ALL_",
                "pid": "2030",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2042",
                "title": "运单明细查询列表",
                "methodType": "get",
                "rule": "/qms/receipt-management/package-detail/_ALL_",
                "pid": "2030",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3788",
                "title": "入库差异复议",
                "methodType": "get",
                "rule": "/qms/receipt-management/in-storage-diff",
                "pid": "2030",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4632",
                "title": "差异订单列表",
                "methodType": "get",
                "rule": "/qms/receipt-management/difference-order",
                "pid": "2030",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5735",
                "title": "入库安检",
                "methodType": "get",
                "rule": "/qms/receipt-management/storage-security",
                "pid": "2030",
                "type": "1",
                "remark": "产品经理-王鑫",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8747",
                "title": "送检单列表",
                "methodType": "get",
                "rule": "/qms/receipt-management/inspection-list",
                "pid": "2030",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "4830",
            "title": "次品退供",
            "methodType": "get",
            "rule": "/qms/defective-new",
            "pid": "2000",
            "type": "1",
            "remark": "产品经理-索韵秋",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "4831",
                "title": "退供通知单",
                "methodType": "get",
                "rule": "/qms/defective/all-return-new",
                "pid": "4830",
                "type": "1",
                "remark": "产品经理-索韵秋",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4832",
                "title": "退供交接明细",
                "methodType": "get",
                "rule": "/qms/defective/return-handover-detail",
                "pid": "4830",
                "type": "1",
                "remark": "产品经理-索韵秋",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4838",
                "title": "退供商品明细",
                "methodType": "get",
                "rule": "/qms/defective/return-goods-detail",
                "pid": "4830",
                "type": "1",
                "remark": "产品经理-索韵秋",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4833",
                "title": "退供装箱查询",
                "methodType": "get",
                "rule": "/qms/defective/return-box-query",
                "pid": "4830",
                "type": "1",
                "remark": "产品经理-索韵秋",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4834",
                "title": "退供装箱明细",
                "methodType": "get",
                "rule": "/qms/defective/return-box-detail",
                "pid": "4830",
                "type": "1",
                "remark": "产品经理-索韵秋",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4835",
                "title": "退供流程配置",
                "methodType": "get",
                "rule": "/sysconfig/inbound/return-process-config",
                "pid": "4830",
                "type": "1",
                "remark": "产品经理-索韵秋",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4836",
                "title": "退供装箱扫描",
                "methodType": "get",
                "rule": "/qms/defective/refund-box-scan-new",
                "pid": "4830",
                "type": "1",
                "remark": "产品经理-索韵秋",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4837",
                "title": "退供操作记录",
                "methodType": "get",
                "rule": "/qms/defective/return-record-query-new",
                "pid": "4830",
                "type": "1",
                "remark": "产品经理-索韵秋",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6319",
                "title": "多货扫描",
                "methodType": "get",
                "rule": "/qms/defective/de-product-scan",
                "pid": "4830",
                "type": "1",
                "remark": "产品经理-索韵秋",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8317",
                "title": "正品退供积压数据监控",
                "methodType": "get",
                "rule": "/qms/defective/genuine-product-returns-monitoring",
                "pid": "4830",
                "type": "1",
                "remark": "产品经理-索韵秋",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4821",
                "title": "稽查单管理",
                "methodType": "get",
                "rule": "/qms/defective/check-list",
                "pid": "4830",
                "type": "1",
                "remark": "产品经理-索韵秋",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4822",
                "title": "稽查单明细",
                "methodType": "get",
                "rule": "/qms/defective/check-list-detail",
                "pid": "4830",
                "type": "1",
                "remark": "产品经理-索韵秋",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5694",
                "title": "退供仓库配置",
                "methodType": "get",
                "rule": "/sysconfig/inbound/return-subwarehouse-config",
                "pid": "4830",
                "type": "1",
                "remark": "产品经理-索韵秋",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6420",
                "title": "异常退供扫描",
                "methodType": "get",
                "rule": "/qms/defective/de-product-scan-new",
                "pid": "4830",
                "type": "1",
                "remark": "产品经理-索韵秋",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9628",
                "title": "退供预发货扫描",
                "methodType": "get",
                "rule": "/qms/defective/pre-shipment-scan",
                "pid": "4830",
                "type": "1",
                "remark": "产品经理-索韵秋",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2034",
            "title": "入仓管理",
            "methodType": "get",
            "rule": "/qms/warehousing",
            "pid": "2000",
            "type": "1",
            "remark": "产品经理-曾秋娟",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2501",
                "title": "入库单查询",
                "methodType": "get",
                "rule": "/inbound/storage-query/list",
                "pid": "2034",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2505",
                "title": "入库单明细",
                "methodType": "get",
                "rule": "/inbound/storage-query-detail/list",
                "pid": "2034",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4760",
                "title": "新检换入库装箱(旧版)",
                "methodType": "get",
                "rule": "/qms/warehousing/into-scanning-upgrade",
                "pid": "2034",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7618",
                "title": "检换入库(新版)",
                "methodType": "get",
                "rule": "/inbound/warehouse-packing",
                "pid": "2034",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2161",
            "title": "报表管理",
            "methodType": "get",
            "rule": "/qms/statement-management",
            "pid": "2000",
            "type": "1",
            "remark": "产品经理-曾秋娟",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2167",
                "title": "员工检换产能报表",
                "methodType": "get",
                "rule": "/qms/statement-management/quality-performance",
                "pid": "2161",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2208",
                "title": "打标数据",
                "methodType": "get",
                "rule": "/board/marking/list",
                "pid": "2161",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "9334",
            "title": "其他入库管理",
            "methodType": "get",
            "rule": "/inbound/other-storage-manage",
            "pid": "2000",
            "type": "1",
            "remark": "产品经理-曾秋娟",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "9435",
                "title": "其他入库装箱配置",
                "methodType": "get",
                "rule": "/inbound/other-storage-manage/storage-rule-config",
                "pid": "9334",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9440",
                "title": "非正品分箱配置",
                "methodType": "get",
                "rule": "/inbound/other-storage-manage/non-genuine-config",
                "pid": "9334",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9445",
                "title": "其他入库装箱扫描",
                "methodType": "get",
                "rule": "/inbound/other-storage-manage/inbound-packing-scan",
                "pid": "9334",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9474",
                "title": "非正品分箱查询",
                "methodType": "get",
                "rule": "/inbound/other-storage-manage/non-genuine-boxing",
                "pid": "9334",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9477",
                "title": "非正品分箱明细",
                "methodType": "get",
                "rule": "/inbound/other-storage-manage/non-genuine-boxing-detail",
                "pid": "9334",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9479",
                "title": "多货差异列表",
                "methodType": "get",
                "rule": "/inbound/other-storage-manage/muti-stock-diff",
                "pid": "9334",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2155",
            "title": "看板管理",
            "methodType": "get",
            "rule": "/qms/home",
            "pid": "2000",
            "type": "1",
            "remark": "产品经理-曾秋娟",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2156",
                "title": "回货数据看板",
                "methodType": "get",
                "rule": "/qms/home/<USER>",
                "pid": "2155",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2303",
                "title": "预约送货监控报表",
                "methodType": "get",
                "rule": "/qms/home/<USER>",
                "pid": "2155",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2896",
                "title": "订单入库差异看板",
                "methodType": "get",
                "rule": "/board/diff-of-Inwarehose-order",
                "pid": "2155",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "612",
            "title": "特殊入库管理",
            "methodType": "get",
            "rule": "/inbound",
            "pid": "2000",
            "type": "1",
            "remark": "产品经理-王鑫",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "613",
                "title": "入库管理",
                "methodType": "get",
                "rule": "/inbound/reject-order",
                "pid": "612",
                "type": "1",
                "remark": "产品经理-王鑫",
                "remarkPositionIdList": [],
                "children": [
                  {
                    "id": "9159",
                    "title": "退货收包扫描(新版)",
                    "methodType": "post",
                    "rule": "/inbound/reject-order/return-package-scan",
                    "pid": "613",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "614",
                    "title": "收货单",
                    "methodType": "get",
                    "rule": "/inbound/reject-order/list",
                    "pid": "613",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "617",
                    "title": "收货单明细",
                    "methodType": "get",
                    "rule": "/inbound/reject-order/detail",
                    "pid": "613",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "2899",
                    "title": "退货收包扫描",
                    "methodType": "get",
                    "rule": "/inbound/reject-order/reback-receive-scan",
                    "pid": "613",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "3029",
                    "title": "客退申请单",
                    "methodType": "get",
                    "rule": "/inbound/return-orders",
                    "pid": "613",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": [
                      {
                        "id": "3030",
                        "title": "客退单",
                        "methodType": "get",
                        "rule": "/inbound/return-orders/return-order-query",
                        "pid": "3029",
                        "type": "1",
                        "remark": "产品经理-王鑫",
                        "remarkPositionIdList": [],
                        "children": []
                      },
                      {
                        "id": "3032",
                        "title": "客退单明细",
                        "methodType": "get",
                        "rule": "/inbound/return-orders/return-order-detail",
                        "pid": "3029",
                        "type": "1",
                        "remark": "产品经理-王鑫",
                        "remarkPositionIdList": [],
                        "children": []
                      }
                    ]
                  },
                  {
                    "id": "3886",
                    "title": "退货质检结果",
                    "methodType": "get",
                    "rule": "/inbound/return-quality-inspection",
                    "pid": "613",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "4275",
                    "title": "拒收包裹退货收包",
                    "methodType": "get",
                    "rule": "/inbound/reject-order/reback-receive-scan-new",
                    "pid": "613",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  }
                ]
              },
              {
                "id": "620",
                "title": "入库质检管理",
                "methodType": "get",
                "rule": "/inbound/reject-check-manage",
                "pid": "612",
                "type": "1",
                "remark": "产品经理-王鑫",
                "remarkPositionIdList": [],
                "children": [
                  {
                    "id": "621",
                    "title": "入库质检扫描",
                    "methodType": "get",
                    "rule": "/inbound/reject-check-manage/reject-check-scan",
                    "pid": "620",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "639",
                    "title": "入库装箱查询",
                    "methodType": "get",
                    "rule": "/inbound/reject-check-manage/reject-box-query",
                    "pid": "620",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "643",
                    "title": "入库装箱明细查询",
                    "methodType": "get",
                    "rule": "/inbound/reject-check-manage/reject-box-detail-query",
                    "pid": "620",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "5340",
                    "title": "非特殊入库黑码装箱",
                    "methodType": "get",
                    "rule": "/inbound/nsp-black-pack",
                    "pid": "620",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "5344",
                    "title": "非特殊入库黑码装箱查询",
                    "methodType": "post",
                    "rule": "/inbound/nsp-black-query",
                    "pid": "620",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "8891",
                    "title": "大纸箱信息查询",
                    "methodType": "get",
                    "rule": "/inbound/reject-check-manage/big-container",
                    "pid": "620",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "9162",
                    "title": "上架信息查询",
                    "methodType": "post",
                    "rule": "/inbound/reject-check-manage/shelf-information-query",
                    "pid": "620",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  }
                ]
              },
              {
                "id": "2451",
                "title": "退货待上架列表（停用）",
                "methodType": "get",
                "rule": "/inbound/inventory-temp/list",
                "pid": "612",
                "type": "1",
                "remark": "产品经理-王鑫",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2924",
                "title": "多货处理",
                "methodType": "get",
                "rule": "/multiple-goods-deal",
                "pid": "612",
                "type": "1",
                "remark": "产品经理-王鑫",
                "remarkPositionIdList": [],
                "children": [
                  {
                    "id": "2925",
                    "title": "多货装箱管理",
                    "methodType": "get",
                    "rule": "/multiple-goods-deal/multiple-goods-manage",
                    "pid": "2924",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "2929",
                    "title": "多货装箱明细管理",
                    "methodType": "get",
                    "rule": "/multiple-goods-deal/multiple-goods-detail",
                    "pid": "2924",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "2931",
                    "title": "SKC与项号对应关系",
                    "methodType": "get",
                    "rule": "/multiple-goods-deal/skc-with-customs",
                    "pid": "2924",
                    "type": "1",
                    "remark": "产品经理-王鑫",
                    "remarkPositionIdList": [],
                    "children": []
                  }
                ]
              }
            ]
          },
          {
            "id": "2802",
            "title": "导入导出",
            "methodType": "get",
            "rule": "/qms/import-management",
            "pid": "2000",
            "type": "1",
            "remark": "产品经理-曾秋娟",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2126",
                "title": "导出下载",
                "methodType": "get",
                "rule": "/qms/export-management/download",
                "pid": "2802",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2163",
                "title": "导入查询",
                "methodType": "get",
                "rule": "/qms/import-management/import",
                "pid": "2802",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "5976",
            "title": "入库任务管理",
            "methodType": "get",
            "rule": "/inbound/tasks/new",
            "pid": "2000",
            "type": "1",
            "remark": "产品经理-Jayer Xu",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "6566",
                "title": "退供任务查询",
                "methodType": "get",
                "rule": "/inbound/task-management/return-supplier-task",
                "pid": "5976",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6567",
                "title": "退供任务明细",
                "methodType": "get",
                "rule": "/inbound/task-management/return-supplier-task-detail",
                "pid": "5976",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6568",
                "title": "退供移位查询",
                "methodType": "get",
                "rule": "/inbound/task-management/shift-order",
                "pid": "5976",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6569",
                "title": "退供移位明细",
                "methodType": "get",
                "rule": "/inbound/task-management/shift-order-detail",
                "pid": "5976",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6612",
                "title": "入库上架查询",
                "methodType": "post",
                "rule": "/inbound/task-management/putaway-task",
                "pid": "5976",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7422",
                "title": "入库上架明细",
                "methodType": "post",
                "rule": "/inbound/task-management/putaway-task-detail-new",
                "pid": "5976",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6615",
                "title": "入库上架明细(废弃)",
                "methodType": "post",
                "rule": "/inbound/task-management/putaway-task-detail",
                "pid": "5976",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6766",
                "title": "拉货任务管理",
                "methodType": "get",
                "rule": "/inbound/task-management/pull-task-management",
                "pid": "5976",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5973",
                "title": "任务查询(旧)",
                "methodType": "get",
                "rule": "/inbound/task-management/replenish-new",
                "pid": "5976",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5972",
                "title": "任务明细（旧）",
                "methodType": "get",
                "rule": "/inbound/task-management/replenish-detail-new",
                "pid": "5976",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8413",
                "title": "PT退供任务明细",
                "methodType": "get",
                "rule": "/inbound/task-management/return-task-detail",
                "pid": "5976",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8414",
                "title": "PT退供任务查询",
                "methodType": "get",
                "rule": "/inbound/task-management/return-task",
                "pid": "5976",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "5977",
            "title": "移位管理",
            "methodType": "get",
            "rule": "/in-warehouse/shift-order-new",
            "pid": "2000",
            "type": "1",
            "remark": "产品经理-曾秋娟",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "5975",
                "title": "移位单查询",
                "methodType": "get",
                "rule": "/inbound/shift-order-new/query",
                "pid": "5977",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5974",
                "title": "移位单明细查询",
                "methodType": "get",
                "rule": "/inbound/shift-order-new/detail",
                "pid": "5977",
                "type": "1",
                "remark": "产品经理-曾秋娟",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          }
        ]
      },
      {
        "id": "376",
        "title": "库内管理",
        "methodType": "get",
        "rule": "/in-warehouse",
        "pid": "62159",
        "type": "1",
        "remark": "备注信息",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "2805",
            "title": "任务管理",
            "methodType": "get",
            "rule": "wms/in/task",
            "pid": "376",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "368",
                "title": "任务查询",
                "methodType": "get",
                "rule": "/in-warehouse/replenish",
                "pid": "2805",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "373",
                "title": "任务明细",
                "methodType": "get",
                "rule": "/in-warehouse/replenish-detail",
                "pid": "2805",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9112",
                "title": "任务查询(新)",
                "methodType": "get",
                "rule": "/in-warehouse/replenish",
                "pid": "2805",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9113",
                "title": "任务明细(新)",
                "methodType": "get",
                "rule": "/in-warehouse/replenish-detail",
                "pid": "2805",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2808",
            "title": "交接管理",
            "methodType": "get",
            "rule": "wms/in/exchange",
            "pid": "376",
            "type": "1",
            "remark": "产品经理-Jayer Xu",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "7572",
                "title": "车辆调度查询",
                "methodType": "get",
                "rule": "/in-warehouse/car-scheduling",
                "pid": "2808",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "427",
                "title": "交接单查询",
                "methodType": "get",
                "rule": "/in-warehouse/receipt-query",
                "pid": "2808",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "431",
                "title": "交接单明细",
                "methodType": "get",
                "rule": "/in-warehouse/receipt-query-detail",
                "pid": "2808",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2970",
                "title": "交接操作明细查询",
                "methodType": "get",
                "rule": "/in-warehouse/handover-operation-detail/list",
                "pid": "2808",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3897",
                "title": "交接流程记录查询",
                "methodType": "get",
                "rule": "/in-warehouse/handover-process-record",
                "pid": "2808",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6052",
                "title": "周转箱交接推荐与记录(废除)",
                "methodType": "get",
                "rule": "/in-warehouse/box-handover-recommend-and-record",
                "pid": "2808",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7464",
                "title": "空框交接单明细",
                "methodType": "get",
                "rule": "/in-warehouse/empty-handover-detail",
                "pid": "2808",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7494",
                "title": "空框交接单",
                "methodType": "post",
                "rule": "/in-warehouse/empty-handover",
                "pid": "2808",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7588",
                "title": "转运调度管理",
                "methodType": "get",
                "rule": "/in-warehouse/transfer-scheduling-management",
                "pid": "2808",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7754",
                "title": "空框交接结果",
                "methodType": "get",
                "rule": "/in-warehouse/empty-handover-result",
                "pid": "2808",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7856",
                "title": "RFID空框交接单",
                "methodType": "post",
                "rule": "/in-warehouse/empty-handover-rfid",
                "pid": "2808",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7857",
                "title": "RFID空框交接单明细",
                "methodType": "post",
                "rule": "/in-warehouse/empty-handover-detail-rfid",
                "pid": "2808",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9634",
                "title": "交接拆托记录",
                "methodType": "get",
                "rule": "/in-warehouse/handover-disass",
                "pid": "2808",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2806",
            "title": "移位管理",
            "methodType": "get",
            "rule": "wms/in/move",
            "pid": "376",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "9624",
                "title": "移位单上架明细",
                "methodType": "get",
                "rule": "/in-warehouse/shift-order/shift-detail",
                "pid": "2806",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "377",
                "title": "移位单查询",
                "methodType": "get",
                "rule": "/in-warehouse/shift-order/list",
                "pid": "2806",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "380",
                "title": "移位单明细",
                "methodType": "get",
                "rule": "/in-warehouse/shift-order/view",
                "pid": "2806",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2807",
            "title": "采集管理",
            "methodType": "get",
            "rule": "wms/in/gather",
            "pid": "376",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "3998",
                "title": "采集操作",
                "methodType": "get",
                "rule": "/in-warehouse/collect-operation",
                "pid": "2807",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2335",
                "title": "采集任务管理",
                "methodType": "get",
                "rule": "/in-warehouse/collect-task/list",
                "pid": "2807",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2340",
                "title": "采集任务明细",
                "methodType": "get",
                "rule": "/in-warehouse/collect-task-detail/list",
                "pid": "2807",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2342",
                "title": "采集请求",
                "methodType": "get",
                "rule": "/in-warehouse/collection-request",
                "pid": "2807",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "395",
            "title": "盘点管理",
            "methodType": "get",
            "rule": "/order-management",
            "pid": "376",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "3828",
                "title": "动碰盘点",
                "methodType": "get",
                "rule": "/order-management/dynamic-location-query",
                "pid": "395",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "398",
                "title": "盘点单管理",
                "methodType": "post",
                "rule": "/order-management/order-list",
                "pid": "395",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "399",
                "title": "盘点任务",
                "methodType": "post",
                "rule": "/order-management/task",
                "pid": "395",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "396",
                "title": "任务分配",
                "methodType": "get",
                "rule": "/order-management/distribution",
                "pid": "395",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "397",
                "title": "盘点结果",
                "methodType": "post",
                "rule": "/order-management/check-result",
                "pid": "395",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5133",
                "title": "盘点作业",
                "methodType": "get",
                "rule": "/board/counting-monitoring",
                "pid": "395",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9116",
                "title": "任务分配(新)",
                "methodType": "get",
                "rule": "/order-managemen/distribution",
                "pid": "395",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "522",
            "title": "库存管理",
            "methodType": "get",
            "rule": "/stock-manage",
            "pid": "376",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2554",
                "title": "库存汇总查询",
                "methodType": "get",
                "rule": "/stock-manage/stock-query",
                "pid": "522",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2558",
                "title": "库存明细查询",
                "methodType": "get",
                "rule": "/stock-manage/stock-multiple-query",
                "pid": "522",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2485",
                "title": "库存变动记录",
                "methodType": "get",
                "rule": "/stock-manage/inventory-change",
                "pid": "522",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3230",
                "title": "批次库存查询",
                "methodType": "get",
                "rule": "/stock-manage/batch-of-stock-query",
                "pid": "522",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3231",
                "title": "批次库存变动记录",
                "methodType": "get",
                "rule": "/stock-manage/batch-of-stock-diff-query",
                "pid": "522",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8098",
                "title": "封仓管理",
                "methodType": "post",
                "rule": "/stock-manage/deal-with-warehouse",
                "pid": "522",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9323",
                "title": "SFS发票库存",
                "methodType": "get",
                "rule": "/stock-manage/SFS",
                "pid": "522",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": [
                  {
                    "id": "9316",
                    "title": "SFS发票库存查询",
                    "methodType": "get",
                    "rule": "/stock-manage/SFS/invoice-inventory",
                    "pid": "9323",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "9317",
                    "title": "SFS发票库存出库明细查询",
                    "methodType": "get",
                    "rule": "/stock-manage/SFS/invoice-inventory-out-record",
                    "pid": "9323",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "9318",
                    "title": "SFS发票库存变动查询",
                    "methodType": "get",
                    "rule": "/stock-manage/SFS/invoice-inventory-change",
                    "pid": "9323",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "9523",
                    "title": "SFS发票库存入库明细查询",
                    "methodType": "get",
                    "rule": "/stock-manage/SFS/invoice-inventory-in-record",
                    "pid": "9323",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  }
                ]
              },
              {
                "id": "10076",
                "title": "SFS月度报表汇总",
                "methodType": "get",
                "rule": "/stock-manage/SFS/monthly-statement-summary",
                "pid": "522",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "10077",
                "title": "SFS月度报表明细",
                "methodType": "get",
                "rule": "/stock-manage/SFS/monthly-report-details",
                "pid": "522",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "7361",
            "title": "缓存操作",
            "methodType": "post",
            "rule": "/wws/inner/wws_redis_op",
            "pid": "376",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "7373",
            "title": "测试接口",
            "methodType": "post",
            "rule": "/wws/front/test/test_Api_invoke",
            "pid": "376",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "2466",
            "title": "样衣管理",
            "methodType": "get",
            "rule": "/in-warehouse/sample-clothes-manage",
            "pid": "376",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2467",
                "title": "在库样衣",
                "methodType": "get",
                "rule": "/in-warehouse/sample-clothes-manage/in-warehouse-clothes",
                "pid": "2466",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2472",
                "title": "待借样衣",
                "methodType": "get",
                "rule": "/in-warehouse/sample-clothes-manage/will-borrow-clothes",
                "pid": "2466",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2475",
                "title": "已借样衣",
                "methodType": "get",
                "rule": "/in-warehouse/sample-clothes-manage/borrowed-clothes",
                "pid": "2466",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2477",
                "title": "已删样衣",
                "methodType": "get",
                "rule": "/in-warehouse/sample-clothes-manage/delete-clothes",
                "pid": "2466",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2810",
            "title": "异常管理",
            "methodType": "get",
            "rule": "wms/in/cancel",
            "pid": "376",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2380",
                "title": "取消返仓列表",
                "methodType": "get",
                "rule": "/in-warehouse/cancel-back-warehouse",
                "pid": "2810",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3018",
                "title": "异常库位处理任务",
                "methodType": "get",
                "rule": "/exception/exception-location-task",
                "pid": "2810",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "5960",
            "title": "黑码搜图",
            "methodType": "get",
            "rule": "/in-warehouse/black-code-search",
            "pid": "376",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "10045",
            "title": "白名单管理",
            "methodType": "get",
            "rule": "/in-warehouse/whitelist-manage",
            "pid": "376",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "10046",
                "title": "补货&理货白名单",
                "methodType": "get",
                "rule": "/in-warehouse/whitelist-manage/replenishment-and-inventory-whitelist",
                "pid": "10045",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "10410",
            "title": "理货管理",
            "methodType": "get",
            "rule": "/in-warehouse/tallying-manage",
            "pid": "376",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "10411",
                "title": "件型调整理货",
                "methodType": "get",
                "rule": "/in-warehouse/tallying-manage/piece-type-tally",
                "pid": "10410",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          }
        ]
      },
      {
        "id": "35",
        "title": "出库管理",
        "methodType": "get",
        "rule": "/outbound",
        "pid": "62159",
        "type": "1",
        "remark": "产品经理-张三 123456789000",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "43",
            "title": "包裹管理",
            "methodType": "get",
            "rule": "/outbound/package",
            "pid": "35",
            "type": "1",
            "remark": "tytttt",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "44",
            "title": "波次管理",
            "methodType": "get",
            "rule": "/outbound/wellen",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "45",
            "title": "批次管理",
            "methodType": "get",
            "rule": "/outbound/batch",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "46",
            "title": "拣货任务管理",
            "methodType": "get",
            "rule": "/outbound/task",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "132",
                "title": "任务查询",
                "methodType": "post",
                "rule": "/outbound/tasks/task",
                "pid": "46",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "133",
                "title": "拣货数据查询",
                "methodType": "post",
                "rule": "/outbound/tasks/pick-data",
                "pid": "46",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "134",
                "title": "拣货明细数据查询",
                "methodType": "post",
                "rule": "/outbound/tasks/pick-detail",
                "pid": "46",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3131",
                "title": "拣货库位明细查询",
                "methodType": "post",
                "rule": "/outbound/tasks/pick-warehouse-detail/list",
                "pid": "46",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "117",
            "title": "集货管理",
            "methodType": "post",
            "rule": "/outbound/gathers",
            "pid": "35",
            "type": "1",
            "remark": "测试内容-*********************************************",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "137",
                "title": "集货明细查询",
                "methodType": "post",
                "rule": "/outbound/gathers/gather-data",
                "pid": "117",
                "type": "1",
                "remark": "测试内容-*********************************************",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "138",
                "title": "子仓集货查询",
                "methodType": "post",
                "rule": "/outbound/gathers/gather-sub",
                "pid": "117",
                "type": "1",
                "remark": "测试内容-*********************************************",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "140",
                "title": "主仓集货查询",
                "methodType": "post",
                "rule": "/outbound/gathers/gather-master",
                "pid": "117",
                "type": "1",
                "remark": "测试内容-*********************************************",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2794",
                "title": "特殊出库集货查询",
                "methodType": "get",
                "rule": "/outbound/gathers/transfer-collection/list",
                "pid": "117",
                "type": "1",
                "remark": "测试内容-*********************************************",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4282",
                "title": "集货转运查询",
                "methodType": "get",
                "rule": "/outbound/gathers/transshipment-collection",
                "pid": "117",
                "type": "1",
                "remark": "测试内容-*********************************************",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5123",
                "title": "集货交接配置",
                "methodType": "get",
                "rule": "/outbound/gathers/pull-handover-config",
                "pid": "117",
                "type": "1",
                "remark": "测试内容-*********************************************",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5144",
                "title": "拉货交接管理",
                "methodType": "get",
                "rule": "/outbound/gathers/pull-handover-manage",
                "pid": "117",
                "type": "1",
                "remark": "测试内容-*********************************************",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6417",
                "title": "集货任务查询",
                "methodType": "get",
                "rule": "/outbound/gathers/gathers-task-search",
                "pid": "117",
                "type": "1",
                "remark": "测试内容-*********************************************",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6427",
                "title": "集货任务明细查询",
                "methodType": "get",
                "rule": "/outbound/gathers/gathers-taskdetail-search",
                "pid": "117",
                "type": "1",
                "remark": "测试内容-*********************************************",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "116",
            "title": "分播管理",
            "methodType": "post",
            "rule": "/outbound/transfer",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "135",
                "title": "一分查询",
                "methodType": "post",
                "rule": "/outbound/transfer/first",
                "pid": "116",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "136",
                "title": "二分查询",
                "methodType": "post",
                "rule": "/outbound/transfer/second",
                "pid": "116",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "255",
                "title": "分播明细查询",
                "methodType": "post",
                "rule": "/outbound/transfer/detail",
                "pid": "116",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4756",
                "title": "二分周转箱查询",
                "methodType": "get",
                "rule": "/outbound/second-head",
                "pid": "116",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "115",
            "title": "打包复核",
            "methodType": "post",
            "rule": "/outbound/packChecking",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "4791",
                "title": "精准出库扫描",
                "methodType": "get",
                "rule": "/outbound/packChecking/rechecking-refactor",
                "pid": "115",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "131",
                "title": "打印包裹明细",
                "methodType": "post",
                "rule": "/outbound/packChecking/printParcels",
                "pid": "115",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "293",
                "title": "打包明细",
                "methodType": "get",
                "rule": "/outbound/packChecking/package-detail",
                "pid": "115",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2512",
                "title": "重量差异包裹处理",
                "methodType": "get",
                "rule": "/outbound/packChecking/package-weight-difference/list",
                "pid": "115",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4508",
                "title": "面单前置出库扫描",
                "methodType": "post",
                "rule": "/outbound/packChecking/rechecking-new",
                "pid": "115",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4517",
                "title": "包裹装箱查询",
                "methodType": "get",
                "rule": "/outbound/packChecking/package-into-box",
                "pid": "115",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4521",
                "title": "打包记录管理",
                "methodType": "get",
                "rule": "/outbound/package-record",
                "pid": "115",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "536",
            "title": "下架明细",
            "methodType": "get",
            "rule": "/outbound/outShelvesDetail",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "3254",
            "title": "订单池管理",
            "methodType": "get",
            "rule": "/order-pool-management",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "3255",
                "title": "订单列表",
                "methodType": "get",
                "rule": "/outbound/order-package",
                "pid": "3254",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9118",
                "title": "订单列表(新)",
                "methodType": "get",
                "rule": "/outbound/order-package",
                "pid": "3254",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "5727",
            "title": "合单管理",
            "methodType": "get",
            "rule": "/outbound/combine-orders-manage",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "2804",
            "title": "异常管理",
            "methodType": "get",
            "rule": "wms/out/exception",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "273",
                "title": "补打标签（合包预包拆出子包裹标签在此打印）",
                "methodType": "get",
                "rule": "/outbound/patchLabel",
                "pid": "2804",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "118",
                "title": "包裹异常",
                "methodType": "post",
                "rule": "/exception/package",
                "pid": "2804",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "119",
                "title": "拣货异常",
                "methodType": "post",
                "rule": "/exception/picking",
                "pid": "2804",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4536",
                "title": "异常一分查询",
                "methodType": "get",
                "rule": "/exception/ex-first-search",
                "pid": "2804",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4537",
                "title": "异常二分查询",
                "methodType": "get",
                "rule": "/exception/ex-second-search",
                "pid": "2804",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4538",
                "title": "异常分播明细",
                "methodType": "get",
                "rule": "/exception/ex-sowing-detail",
                "pid": "2804",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "572",
            "title": "特殊出库管理",
            "methodType": "get",
            "rule": "/special-out",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "5745",
                "title": "撤仓装箱管理",
                "methodType": "get",
                "rule": "/special-out/recall-packing-query",
                "pid": "572",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "573",
                "title": "装箱扫描",
                "methodType": "get",
                "rule": "/special-out/box-scan",
                "pid": "572",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "579",
                "title": "出库单导入",
                "methodType": "get",
                "rule": "/special-out/order-import/list",
                "pid": "572",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "582",
                "title": "出库单管理",
                "methodType": "get",
                "rule": "/special-out/order-manage/list",
                "pid": "572",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "604",
                "title": "装箱查询",
                "methodType": "get",
                "rule": "/special-out/box-data-query",
                "pid": "572",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "607",
                "title": "装箱明细查询",
                "methodType": "get",
                "rule": "/special-out/box-detail-query",
                "pid": "572",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3809",
                "title": "挂牌打印",
                "methodType": "get",
                "rule": "/special-out/tag-print",
                "pid": "572",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3814",
                "title": "分播管理",
                "methodType": "get",
                "rule": "/special-out/second-sowe",
                "pid": "572",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": [
                  {
                    "id": "3815",
                    "title": "二分周转箱",
                    "methodType": "get",
                    "rule": "/special-out/second-sowe/list",
                    "pid": "3814",
                    "type": "1",
                    "remark": "产品经理-张三 123456789000",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "3818",
                    "title": "二分周转箱明细",
                    "methodType": "get",
                    "rule": "/special-out/second-sowe/detail",
                    "pid": "3814",
                    "type": "1",
                    "remark": "产品经理-张三 123456789000",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "3821",
                    "title": "二分操作记录",
                    "methodType": "get",
                    "rule": "/special-out/second-sowe/operation",
                    "pid": "3814",
                    "type": "1",
                    "remark": "产品经理-张三 123456789000",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "3824",
                    "title": "分播异常记录",
                    "methodType": "get",
                    "rule": "/special-out/second-sowe/exception",
                    "pid": "3814",
                    "type": "1",
                    "remark": "产品经理-张三 123456789000",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "4299",
                    "title": "特殊出库拣货查询",
                    "methodType": "get",
                    "rule": "/special-out/transfer-manage/first-search",
                    "pid": "3814",
                    "type": "1",
                    "remark": "产品经理-张三 123456789000",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "4301",
                    "title": "一分周转箱",
                    "methodType": "get",
                    "rule": "/special-out/transfer-manage/first-container",
                    "pid": "3814",
                    "type": "1",
                    "remark": "产品经理-张三 123456789000",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "4302",
                    "title": "一分周转箱明细",
                    "methodType": "get",
                    "rule": "/special-out/transfer-manage/first-container-detail",
                    "pid": "3814",
                    "type": "1",
                    "remark": "产品经理-张三 123456789000",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "4303",
                    "title": "一分操作记录",
                    "methodType": "get",
                    "rule": "/special-out/transfer-manage/first-record",
                    "pid": "3814",
                    "type": "1",
                    "remark": "产品经理-张三 123456789000",
                    "remarkPositionIdList": [],
                    "children": []
                  }
                ]
              },
              {
                "id": "4279",
                "title": "波次管理",
                "methodType": "get",
                "rule": "/special-out/wellen-manage",
                "pid": "572",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5094",
                "title": "拣货管理",
                "methodType": "get",
                "rule": "/special-out/picking",
                "pid": "572",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": [
                  {
                    "id": "5101",
                    "title": "拣货数据查询",
                    "methodType": "get",
                    "rule": "/special-out/tasks/pick-data",
                    "pid": "5094",
                    "type": "1",
                    "remark": "产品经理-张三 123456789000",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "5102",
                    "title": "拣货明细数据查询",
                    "methodType": "get",
                    "rule": "/special-out/tasks/pick-detail",
                    "pid": "5094",
                    "type": "1",
                    "remark": "产品经理-张三 123456789000",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "5103",
                    "title": "拣货库位明细查询",
                    "methodType": "get",
                    "rule": "/special-out/tasks/pick-warehouse-detail",
                    "pid": "5094",
                    "type": "1",
                    "remark": "产品经理-张三 123456789000",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "4120",
                    "title": "拣货任务查询",
                    "methodType": "get",
                    "rule": "/special-out/special/special-pick-task",
                    "pid": "5094",
                    "type": "1",
                    "remark": "产品经理-张三 123456789000",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "4123",
                    "title": "拣货任务明细查询",
                    "methodType": "get",
                    "rule": "/special-out/special/special-pick-task-detail",
                    "pid": "5094",
                    "type": "1",
                    "remark": "产品经理-张三 123456789000",
                    "remarkPositionIdList": [],
                    "children": []
                  }
                ]
              },
              {
                "id": "6041",
                "title": "特殊出库集货转运查询",
                "methodType": "get",
                "rule": "/special-out/sp-transshipment-collection",
                "pid": "572",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6044",
                "title": "特殊出库集货转运明细查询",
                "methodType": "get",
                "rule": "/special-out/sp-transshipment-collection-detail",
                "pid": "572",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8594",
                "title": "出库单配置",
                "methodType": "get",
                "rule": "/special-out/outbound-order-config",
                "pid": "572",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8598",
                "title": "海关编码目的区域配置",
                "methodType": "get",
                "rule": "/special-out/code-region-config",
                "pid": "572",
                "type": "1",
                "remark": "产品经理-张三 123456789000",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "3899",
            "title": "整箱出库管理",
            "methodType": "get",
            "rule": "/box-out/manage",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "3234",
                "title": "整箱出库单管理",
                "methodType": "get",
                "rule": "/special-out/overseas-out",
                "pid": "3899",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3900",
                "title": "整箱暂存管理",
                "methodType": "get",
                "rule": "/special-out/box-storage",
                "pid": "3899",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "4069",
            "title": "ABC智能波次查询（前期确认不用了，后面又用了，该页面确认不用期间可能页面代码有遗失）",
            "methodType": "get",
            "rule": "/outbound/abc-wellen-query",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "4070",
            "title": "ABC智能波次明细查询(OFC-20924智能波次明细功能移除)",
            "methodType": "get",
            "rule": "/outbound/abc-wellen-detail-query",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "4103",
            "title": "分播多货查询",
            "methodType": "get",
            "rule": "/outbound/sowing-more-goods-query",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "4527",
            "title": "打包记录管理",
            "methodType": "post",
            "rule": "/outbound/package-record",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "4932",
            "title": "出库质检查询",
            "methodType": "post",
            "rule": "/outbound/outbound-quality-check",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "4933",
            "title": "出库质检明细查询",
            "methodType": "post",
            "rule": "/outbound/outbound-quality-detail-check",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "7138",
            "title": "mq推送",
            "methodType": "post",
            "rule": "/wos/inner/send_mq_message",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "9535",
            "title": "波次生成监控",
            "methodType": "get",
            "rule": "/outbound/wellen-monitoring",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "9580",
            "title": "分波次暂存管理",
            "methodType": "get",
            "rule": "/outbound/wellen-storage-management",
            "pid": "35",
            "type": "1",
            "remark": "产品经理-张三 123456789000",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "491",
        "title": "调拨管理",
        "methodType": "get",
        "rule": "/transferBill-manage",
        "pid": "62159",
        "type": "1",
        "remark": "嵇素，胡歌",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "2980",
            "title": "调拨单管理",
            "methodType": "get",
            "rule": "/transfer-manage",
            "pid": "491",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [
              50012,
              195
            ],
            "children": [
              {
                "id": "492",
                "title": "调拨单",
                "methodType": "get",
                "rule": "/transferBill-manage/transferBill",
                "pid": "2980",
                "type": "1",
                "remark": "",
                "remarkPositionIdList": [
                  50012,
                  195
                ],
                "children": []
              },
              {
                "id": "501",
                "title": "调拨明细",
                "methodType": "get",
                "rule": "/transferBill-manage/transferBill-detail",
                "pid": "2980",
                "type": "1",
                "remark": "",
                "remarkPositionIdList": [
                  50012,
                  195
                ],
                "children": []
              },
              {
                "id": "549",
                "title": "调拨SKU明细",
                "methodType": "get",
                "rule": "/transferBill-manage/SKU-detail",
                "pid": "2980",
                "type": "1",
                "remark": "",
                "remarkPositionIdList": [
                  50012,
                  195
                ],
                "children": []
              },
              {
                "id": "4361",
                "title": "调拨单规则配置记录",
                "methodType": "get",
                "rule": "/transferBill-manage/transfer-order-rule-configuration",
                "pid": "2980",
                "type": "1",
                "remark": "",
                "remarkPositionIdList": [
                  50012,
                  195
                ],
                "children": []
              }
            ]
          },
          {
            "id": "505",
            "title": "收货质检管理",
            "methodType": "get",
            "rule": "/receive-goods-check",
            "pid": "491",
            "type": "1",
            "remark": "嵇素，胡歌",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "506",
                "title": "收货质检扫描",
                "methodType": "get",
                "rule": "/receive-goods-check/receive-qc-scan",
                "pid": "505",
                "type": "1",
                "remark": "嵇素，胡歌",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "518",
                "title": "收货质检单管理",
                "methodType": "get",
                "rule": "/receive-goods-check/receive-qc-manage",
                "pid": "505",
                "type": "1",
                "remark": "嵇素，胡歌",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "520",
                "title": "收货质检单明细管理",
                "methodType": "get",
                "rule": "/receive-goods-check/receive-qc-detail-manage",
                "pid": "505",
                "type": "1",
                "remark": "嵇素，胡歌",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "3801",
            "title": "退货调仓",
            "methodType": "get",
            "rule": "/transferBill-manage/goods-reject",
            "pid": "491",
            "type": "1",
            "remark": "嵇素，胡歌",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "3802",
                "title": "商品调仓列表",
                "methodType": "get",
                "rule": "/transferBill-manage/goods-reject/transfer-warehouse",
                "pid": "3801",
                "type": "1",
                "remark": "嵇素，胡歌",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3803",
                "title": "当地销售列表",
                "methodType": "get",
                "rule": "/transferBill-manage/goods-reject/outbound-offline",
                "pid": "3801",
                "type": "1",
                "remark": "嵇素，胡歌",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "7610",
            "title": "调拨暂存管理",
            "methodType": "get",
            "rule": "/transfer-save-manage",
            "pid": "491",
            "type": "1",
            "remark": "嵇素，胡歌",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "7611",
                "title": "整箱暂存列表",
                "methodType": "get",
                "rule": "/transfer-save-manage/box-storage",
                "pid": "7610",
                "type": "1",
                "remark": "嵇素，胡歌",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7612",
                "title": "下架任务管理",
                "methodType": "get",
                "rule": "/transfer-save-manage/takedown-task-management",
                "pid": "7610",
                "type": "1",
                "remark": "嵇素，胡歌",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7613",
                "title": "下架任务明细",
                "methodType": "get",
                "rule": "/transfer-save-manage/takedown-task-detail",
                "pid": "7610",
                "type": "1",
                "remark": "嵇素，胡歌",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8346",
                "title": "整箱暂存明细",
                "methodType": "get",
                "rule": "/transfer-save-manage/box-storage-detail",
                "pid": "7610",
                "type": "1",
                "remark": "嵇素，胡歌",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "8921",
            "title": "调拨收货绑托管理",
            "methodType": "get",
            "rule": "/transferBill-manage/transferBill-receipt",
            "pid": "491",
            "type": "1",
            "remark": "嵇素，胡歌",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "8922",
                "title": "调拨收货绑托查询",
                "methodType": "get",
                "rule": "/transferBill-manage/transferBill-receipt/transferBill-receipt-manage",
                "pid": "8921",
                "type": "1",
                "remark": "嵇素，胡歌",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8933",
                "title": "调拨收货绑托明细",
                "methodType": "get",
                "rule": "/transferBill-manage/transferBill-receipt/transferBill-receipt-detail",
                "pid": "8921",
                "type": "1",
                "remark": "嵇素，胡歌",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8936",
                "title": "叫货任务查询",
                "methodType": "get",
                "rule": "/transferBill-manage/transferBill-receipt/transferBill-task-manage",
                "pid": "8921",
                "type": "1",
                "remark": "嵇素，胡歌",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8940",
                "title": "叫货任务明细",
                "methodType": "get",
                "rule": "/transferBill-manage/transferBill-receipt/transferBill-task-detail",
                "pid": "8921",
                "type": "1",
                "remark": "嵇素，胡歌",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          }
        ]
      },
      {
        "id": "110",
        "title": "运营监控",
        "methodType": "post",
        "rule": "/board",
        "pid": "62159",
        "type": "1",
        "remark": "备注信息",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "661",
            "title": "实时看板",
            "methodType": "get",
            "rule": "/board/real-time-board",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "2811",
            "title": "入库看板",
            "methodType": "get",
            "rule": "wms/board/receive",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "3762",
                "title": "入库集货明细看板",
                "methodType": "get",
                "rule": "/qms/home/<USER>",
                "pid": "2811",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3759",
                "title": "入库集货看板",
                "methodType": "get",
                "rule": "/qms/home/<USER>",
                "pid": "2811",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3757",
                "title": "入库输送线查询",
                "methodType": "get",
                "rule": "/qms/home/<USER>",
                "pid": "2811",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "458",
                "title": "预约送货监控报表",
                "methodType": "get",
                "rule": "/reserve",
                "pid": "2811",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2377",
                "title": "收货入库上架报表",
                "methodType": "get",
                "rule": "/board/receipt-report",
                "pid": "2811",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3996",
                "title": "入库周转箱监控",
                "methodType": "get",
                "rule": "/board/entry-warehouse-monitor",
                "pid": "2811",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6347",
                "title": "入库作业看板",
                "methodType": "get",
                "rule": "/board/inbound-board",
                "pid": "2811",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7882",
                "title": "入库流程库存状态监控",
                "methodType": "get",
                "rule": "/board/qc-inventory-status-monitoring",
                "pid": "2811",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2812",
            "title": "库内看板",
            "methodType": "get",
            "rule": "wms/board/instorge",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "7886",
                "title": "上架/暂存监控看板",
                "methodType": "get",
                "rule": "/board/putaway-temporary-save",
                "pid": "2812",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4259",
                "title": "上架监控看板",
                "methodType": "get",
                "rule": "/basic/putaway-strategy/putaway-recommend-monitoring",
                "pid": "2812",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4245",
                "title": "库位状态看板",
                "methodType": "get",
                "rule": "/basic/putaway-strategy/location-status",
                "pid": "2812",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2347",
                "title": "采集任务报表",
                "methodType": "get",
                "rule": "/board/collection-mission-board",
                "pid": "2812",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3065",
                "title": "库存汇总表",
                "methodType": "get",
                "rule": "/board/inventory-variance-summary/list",
                "pid": "2812",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3128",
                "title": "库存变动记录汇总查询",
                "methodType": "get",
                "rule": "/board/inventory-change-record/list",
                "pid": "2812",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3994",
                "title": "库内周转箱监控",
                "methodType": "get",
                "rule": "/board/in-warehouse-monitor",
                "pid": "2812",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6933",
                "title": "积压监控明细",
                "methodType": "get",
                "rule": "/basic/putaway-strategy/over-stock-detail",
                "pid": "2812",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6939",
                "title": "积压监控看板",
                "methodType": "get",
                "rule": "/basic/putaway-strategy/over-stock-monitoring",
                "pid": "2812",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2813",
            "title": "出库看板",
            "methodType": "get",
            "rule": "wms/board/out",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "4599",
                "title": "包裹跟进",
                "methodType": "get",
                "rule": "/board/package-follow",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4753",
                "title": "打包作业看板",
                "methodType": "get",
                "rule": "/board/packing-board",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "120",
                "title": "主仓集货看板",
                "methodType": "post",
                "rule": "/board/mainStorehouse",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "121",
                "title": "子仓集货看板",
                "methodType": "post",
                "rule": "/board/childStorehouse",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "122",
                "title": "分播台看板",
                "methodType": "post",
                "rule": "/board/sowingStand",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "287",
                "title": "出库作业看板",
                "methodType": "post",
                "rule": "/home/<USER>",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "288",
                "title": "出库个人产能看板",
                "methodType": "post",
                "rule": "/home/<USER>",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "291",
                "title": "包裹看板",
                "methodType": "post",
                "rule": "/basic/broad",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3844",
                "title": "拣货超时看板",
                "methodType": "get",
                "rule": "/board/timeout-pick-board",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3869",
                "title": "出库积压实时监控",
                "methodType": "get",
                "rule": "/board/outstock-monitoring",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3977",
                "title": "出库周转箱监控",
                "methodType": "get",
                "rule": "/board/out-warehouse-monitor",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4704",
                "title": "出库集货作业看板",
                "methodType": "get",
                "rule": "/board/collection-monitor",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4718",
                "title": "出库波次平衡看板",
                "methodType": "get",
                "rule": "/board/wellen-balance-monitoring",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4746",
                "title": "出库一分作业看板",
                "methodType": "get",
                "rule": "/board/first-board",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4748",
                "title": "出库二分作业看板",
                "methodType": "get",
                "rule": "/board/second-board",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4750",
                "title": "出库拣货超时看板",
                "methodType": "get",
                "rule": "/board/pick-timeout-board",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5719",
                "title": "新老智能波次看板",
                "methodType": "get",
                "rule": "/basic/abc-plus",
                "pid": "2813",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2814",
            "title": "全流程监控看板",
            "methodType": "get",
            "rule": "wms/board/bigsale",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "3211",
                "title": "全流程主看板",
                "methodType": "get",
                "rule": "/board/main-board",
                "pid": "2814",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3212",
                "title": "合包业务看板",
                "methodType": "get",
                "rule": "/board/combination",
                "pid": "2814",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3260",
                "title": "出库看板",
                "methodType": "get",
                "rule": "/board/outbound-board",
                "pid": "2814",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3262",
                "title": "在库看板",
                "methodType": "get",
                "rule": "/board/in-warehouse-board",
                "pid": "2814",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4755",
                "title": "黑五狂欢购物节",
                "methodType": "get",
                "rule": "/board/main-board-2020",
                "pid": "2814",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5671",
                "title": "库存环节全流程看板",
                "methodType": "get",
                "rule": "/board/stock-full-process",
                "pid": "2814",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6782",
                "title": "产能环节看板",
                "methodType": "get",
                "rule": "/board/capacity-link-statistics",
                "pid": "2814",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8394",
                "title": "库存看板",
                "methodType": "get",
                "rule": "/board/inventory-board",
                "pid": "2814",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9657",
                "title": "全球网络看板",
                "methodType": "get",
                "rule": "/board/global-network-monitoring",
                "pid": "2814",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2815",
            "title": "导入导出",
            "methodType": "get",
            "rule": "wms/board/download",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "2351",
            "title": "斋节看板",
            "methodType": "get",
            "rule": "/home/<USER>",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "2944",
            "title": "海外仓库存监控",
            "methodType": "get",
            "rule": "/board/invoicing/list",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "2972",
            "title": "转运暂存位监控看板",
            "methodType": "get",
            "rule": "/board/temporary-storage/receipt-list",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "2974",
            "title": "待上架暂存监控看板",
            "methodType": "get",
            "rule": "/board/temporary-storage/pending-list-new",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "3068",
            "title": "人力资源看板",
            "methodType": "get",
            "rule": "/board/human-resources-manage",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "5698",
            "title": "实时看板菜单",
            "methodType": "get",
            "rule": "/a",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "7772",
            "title": "RFID工具管控",
            "methodType": "get",
            "rule": "/board/rfid-tools-control",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "7888",
            "title": "PV/UV监控看板",
            "methodType": "get",
            "rule": "/board/uvpv-monitoring",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "7965",
            "title": "设备看板",
            "methodType": "get",
            "rule": "/wms/board/equipment",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "8860",
            "title": "Flowpicking监控看板",
            "methodType": "get",
            "rule": "/fp-monitor-board",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "8861",
                "title": "波次汇总看板",
                "methodType": "get",
                "rule": "/board/fp-monitor-board/wave-summary-monitor",
                "pid": "8860",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8862",
                "title": "库区任务看板",
                "methodType": "get",
                "rule": "/board/fp-monitor-board/location-task-monitor",
                "pid": "8860",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "9800",
            "title": "运营简报",
            "methodType": "get",
            "rule": "/subscription/daily-report",
            "pid": "110",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "9500",
                "title": "仓库运营简报海外新页面",
                "methodType": "get",
                "rule": "/subscription/daily-report/foreignNew",
                "pid": "9800",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9512",
                "title": "仓库运营简报海外旧页面",
                "methodType": "get",
                "rule": "/subscription/daily-report/old",
                "pid": "9800",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          }
        ]
      },
      {
        "id": "2442",
        "title": "系统配置",
        "methodType": "get",
        "rule": "/wms/sysconfig",
        "pid": "62159",
        "type": "1",
        "remark": "备注信息",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "2816",
            "title": "全局配置",
            "methodType": "get",
            "rule": "wms/sysconfig/global",
            "pid": "2442",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "4246",
                "title": "上架推荐配置",
                "methodType": "get",
                "rule": "/basic/putaway-strategy",
                "pid": "2816",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": [
                  {
                    "id": "4247",
                    "title": "上架推荐配置",
                    "methodType": "get",
                    "rule": "/basic/putaway-strategy/putaway-recommend-config",
                    "pid": "4246",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "4256",
                    "title": "推荐规则",
                    "methodType": "get",
                    "rule": "/basic/putaway-strategy/putaway-recommend-rule",
                    "pid": "4246",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  }
                ]
              },
              {
                "id": "9660",
                "title": "全球网络配置",
                "methodType": "post",
                "rule": "/basic/global-network-config",
                "pid": "2816",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4022",
                "title": "超期配置",
                "methodType": "post",
                "rule": "/basic/overdue-threshold-configuration",
                "pid": "2816",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4697",
                "title": "pda 新手操作指引管理",
                "methodType": "post",
                "rule": "/basic/operational-guideline-pda",
                "pid": "2816",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5352",
                "title": "上架运营规则",
                "methodType": "get",
                "rule": "/basic/shelf-rules-configuration",
                "pid": "2816",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5677",
                "title": "环节能效配置",
                "methodType": "get",
                "rule": "/basic/link-performance",
                "pid": "2816",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8987",
                "title": "常用系统配置",
                "methodType": "get",
                "rule": "/basic/common-system-settings",
                "pid": "2816",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8995",
                "title": "轮播图片配置",
                "methodType": "get",
                "rule": "/basic/homepage-gallery-config",
                "pid": "2816",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2817",
            "title": "入库配置",
            "methodType": "get",
            "rule": "wms/sysconfig/in",
            "pid": "2442",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "9756",
                "title": "子仓参数管理",
                "methodType": "get",
                "rule": "/basic/inbound-configuration/subwarehouse-params-management",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3772",
                "title": "入库分拣口维护",
                "methodType": "get",
                "rule": "/basic/in-storage-maintenance",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6384",
                "title": "分拣口配置",
                "methodType": "get",
                "rule": "/basic/in-storage-configuration",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6391",
                "title": "库区入库规则配置",
                "methodType": "get",
                "rule": "/basic/inbound-configuration/warehouse-entry-rules",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6518",
                "title": "分仓送货管理",
                "methodType": "get",
                "rule": "/basic/inbound-configuration/delivery-management",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7806",
                "title": "TC分仓配置",
                "methodType": "get",
                "rule": "/sysconfig/inbound/tc-warehouse-configuration",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9301",
                "title": "商品收货规则配置",
                "methodType": "get",
                "rule": "/basic/goods-receipt-restriction-rules",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2134",
                "title": "数据字典",
                "methodType": "get",
                "rule": "/sysconfig/inbound/data-dictionary",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2202",
                "title": "系统配置",
                "methodType": "get",
                "rule": "/sysconfig/inbound/sys-manage",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2212",
                "title": "货位管理",
                "methodType": "get",
                "rule": "/sysconfig/inbound/cargo-space",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4504",
                "title": "退货收包国家线配置",
                "methodType": "post",
                "rule": "/basic/configuration-national-line",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4658",
                "title": "供应商送货子仓信息",
                "methodType": "get",
                "rule": "/basic/supplier-sub-warehouse",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5335",
                "title": "次品/黑码箱品类配置",
                "methodType": "get",
                "rule": "/basic/ib-type-config",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5682",
                "title": "部门子仓关系配置",
                "methodType": "get",
                "rule": "/basic/department-substore-relationship",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6054",
                "title": "交接转运配置",
                "methodType": "get",
                "rule": "/basic/handover-and-transfer-configuration",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6789",
                "title": "入库权限授权",
                "methodType": "get",
                "rule": "/sysconfig/inbound/auth-center",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7149",
                "title": "快慢流推荐及消耗",
                "methodType": "get",
                "rule": "/sysconfig/inbound/slow-flow-recommendation",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7151",
                "title": "入库分拣口管理",
                "methodType": "get",
                "rule": "/sysconfig/inbound/sorting-port-management",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7157",
                "title": "入库分拣区",
                "methodType": "get",
                "rule": "/sysconfig/inbound/sorting-area-management",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7574",
                "title": "转运时效配置",
                "methodType": "get",
                "rule": "/sysconfig/inbound/transport-effective-config",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7575",
                "title": "转运调度策略配置",
                "methodType": "get",
                "rule": "/sysconfig/inbound/transport-schedule-strategy",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7768",
                "title": "站仓运营规则",
                "methodType": "get",
                "rule": "/sysconfig/inbound/operation-rules",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7815",
                "title": "存储属性满箱件数维护",
                "methodType": "get",
                "rule": "/sysconfig/inbound/storage-attributes-box",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7834",
                "title": "上架/暂存推荐规则",
                "methodType": "get",
                "rule": "/basic/staging-recommendation-rules",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8151",
                "title": "退供运营规则",
                "methodType": "get",
                "rule": "/sysconfig/inbound/retreatment-rules",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8352",
                "title": "上架规则配置",
                "methodType": "post",
                "rule": "/basic/putaway-rules-config",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8817",
                "title": "其他入库权限授权",
                "methodType": "post",
                "rule": "/sysconfig/inbound/other-entry-authorization",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9153",
                "title": "运单号截取规则配置",
                "methodType": "get",
                "rule": "/qms/management-sys/transportation-number-intercept-rules",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9278",
                "title": "抽检重量差异配置",
                "methodType": "get",
                "rule": "/basic/inbound-configuration/weight-discrepancy-allocation",
                "pid": "2817",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2818",
            "title": "出库配置",
            "methodType": "get",
            "rule": "/wms/sysconfig/out",
            "pid": "2442",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "3796",
                "title": "包裹重量差对应关系表",
                "methodType": "get",
                "rule": "/basic/package-weight-relation",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5525",
                "title": "拉取规则管理",
                "methodType": "get",
                "rule": "/basic/intelligence-wellen-pull-rule",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5526",
                "title": "拉取策略管理",
                "methodType": "get",
                "rule": "/basic/intelligence-wellen-pull-strategy",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6380",
                "title": "出库输送线管理",
                "methodType": "get",
                "rule": "/outbound/conveyor-management",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": [
                  {
                    "id": "6381",
                    "title": "出库输送线分拣查询",
                    "methodType": "get",
                    "rule": "/outbound/conveyor-management/sorting",
                    "pid": "6380",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  }
                ]
              },
              {
                "id": "3841",
                "title": "拣货任务超时配置",
                "methodType": "get",
                "rule": "/basic/stock-out-config/pick_task_overtime_config",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3915",
                "title": "出库环节超时企微推送配置",
                "methodType": "get",
                "rule": "/basic/stock-out-config/wechat-push-config",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4029",
                "title": "集货配置",
                "methodType": "get",
                "rule": "/basic/gather-config",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4062",
                "title": "智能波次配置",
                "methodType": "get",
                "rule": "/basic/intelligence-wellen-config",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": [
                  {
                    "id": "8533",
                    "title": "智能波次组波策略日程管理",
                    "methodType": "get",
                    "rule": "/basic/intelligence-wellen-pull-strategy-schedule",
                    "pid": "4062",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "4764",
                    "title": "智能波次标记策略",
                    "methodType": "get",
                    "rule": "/basic/intelligence-wellen-mark-strategy",
                    "pid": "4062",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "4768",
                    "title": "智能波次标记规则",
                    "methodType": "get",
                    "rule": "/basic/intelligence-wellen-mark-rule",
                    "pid": "4062",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "4771",
                    "title": "智能波次组波策略",
                    "methodType": "get",
                    "rule": "/basic/intelligence-wellen-pull-strategy",
                    "pid": "4062",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "4775",
                    "title": "智能波次组波规则",
                    "methodType": "get",
                    "rule": "/basic/intelligence-wellen-pull-rule",
                    "pid": "4062",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "5283",
                    "title": "命中规则管理",
                    "methodType": "get",
                    "rule": "/basic/intelligence-wellen-hit-rule",
                    "pid": "4062",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "5284",
                    "title": "命中策略管理",
                    "methodType": "get",
                    "rule": "/basic/intelligence-wellen-hit-strategy",
                    "pid": "4062",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "5323",
                    "title": "作业平衡管理",
                    "methodType": "post",
                    "rule": "/board/intelligence-wellen-balance-monitor",
                    "pid": "4062",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "5329",
                    "title": "智能波次拉取策略（停用）",
                    "methodType": "get",
                    "rule": "/basic/intelligence-wellen-pull-strategy-old",
                    "pid": "4062",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "5330",
                    "title": "智能波次拉取规则（停用）",
                    "methodType": "get",
                    "rule": "/basic/intelligence-wellen-pull-rule-old",
                    "pid": "4062",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  }
                ]
              },
              {
                "id": "4075",
                "title": "拣货任务商品数库区配置",
                "methodType": "get",
                "rule": "/basic/area-pick-task-config",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4513",
                "title": "包裹面单打印前置配置",
                "methodType": "get",
                "rule": "/basic/package-sheet-print",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4637",
                "title": "包裹标识管理",
                "methodType": "post",
                "rule": "/basic/parcel-identification-management",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4723",
                "title": "集货转运配置",
                "methodType": "get",
                "rule": "/sysconfig/outbound/transshipment-configuration",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4798",
                "title": "物料配置",
                "methodType": "get",
                "rule": "/sysconfig/outbound/material-configuration/view",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5116",
                "title": "产能管理",
                "methodType": "get",
                "rule": "/basic/production-capacity-management",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": [
                  {
                    "id": "5310",
                    "title": "产能管理",
                    "methodType": "post",
                    "rule": "/basic/production-capacity-management",
                    "pid": "5116",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "6470",
                    "title": "目的港限量管理",
                    "methodType": "get",
                    "rule": "/basic/destination-port-management",
                    "pid": "5116",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  }
                ]
              },
              {
                "id": "5203",
                "title": "合包配置",
                "methodType": "get",
                "rule": "/basic/merge-management",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5327",
                "title": "出库作业配置",
                "methodType": "get",
                "rule": "/basic/outbound-work-config",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": [
                  {
                    "id": "4778",
                    "title": "出库作业配置",
                    "methodType": "get",
                    "rule": "/basic/outbound-work-configuration",
                    "pid": "5327",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "5141",
                    "title": "作业方式配置",
                    "methodType": "get",
                    "rule": "/basic/weight-diff-config",
                    "pid": "5327",
                    "type": "1",
                    "remark": "备注信息",
                    "remarkPositionIdList": [],
                    "children": []
                  }
                ]
              },
              {
                "id": "5944",
                "title": "特殊出库转运配置",
                "methodType": "get",
                "rule": "/basic/transfer-config",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7117",
                "title": "库存预占区域配置",
                "methodType": "post",
                "rule": "/basic/stock-occupy-config",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7118",
                "title": "包裹国家线配置",
                "methodType": "get",
                "rule": "/basic/package-national-line-config",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7284",
                "title": "拣货任务优先级配置",
                "methodType": "get",
                "rule": "/basic/pick-task-priority",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7453",
                "title": "组波规则配置",
                "methodType": "get",
                "rule": "/basic/outbound-wave-group-rule",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7737",
                "title": "特殊出库预占配置",
                "methodType": "post",
                "rule": "/basic/special-stock-occupy-config",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7789",
                "title": "库存预占区域配置(新)",
                "methodType": "get",
                "rule": "/basic/stock-occupy-config-new",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7848",
                "title": "多任务配置",
                "methodType": "get",
                "rule": "/basic/mult-task-config",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8386",
                "title": "分播配置",
                "methodType": "get",
                "rule": "/basic/sowing-config",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8699",
                "title": "独立品牌耗材推荐规则",
                "methodType": "get",
                "rule": "/basic/independent-brand-consumables",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8851",
                "title": "海外平台集运仓履时效配置",
                "methodType": "get",
                "rule": "/basic/oversea-time-config",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9513",
                "title": "渠道组管理配置",
                "methodType": "get",
                "rule": "/basic/outbound-config/channel-group-configuration",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9514",
                "title": "渠道管理（新）",
                "methodType": "get",
                "rule": "/basic/outbound-config/channel-management",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9884",
                "title": "flow picking拣货任务均衡下发",
                "methodType": "post",
                "rule": "/basic/fp-balance-distribute",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9962",
                "title": "提前拣选策略配置",
                "methodType": "get",
                "rule": "/outbound/early-pick-config",
                "pid": "2818",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2819",
            "title": "库内配置",
            "methodType": "get",
            "rule": "wms/sysconfig/location",
            "pid": "2442",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "4330",
                "title": "补货策略",
                "methodType": "get",
                "rule": "/basic/replenishment-strategy",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5129",
                "title": "盘点配置",
                "methodType": "get",
                "rule": "/basic/check-config",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7142",
                "title": "库内授权",
                "methodType": "get",
                "rule": "/basic/in-warehouse-auth",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8864",
                "title": "计价作业仓配置",
                "methodType": "get",
                "rule": "/basic/valuation-operation-warehouse",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3008",
                "title": "库位异常原因配置",
                "methodType": "get",
                "rule": "/basic/location-exception-config",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3159",
                "title": "销售等级",
                "methodType": "get",
                "rule": "/basic/sales-grade-type",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3894",
                "title": "交接流程配置",
                "methodType": "get",
                "rule": "/basic/handover-process-configuration",
                "pid": "2819",
                "type": "1",
                "remark": "产品经理-Jayer Xu",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4154",
                "title": "安灯配置",
                "methodType": "get",
                "rule": "/basic/andon-configuration",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4737",
                "title": "任务优先级",
                "methodType": "get",
                "rule": "/basic/task-priority",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5687",
                "title": "仓库库容规划配置",
                "methodType": "get",
                "rule": "/sysconfig/inbound/capacity-planning",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6026",
                "title": "存储等级规划",
                "methodType": "get",
                "rule": "/management-sys/rolls-configuration/storage-level-planning",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6406",
                "title": "空箱回流策略配置",
                "methodType": "get",
                "rule": "/basic/box-back-strategy-management",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6533",
                "title": "返仓混装配置",
                "methodType": "get",
                "rule": "/basic/shift-mixed-config",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6561",
                "title": "库龄等级配置",
                "methodType": "get",
                "rule": "/basic/goods-age-grade",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6603",
                "title": "存储等级库存配置",
                "methodType": "get",
                "rule": "/basic/goods-store-grade",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8198",
                "title": "盘点参数配置",
                "methodType": "post",
                "rule": "/basic/order-config",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8211",
                "title": "外部系统配置",
                "methodType": "get",
                "rule": "/basic/external-system-param-config",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8707",
                "title": "存储标签",
                "methodType": "get",
                "rule": "/management-sys/storage-label",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9569",
                "title": "海外销量等级配置",
                "methodType": "get",
                "rule": "/basic/overseas-sales-level-config",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9570",
                "title": "海外库龄配置",
                "methodType": "get",
                "rule": "/basic/overseas-inventory-age-config",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "10167",
                "title": "规划产能维护",
                "methodType": "get",
                "rule": "/in-warehouse/capacity-planning",
                "pid": "2819",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "62351",
                "title": "库存变动监控配置",
                "methodType": "get",
                "rule": "/basic/inventory-change-monitor-config",
                "pid": "2819",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2820",
            "title": "调拨配置",
            "methodType": "get",
            "rule": "wms/sysconfig/transer",
            "pid": "2442",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "683",
                "title": "调拨规则表",
                "methodType": "get",
                "rule": "/basic/allocation-rule",
                "pid": "2820",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4357",
                "title": "调拨规则",
                "methodType": "get",
                "rule": "/basic/transfer-rule-table",
                "pid": "2820",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5316",
                "title": "调拨规则表2",
                "methodType": "post",
                "rule": "/basic/allocation-rule2",
                "pid": "2820",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8577",
                "title": "分箱查询",
                "methodType": "get",
                "rule": "/basic/subcontainer-query",
                "pid": "2820",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8578",
                "title": "分箱明细",
                "methodType": "get",
                "rule": "/basic/subcontainer-detail-query",
                "pid": "2820",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "4293",
            "title": "看板配置",
            "methodType": "get",
            "rule": "/board",
            "pid": "2442",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "4294",
                "title": "主看板配置",
                "methodType": "post",
                "rule": "/basic/main-kanban-configuration",
                "pid": "4293",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4714",
                "title": "作业环节超时看板",
                "methodType": "post",
                "rule": "/basic/operate-timeout-config",
                "pid": "4293",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5318",
                "title": "国家线排序配置",
                "methodType": "post",
                "rule": "/basic/national-line-monitor-configuration",
                "pid": "4293",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6924",
                "title": "积压监控配置",
                "methodType": "get",
                "rule": "/basic/over-stock-config",
                "pid": "4293",
                "type": "1",
                "remark": "备注信息",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "6274",
            "title": "设备配置",
            "methodType": "post",
            "rule": "/device-config",
            "pid": "2442",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "8343",
            "title": "打印模板管理",
            "methodType": "post",
            "rule": "/basic/ejs-manage",
            "pid": "2442",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "9957",
            "title": "配置管理",
            "methodType": "get",
            "rule": "/basic/config-management",
            "pid": "2442",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "1",
        "title": "后台管理",
        "methodType": "get",
        "rule": "/management-sys",
        "pid": "62159",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "34",
            "title": "基础数据",
            "methodType": "get",
            "rule": "/basic",
            "pid": "1",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "359",
                "title": "商品数据",
                "methodType": "post",
                "rule": "/basic/product-info",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2363",
                "title": "销量预测",
                "methodType": "get",
                "rule": "/basic/sales-predict/list",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2390",
                "title": "商品批次关系对应表",
                "methodType": "get",
                "rule": "/basic/goods-batch-relationship",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2430",
                "title": "退货说明书与宣传单",
                "methodType": "get",
                "rule": "/basic/return-instruction-and-leaflet",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3039",
                "title": "商品存储属性",
                "methodType": "get",
                "rule": "/basic/goods-storage-attributes",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3832",
                "title": "品类与存储属性关系",
                "methodType": "get",
                "rule": "/basic/category-storage",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3880",
                "title": "包裹取消原因",
                "methodType": "get",
                "rule": "/basic/package-cancel-reason",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3970",
                "title": "周转箱数据查询",
                "methodType": "get",
                "rule": "/basic/turnover-container",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3986",
                "title": "周转箱监控看板配置",
                "methodType": "get",
                "rule": "/basic/container-board-config",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4183",
                "title": "国家线管理",
                "methodType": "get",
                "rule": "/basic/national-line",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6038",
                "title": "商品存储等级",
                "methodType": "get",
                "rule": "/basic/product-stock-lvl",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7134",
                "title": "商品快慢流",
                "methodType": "get",
                "rule": "/basic/product-flow-type",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7188",
                "title": "库位周转箱关系管理",
                "methodType": "get",
                "rule": "/basic/goods-location-container-map",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7448",
                "title": "商品白名单",
                "methodType": "get",
                "rule": "/basic/goods-allow-list",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7596",
                "title": "面料溯源历史skc",
                "methodType": "get",
                "rule": "/basic/turnover-handover-record",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8742",
                "title": "格口信息查询",
                "methodType": "get",
                "rule": "/basic/grid-message-search",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9934",
                "title": "市场国家线管理",
                "methodType": "get",
                "rule": "/basic/market-national-line",
                "pid": "34",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "4509",
            "title": "推送消息服务",
            "methodType": "get",
            "rule": "/basic/message-publish",
            "pid": "1",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "5712",
            "title": "公告列表",
            "methodType": "post",
            "rule": "/management-sys/public-list",
            "pid": "1",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "6541",
            "title": "基础功能",
            "methodType": "get",
            "rule": "/basic-functions",
            "pid": "1",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "6542",
                "title": "条码打印",
                "methodType": "get",
                "rule": "/basic-functions/barcode-print",
                "pid": "6541",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6544",
                "title": "包裹打印",
                "methodType": "get",
                "rule": "/basic-functions/package-print",
                "pid": "6541",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "8364",
            "title": "审批单",
            "methodType": "get",
            "rule": "/basic/approve",
            "pid": "1",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "8365",
                "title": "审批单管理",
                "methodType": "get",
                "rule": "/basic/approve-manage",
                "pid": "8364",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8366",
                "title": "审批流配置",
                "methodType": "get",
                "rule": "/basic/approval-flow-config",
                "pid": "8364",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "8544",
            "title": "特殊审核人配置",
            "methodType": "get",
            "rule": "/basic/special-reviewer-configuration-of-the-work-order",
            "pid": "1",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "8641",
        "title": "工单管理",
        "methodType": "get",
        "rule": "/work-order-management",
        "pid": "62159",
        "type": "1",
        "remark": "备注信息",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "8609",
            "title": "工单处理",
            "methodType": "get",
            "rule": "/work-order-management/work-order-deal",
            "pid": "8641",
            "type": "1",
            "remark": "工单处理",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "8612",
                "title": "工单工作台",
                "methodType": "get",
                "rule": "/work-order-management/work-order-deal/work-order-platform",
                "pid": "8609",
                "type": "1",
                "remark": "工单工作台",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8611",
                "title": "工单列表",
                "methodType": "get",
                "rule": "/work-order-management/work-order-deal/work-order-list",
                "pid": "8609",
                "type": "1",
                "remark": "产品经理-Sean Zheng",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8610",
                "title": "创建工单",
                "methodType": "get",
                "rule": "/work-order-management/work-order-deal/work-order-create",
                "pid": "8609",
                "type": "1",
                "remark": "工单处理",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "8554",
            "title": "工单基础配置",
            "methodType": "get",
            "rule": "/work-order-management/work-order-basic-config",
            "pid": "8641",
            "type": "1",
            "remark": "产品经理-Alan Lai",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "8602",
                "title": "工单自动生成激励单配置",
                "methodType": "get",
                "rule": "/work-order-management/work-order-basic-config/auto-tickets-config",
                "pid": "8554",
                "type": "1",
                "remark": "产品经理-Alan Lai",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8539",
                "title": "工单特殊审核人配置",
                "methodType": "get",
                "rule": "/work-order-management/work-order-basic-config/special-approver-config",
                "pid": "8554",
                "type": "1",
                "remark": "产品经理-Alan Lai",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8555",
                "title": "工单类型",
                "methodType": "get",
                "rule": "/work-order-management/work-order-basic-config/work-order-type",
                "pid": "8554",
                "type": "1",
                "remark": "产品经理-Alan Lai",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8558",
                "title": "工单流程配置",
                "methodType": "get",
                "rule": "/work-order-management/work-order-basic-config/work-order-process/list",
                "pid": "8554",
                "type": "1",
                "remark": "产品经理-Alan Lai",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9328",
                "title": "工单自动触发配置",
                "methodType": "get",
                "rule": "/work-order-management/work-order-basic-config/auto-trigger-config",
                "pid": "8554",
                "type": "1",
                "remark": "产品经理-Alan Lai",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          }
        ]
      },
      {
        "id": "9559",
        "title": "库内开发者工具",
        "methodType": "get",
        "rule": "/in-warehouse-developer",
        "pid": "62159",
        "type": "1",
        "remark": "备注信息",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "9560",
            "title": "库内开发者工具",
            "methodType": "get",
            "rule": "/in-warehouse-developer/developer-tools",
            "pid": "9559",
            "type": "1",
            "remark": "备注信息",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "10091",
        "title": "分播管理",
        "methodType": "get",
        "rule": "/transfer-management",
        "pid": "62159",
        "type": "1",
        "remark": "备注信息",
        "remarkPositionIdList": [],
        "children": []
      }
    ]
  },
  {
    "id": "50001",
    "title": "后台管理",
    "methodType": "get",
    "rule": "/management",
    "pid": "0",
    "type": "1",
    "remark": "",
    "remarkPositionIdList": [],
    "children": [
      {
        "id": "50034",
        "title": "基础数据",
        "methodType": "get",
        "rule": "/management/basic-data",
        "pid": "50001",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "50039",
            "title": "仓库管理",
            "methodType": "get",
            "rule": "/management/basic-data/warehouse",
            "pid": "50034",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "50040",
            "title": "子仓管理",
            "methodType": "get",
            "rule": "/management/basic-data/sub-warehouse",
            "pid": "50034",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "50041",
            "title": "库区管理",
            "methodType": "get",
            "rule": "/management/basic-data/area",
            "pid": "50034",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61743",
            "title": "巷道管理",
            "methodType": "get",
            "rule": "/management/basic-data/roadway",
            "pid": "50034",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "50042",
            "title": "库位管理",
            "methodType": "get",
            "rule": "/management/basic-data/goods",
            "pid": "50034",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "50112",
            "title": "容器管理",
            "methodType": "get",
            "rule": "/management/basic-data/container",
            "pid": "50034",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "50113",
            "title": "集货/暂存位管理",
            "methodType": "post",
            "rule": "/management/basic-data/goods-gather",
            "pid": "50034",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "59257",
            "title": "工位管理",
            "methodType": "post",
            "rule": "/management/basic-data/work-location-manage",
            "pid": "50034",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61981",
            "title": "企微旺旺参数管理",
            "methodType": "get",
            "rule": "/management/basic-data/wecom-arguments-config",
            "pid": "50034",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62061",
            "title": "格口管理",
            "methodType": "get",
            "rule": "/management/basic-data/grid-management",
            "pid": "50034",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62313",
            "title": "城市列表",
            "methodType": "get",
            "rule": "/management/basic-data/city-list",
            "pid": "50034",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "61881",
        "title": "导入/导出管理",
        "methodType": "get",
        "rule": "/management/import-export-mgt",
        "pid": "50001",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "50555",
            "title": "导出下载",
            "methodType": "get",
            "rule": "/management/import-export-mgt/download",
            "pid": "61881",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "60490",
            "title": "导入管理",
            "methodType": "get",
            "rule": "/management/import-export-mgt/import-center",
            "pid": "61881",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "62033",
        "title": "公告管理",
        "methodType": "get",
        "rule": "/management/public-management/announcement-management",
        "pid": "50001",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": []
      },
      {
        "id": "62039",
        "title": "公告列表",
        "methodType": "get",
        "rule": "/management/public-management/publish-list",
        "pid": "50001",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": []
      }
    ]
  },
  {
    "id": "59358",
    "title": "人员管理",
    "methodType": "get",
    "rule": "/personnel-mgt",
    "pid": "0",
    "type": "1",
    "remark": "",
    "remarkPositionIdList": [],
    "children": [
      {
        "id": "2821",
        "title": "用户管理",
        "methodType": "get",
        "rule": "wms/system/user",
        "pid": "59358",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "50527",
            "title": "用户",
            "methodType": "get",
            "rule": "/personnel-mgt/user-manage",
            "pid": "2821",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "562",
            "title": "用户组管理",
            "methodType": "get",
            "rule": "/management-sys/usergroups-manage",
            "pid": "2821",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "3079",
            "title": "作业人员分组",
            "methodType": "get",
            "rule": "/management-sys/operatorgroups-manage",
            "pid": "2821",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "4340",
            "title": "部门角色关系",
            "methodType": "get",
            "rule": "/management-sys/dept-role-config",
            "pid": "2821",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "10082",
            "title": "预排班排产结果",
            "methodType": "get",
            "rule": "/management-sys/member-predict-result",
            "pid": "2821",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "10097",
            "title": "预排班人员明细",
            "methodType": "get",
            "rule": "/management-sys/member-predict-result-detail",
            "pid": "2821",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "10140",
            "title": "货量及效率预测",
            "methodType": "get",
            "rule": "/basic/use-manager/cargo-volume-efficiency-forecast",
            "pid": "2821",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "50004",
            "title": "角色分组管理",
            "methodType": "get",
            "rule": "/personnel-mgt/role-manage/list",
            "pid": "2821",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "3078",
        "title": "作业调度",
        "methodType": "get",
        "rule": "wms/in/scheduling-task",
        "pid": "59358",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "3097",
            "title": "调度任务集管理",
            "methodType": "get",
            "rule": "/person-manage/scheduling-tasks-manage",
            "pid": "3078",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "3106",
            "title": "调度任务管理",
            "methodType": "get",
            "rule": "/person-manage/scheduling-tasks-manage/detail",
            "pid": "3078",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "3111",
            "title": "组员安排管理",
            "methodType": "get",
            "rule": "/person-manage/group-time-management",
            "pid": "3078",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "4285",
            "title": "罚单管理",
            "methodType": "get",
            "rule": "/person-manage/punish-order-manage",
            "pid": "3078",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "5280",
            "title": "排班修改管理",
            "methodType": "get",
            "rule": "/person-manage/schedule-modify-manage",
            "pid": "3078",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "4696",
        "title": "配置管理",
        "methodType": "get",
        "rule": "/human-mgt/config",
        "pid": "59358",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "3777",
            "title": "仓库作业岗位",
            "methodType": "get",
            "rule": "/basic/warehouse-operation-post",
            "pid": "4696",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "4706",
            "title": "登录时间限制",
            "methodType": "get",
            "rule": "/basic/member-time-limit",
            "pid": "4696",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "7470",
            "title": "末级部门相关配置",
            "methodType": "get",
            "rule": "/person-manage/scheduling-tasks-configuration",
            "pid": "4696",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "9900",
            "title": "S&OP货量",
            "methodType": "get",
            "rule": "/basic/use-manager/s-op-goods",
            "pid": "4696",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "9958",
            "title": "排班排产规则配置",
            "methodType": "get",
            "rule": "/basic/config-management/schedule-rule-config",
            "pid": "4696",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "10071",
            "title": "货量及效率配置",
            "methodType": "post",
            "rule": "/basic/use-manager/cargo-volume-efficiency-forecast",
            "pid": "4696",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      }
    ]
  },
  {
    "id": "52816",
    "title": "全局配置",
    "methodType": "get",
    "rule": "/system/global-cfg",
    "pid": "0",
    "type": "1",
    "remark": "",
    "remarkPositionIdList": [],
    "children": [
      {
        "id": "50036",
        "title": "参数管理",
        "methodType": "get",
        "rule": "/system/global-cfg/config",
        "pid": "52816",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": []
      },
      {
        "id": "50038",
        "title": "数据字典管理",
        "methodType": "get",
        "rule": "/system/global-cfg/data-dictionary",
        "pid": "52816",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": []
      },
      {
        "id": "50003",
        "title": "权限节点管理",
        "methodType": "get",
        "rule": "/system/global-cfg/permission",
        "pid": "52816",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": []
      },
      {
        "id": "62406",
        "title": "UV/PV监控看板",
        "methodType": "get",
        "rule": "/system/global-cfg/uvpv-monitoring",
        "pid": "52816",
        "type": "1",
        "remark": "产品经理-Alex Chen",
        "remarkPositionIdList": [],
        "children": []
      }
    ]
  },
  {
    "id": "62139",
    "title": "自动化管理",
    "methodType": "get",
    "rule": "/auto-manage",
    "pid": "0",
    "type": "1",
    "remark": "",
    "remarkPositionIdList": [],
    "children": [
      {
        "id": "62076",
        "title": "设备配置",
        "methodType": "get",
        "rule": "/system/device-cfg",
        "pid": "62139",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "62077",
            "title": "自动化设备配置",
            "methodType": "get",
            "rule": "/system/device-cfg/auto-device-config",
            "pid": "62076",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62078",
            "title": "自动化设备接口地址配置",
            "methodType": "get",
            "rule": "/system/device-cfg/auto-device-address-config",
            "pid": "62076",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "62060",
        "title": "格口渠道/库区配置",
        "methodType": "get",
        "rule": "/system/outbound-cfg/grid-location/area-config",
        "pid": "62139",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": []
      },
      {
        "id": "62068",
        "title": "格口容器关系管理",
        "methodType": "post",
        "rule": "/management/basic-data/grid-container-management",
        "pid": "62139",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": []
      },
      {
        "id": "62058",
        "title": "分拣任务信息查询",
        "methodType": "post",
        "rule": "/outbound-mgt/sorting-task-information-query",
        "pid": "62139",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": []
      }
    ]
  },
  {
    "id": "5245",
    "title": "WeChat",
    "methodType": "get",
    "rule": "/home-page",
    "pid": "0",
    "type": "1",
    "remark": "",
    "remarkPositionIdList": [],
    "children": [
      {
        "id": "5246",
        "title": "上架人员管理",
        "methodType": "get",
        "rule": "/team-management",
        "pid": "5245",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "5248",
            "title": "组员排班",
            "methodType": "get",
            "rule": "/team-management/crew-schedule",
            "pid": "5246",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "5249",
            "title": "拣货管理",
            "methodType": "get",
            "rule": "/team-management/picking-management",
            "pid": "5246",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "5250",
            "title": "小组管理",
            "methodType": "get",
            "rule": "/team-management/substitute-management",
            "pid": "5246",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "8024",
                "title": "小组管理",
                "methodType": "post",
                "rule": "/team-management/inbound-schedule",
                "pid": "5250",
                "type": "1",
                "remark": "",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "5251",
            "title": "小组任务",
            "methodType": "get",
            "rule": "/team-management/group-task",
            "pid": "5246",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "5252",
            "title": "组员点名",
            "methodType": "get",
            "rule": "/team-management/team-call",
            "pid": "5246",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "5331",
            "title": "拉货交接管理",
            "methodType": "get",
            "rule": "/team-management/handover-management",
            "pid": "5246",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "6443",
            "title": "组员安排管理",
            "methodType": "get",
            "rule": "/team-management/plan",
            "pid": "5246",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "6493",
            "title": "组员安排排班测试",
            "methodType": "get",
            "rule": "/team-management/plan-test",
            "pid": "5246",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "7723",
            "title": "入库排班",
            "methodType": "get",
            "rule": "/team-management/inbound-schedule",
            "pid": "5246",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "10178",
            "title": "小组排班看板",
            "methodType": "get",
            "rule": "/team-management/plan-board",
            "pid": "5246",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "10179",
            "title": "个人排班看板",
            "methodType": "get",
            "rule": "/team-management/plan-board/1",
            "pid": "5246",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "5968",
        "title": "业务应用",
        "methodType": "get",
        "rule": "/business-application",
        "pid": "5245",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "7355",
            "title": "通行标识",
            "methodType": "get",
            "rule": "business-application/access-signs",
            "pid": "5968",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "5969",
            "title": "以图搜图",
            "methodType": "get",
            "rule": "/business-application/black-code-search",
            "pid": "5968",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "6252",
        "title": "奖惩管理",
        "methodType": "get",
        "rule": "/rewards-and-punishment",
        "pid": "5245",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "6253",
            "title": "录单管理",
            "methodType": "get",
            "rule": "/rewards-and-punishment/make-bill-manage",
            "pid": "6252",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "6254",
            "title": "审单管理",
            "methodType": "get",
            "rule": "/rewards-and-punishment/check-bill-manage",
            "pid": "6252",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "8052",
            "title": "单据汇总",
            "methodType": "get",
            "rule": "/subscription/bills-summary",
            "pid": "6252",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "8377",
        "title": "审批单管理",
        "methodType": "get",
        "rule": "/approval-list-manage",
        "pid": "5245",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "8379",
            "title": "我的申请",
            "methodType": "get",
            "rule": "/approval-list-manage/my-approval-and-application/2",
            "pid": "8377",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "8378",
            "title": "我的审批",
            "methodType": "get",
            "rule": "/approval-list-manage/my-approval-and-application/1",
            "pid": "8377",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "8573",
        "title": "工单管理",
        "methodType": "get",
        "rule": "/work-order-management",
        "pid": "5245",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "8576",
            "title": "我的已办",
            "methodType": "get",
            "rule": "/work-order-management/my-done",
            "pid": "8573",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "8575",
            "title": "我的待办",
            "methodType": "get",
            "rule": "/work-order-management/my-todo",
            "pid": "8573",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "8574",
            "title": "创建工单",
            "methodType": "get",
            "rule": "/work-order-management/create-work-order",
            "pid": "8573",
            "type": "1",
            "remark": "",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      }
    ]
  },
  {
    "id": "6768",
    "title": "其他",
    "methodType": "get",
    "rule": "/test01",
    "pid": "0",
    "type": "2",
    "remark": "",
    "remarkPositionIdList": [],
    "children": [
      {
        "id": "6867",
        "title": "权限测试02",
        "methodType": "get",
        "rule": "222",
        "pid": "6768",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": []
      },
      {
        "id": "9938",
        "title": "1122443",
        "methodType": "get",
        "rule": "1",
        "pid": "6768",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": []
      },
      {
        "id": "111",
        "title": "PDA",
        "methodType": "post",
        "rule": "/pda/main-menu",
        "pid": "6768",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "437",
            "title": "交接扫描-old",
            "methodType": "get",
            "rule": "/pda/scan",
            "pid": "111",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "438",
                "title": "装托扫描",
                "methodType": "get",
                "rule": "/pda/scan/load",
                "pid": "437",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "443",
                "title": "发货扫描",
                "methodType": "get",
                "rule": "/pda/scan/send",
                "pid": "437",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "447",
                "title": "收货交接",
                "methodType": "get",
                "rule": "/pda/scan/receive",
                "pid": "437",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "477",
            "title": "PDA调拨出库",
            "methodType": "get",
            "rule": "/pda/allot-out",
            "pid": "111",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "478",
                "title": "退货调拨出库",
                "methodType": "get",
                "rule": "/pda/allot-out/sales-return",
                "pid": "477",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "483",
                "title": "取消调拨出库",
                "methodType": "get",
                "rule": "/pda/allot-out/cancel-out",
                "pid": "477",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "485",
                "title": "PDA收货管理",
                "methodType": "get",
                "rule": "/pda/allot-out/receiving",
                "pid": "477",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "488",
                "title": "PDA暂存扫描",
                "methodType": "get",
                "rule": "/pda/allot-out/temp-scan",
                "pid": "477",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "539",
            "title": "返仓",
            "methodType": "get",
            "rule": "/pda/back-warehouse",
            "pid": "111",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "540",
                "title": "返仓装箱",
                "methodType": "get",
                "rule": "/pda/back-warehouse",
                "pid": "539",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "596",
            "title": "特殊出库收货",
            "methodType": "get",
            "rule": "/pda/receive-goods",
            "pid": "111",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "597",
                "title": "特殊出库收货",
                "methodType": "get",
                "rule": "/pda/special-receive-goods",
                "pid": "596",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "654",
            "title": "库内管理",
            "methodType": "get",
            "rule": "/pda/inbound-manage",
            "pid": "111",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2330",
                "title": "执行采集任务",
                "methodType": "get",
                "rule": "/pda/inbound-manage/gather-task",
                "pid": "654",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2373",
                "title": "按箱移货",
                "methodType": "get",
                "rule": "/pda/inbound-manage/the-box-move-goods",
                "pid": "654",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "674",
            "title": "特殊入库收货",
            "methodType": "get",
            "rule": "/pda/standard-receive-goods",
            "pid": "111",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "675",
                "title": "明细收货",
                "methodType": "get",
                "rule": "/pda/standard-receive-goods/standard-receive-detail",
                "pid": "674",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "676",
                "title": "按箱收货",
                "methodType": "get",
                "rule": "/pda/standard-receive-goods/standard-box-receive",
                "pid": "674",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "677",
                "title": "入库整单收货",
                "methodType": "get",
                "rule": "/pda/standard-receive-goods/standard-whole-bill-receive",
                "pid": "674",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2038",
            "title": "收货入库",
            "methodType": "get",
            "rule": "/qms/pda/refund-scan",
            "pid": "111",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "6769",
        "title": "权限测试01-01",
        "methodType": "post",
        "rule": "/test01/test01",
        "pid": "6768",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": []
      },
      {
        "id": "7077",
        "title": "权限测试01-05",
        "methodType": "get",
        "rule": "/test01/test05",
        "pid": "6768",
        "type": "1",
        "remark": "asfd",
        "remarkPositionIdList": [],
        "children": []
      },
      {
        "id": "7109",
        "title": "补推mq",
        "methodType": "post",
        "rule": "/wms/inner/push_compensate_message",
        "pid": "6768",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": []
      },
      {
        "id": "7111",
        "title": "wpoc补推msgDeal",
        "methodType": "post",
        "rule": "/wpoc/inner/msg_deal_re_work",
        "pid": "6768",
        "type": "1",
        "remark": "",
        "remarkPositionIdList": [],
        "children": []
      }
    ]
  },
  {
    "id": "61669",
    "title": "NEW-PDA",
    "methodType": "get",
    "rule": "/new-pda/main-menu",
    "pid": "0",
    "type": "1",
    "remark": "产品经理-Roy Zhou",
    "remarkPositionIdList": [],
    "children": [
      {
        "id": "3295",
        "title": "商品管理系统-入库管理",
        "methodType": "get",
        "rule": "/new-pda/goods-in-manager",
        "pid": "61669",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "2526",
            "title": "上架",
            "methodType": "post",
            "rule": "/new-pda/put-shelves",
            "pid": "3295",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2724",
                "title": "领取上架任务",
                "methodType": "get",
                "rule": "/new-pda/put-shelves/putaway-task-new",
                "pid": "2526",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2548",
                "title": "入库上架",
                "methodType": "get",
                "rule": "/new-pda/put-shelves/put-away",
                "pid": "2526",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2646",
                "title": "移位上架",
                "methodType": "get",
                "rule": "/new-pda/put-shelves/shift-up",
                "pid": "2526",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": [
                  {
                    "id": "3935",
                    "title": "移位-上架历史",
                    "methodType": "post",
                    "rule": "/wms/front/shift_upper/history",
                    "pid": "2646",
                    "type": "1",
                    "remark": "产品经理-Roy Zhou",
                    "remarkPositionIdList": [],
                    "children": []
                  }
                ]
              },
              {
                "id": "2688",
                "title": "质检上架",
                "methodType": "get",
                "rule": "/new-pda/put-shelves/quality-shelves",
                "pid": "2526",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2563",
                "title": "异常上架",
                "methodType": "post",
                "rule": "/new-pda/put-shelves/put-error",
                "pid": "2526",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2693",
                "title": "整箱上架",
                "methodType": "get",
                "rule": "/new-pda/put-shelves/whole-box-shelves",
                "pid": "2526",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2702",
                "title": "批量上架",
                "methodType": "get",
                "rule": "/new-pda/put-shelves/batch-shelves",
                "pid": "2526",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2653",
                "title": "整托上架",
                "methodType": "get",
                "rule": "/new-pda/put-shelves/shift-all",
                "pid": "2526",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2708",
            "title": "特殊入库收货",
            "methodType": "get",
            "rule": "/new-pda/standard-receive-goods",
            "pid": "3295",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "9152",
                "title": "退货收包(新版)",
                "methodType": "get",
                "rule": "/new-pda/standard-receive-goods/new-return-package-scan",
                "pid": "2708",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2709",
                "title": "明细收货",
                "methodType": "get",
                "rule": "/new-pda/standard-receive-goods/standard-receive-detail",
                "pid": "2708",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2713",
                "title": "按箱收货",
                "methodType": "get",
                "rule": "/new-pda/standard-receive-goods/standard-box-receive",
                "pid": "2708",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2716",
                "title": "整单收货",
                "methodType": "get",
                "rule": "/new-pda/standard-receive-goods/standard-whole-bill-receive",
                "pid": "2708",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2903",
                "title": "退货收包",
                "methodType": "get",
                "rule": "/new-pda/standard-receive-goods/return-package-scan",
                "pid": "2708",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2912",
                "title": "多货分箱",
                "methodType": "get",
                "rule": "/new-pda/inbound-manage/points-for-goods",
                "pid": "2708",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2921",
                "title": "分报关批次",
                "methodType": "get",
                "rule": "/new-pda/inbound-manage/batch",
                "pid": "2708",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4276",
                "title": "拒收包裹退货收包",
                "methodType": "get",
                "rule": "/new-pda/standard-receive-goods/return-package-scan-new",
                "pid": "2708",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2738",
            "title": "收货入库",
            "methodType": "get",
            "rule": "/new-pda/refund-scan",
            "pid": "3295",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "3769",
                "title": "品控集货",
                "methodType": "get",
                "rule": "/refund-scan/transfer-line-of-instock-qc",
                "pid": "2738",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3768",
                "title": "入库分拣查询",
                "methodType": "get",
                "rule": "/refund-scan/transfer-line-of-instock-query",
                "pid": "2738",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6908",
                "title": "入库异常",
                "methodType": "get",
                "rule": "refund-scan/abnormal",
                "pid": "2738",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2759",
                "title": "点数装箱",
                "methodType": "get",
                "rule": "/new-pda/refund-scan/point-binning",
                "pid": "2738",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2760",
                "title": "采购入库收货",
                "methodType": "get",
                "rule": "/new-pda/refund-scan/pda-receipt",
                "pid": "2738",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5358",
                "title": "新点数装箱",
                "methodType": "get",
                "rule": "/new-pda/refund-scan/new-point-binning",
                "pid": "2738",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": [
                  {
                    "id": "5641",
                    "title": "历史装箱信息",
                    "methodType": "get",
                    "rule": "/new-pda/refund-scan/packing-history",
                    "pid": "5358",
                    "type": "1",
                    "remark": "产品经理-Roy Zhou",
                    "remarkPositionIdList": [],
                    "children": []
                  }
                ]
              },
              {
                "id": "6822",
                "title": "领取拉货任务",
                "methodType": "post",
                "rule": "/new-pda/refund-scan/receive-drawing-task",
                "pid": "2738",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6828",
                "title": "运单称重",
                "methodType": "post",
                "rule": "/new-pda/refund-scan/waybill-weigh",
                "pid": "2738",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6831",
                "title": "收货",
                "methodType": "post",
                "rule": "/new-pda/refund-scan/goods-receipt",
                "pid": "2738",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6836",
                "title": "暂存",
                "methodType": "post",
                "rule": "/new-pda/refund-scan/temporary-storage",
                "pid": "2738",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7092",
                "title": "领取拉黄框任务",
                "methodType": "get",
                "rule": "/new-pda/refund-scan/receive-drawing-task/2",
                "pid": "2738",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7093",
                "title": "领取拉灰框任务",
                "methodType": "get",
                "rule": "/new-pda/refund-scan/receive-drawing-task/3",
                "pid": "2738",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "3745",
            "title": "退供",
            "methodType": "get",
            "rule": "/new-pda/return",
            "pid": "3295",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2755",
                "title": "接收次品包裹",
                "methodType": "get",
                "rule": "/new-pda/refund-scan/inferior-receipt",
                "pid": "3745",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2756",
                "title": "次品包裹上架",
                "methodType": "get",
                "rule": "/new-pda/refund-scan/scan-putaway",
                "pid": "3745",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2757",
                "title": "次品包裹下架",
                "methodType": "get",
                "rule": "/new-pda/refund-scan/sold-out",
                "pid": "3745",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2758",
                "title": "退供扫描",
                "methodType": "get",
                "rule": "/new-pda/refund-scan/refund-apply-scan",
                "pid": "3745",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3274",
                "title": "退供复核点数",
                "methodType": "get",
                "rule": "/refund-scan/return-packing-scan",
                "pid": "3745",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "4870",
            "title": "次品退供",
            "methodType": "get",
            "rule": "/new-pda/return-new",
            "pid": "3295",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "6620",
                "title": "物流下单",
                "methodType": "get",
                "rule": "/new-pda/return/logistics-order",
                "pid": "4870",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8396",
                "title": "PT退供拣货下架",
                "methodType": "get",
                "rule": "/new-pda/return/return-picking-off",
                "pid": "4870",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4871",
                "title": "接收退供交接箱",
                "methodType": "get",
                "rule": "/new-pda/return/return-receive",
                "pid": "4870",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4872",
                "title": "退供上架",
                "methodType": "get",
                "rule": "/new-pda/return/return-up",
                "pid": "4870",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4873",
                "title": "退供下架",
                "methodType": "get",
                "rule": "/new-pda/return/return-down",
                "pid": "4870",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4874",
                "title": "退供暂存",
                "methodType": "get",
                "rule": "/new-pda/return/return-store",
                "pid": "4870",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4875",
                "title": "退供发货",
                "methodType": "get",
                "rule": "/new-pda/return/return-delivery",
                "pid": "4870",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7313",
                "title": "采购退货下架重构",
                "methodType": "get",
                "rule": "/new-pda/return/purchase-return-down",
                "pid": "4870",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2761",
                "title": "多货退供装箱",
                "methodType": "get",
                "rule": "/new-pda/refund-scan/return-binning",
                "pid": "4870",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4096",
                "title": "次品数据查询",
                "methodType": "get",
                "rule": "/new-pda/refund-scan/inferior-product-query",
                "pid": "4870",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7932",
                "title": "退供暂存查询",
                "methodType": "get",
                "rule": "/new-pda/return/return-store-query",
                "pid": "4870",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "6911",
            "title": "入库异常",
            "methodType": "get",
            "rule": "/new-pda/refund-scan/abnormal",
            "pid": "3295",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "6912",
                "title": "异常包裹暂存",
                "methodType": "get",
                "rule": "/new-pda/refund-scan/abnormal/temporary-storage",
                "pid": "6911",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6915",
                "title": "异常包裹发货",
                "methodType": "get",
                "rule": "/new-pda/refund-scan/abnormal/handle-requisition",
                "pid": "6911",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6919",
                "title": "入库异常查询",
                "methodType": "get",
                "rule": "/new-pda/refund-scan/abnormal/deal-with-requisition",
                "pid": "6911",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "7053",
            "title": "PDA-次品报废",
            "methodType": "get",
            "rule": "/new-pda/defective-scrap",
            "pid": "3295",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "7055",
                "title": "报废下架",
                "methodType": "get",
                "rule": "/new-pda/defective-scrap/discard-shelves",
                "pid": "7053",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7054",
                "title": "报废装箱",
                "methodType": "get",
                "rule": "/new-pda/defective-scrap/scrap-packing",
                "pid": "7053",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8327",
                "title": "第二层",
                "methodType": "get",
                "rule": "/link",
                "pid": "7053",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": [
                  {
                    "id": "8328",
                    "title": "第三层1",
                    "methodType": "get",
                    "rule": "/defective-scrap/discard-shelves",
                    "pid": "8327",
                    "type": "1",
                    "remark": "产品经理-Roy Zhou",
                    "remarkPositionIdList": [],
                    "children": []
                  },
                  {
                    "id": "8329",
                    "title": "第三层2",
                    "methodType": "get",
                    "rule": "/defective-scrap/scrap-packing",
                    "pid": "8327",
                    "type": "1",
                    "remark": "产品经理-Roy Zhou",
                    "remarkPositionIdList": [],
                    "children": []
                  }
                ]
              }
            ]
          },
          {
            "id": "62240",
            "title": "交接扫描",
            "methodType": "get",
            "rule": "/new-pda/scan-new",
            "pid": "3295",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "3296",
        "title": "商品管理系统-库内管理",
        "methodType": "get",
        "rule": "/new-pda/goods-inbound-manager",
        "pid": "61669",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "2527",
            "title": "库内查询",
            "methodType": "post",
            "rule": "/new-pda/query",
            "pid": "3296",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2540",
                "title": "库位库存查询",
                "methodType": "get",
                "rule": "/new-pda/query/location-stock",
                "pid": "2527",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2542",
                "title": "商品库存查询",
                "methodType": "get",
                "rule": "/new-pda/query/goods-stock",
                "pid": "2527",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2608",
                "title": "短拣查询",
                "methodType": "post",
                "rule": "/new-pda/query/short-pick",
                "pid": "2527",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2613",
                "title": "容器号查询",
                "methodType": "get",
                "rule": "/new-pda/query/container-query",
                "pid": "2527",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3051",
                "title": "库位信息查询维护",
                "methodType": "get",
                "rule": "/new-pda/inbound-manage/location-info-manage",
                "pid": "2527",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3287",
                "title": "库存变动轨迹查询",
                "methodType": "get",
                "rule": "/new-pda/query/inventory-flow-status",
                "pid": "2527",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8217",
                "title": "容器号查询（新）",
                "methodType": "get",
                "rule": "/new-pda/query/container-query-new",
                "pid": "2527",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2562",
            "title": "盘点",
            "methodType": "post",
            "rule": "/new-pda/take-account",
            "pid": "3296",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "5166",
                "title": "低水位盘点",
                "methodType": "get",
                "rule": "/new-pda/take-account/take/15",
                "pid": "2562",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2629",
                "title": "循环盘点",
                "methodType": "get",
                "rule": "/new-pda/take-account/take/1",
                "pid": "2562",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2636",
                "title": "实时盘点",
                "methodType": "get",
                "rule": "/new-pda/take-account/take/2",
                "pid": "2562",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2637",
                "title": "短拣盘点",
                "methodType": "get",
                "rule": "/new-pda/take-account/take/3",
                "pid": "2562",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2638",
                "title": "二次盘点",
                "methodType": "get",
                "rule": "/new-pda/take-account/take/4",
                "pid": "2562",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3827",
                "title": "动碰盘点",
                "methodType": "get",
                "rule": "/new-pda/take-account/take/12",
                "pid": "2562",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7500",
                "title": "效期盘点",
                "methodType": "get",
                "rule": "/new-pda/take-account/take/16",
                "pid": "2562",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2874",
            "title": "返仓",
            "methodType": "get",
            "rule": "/new-pda/back-warehouse",
            "pid": "3296",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2875",
                "title": "返仓装箱",
                "methodType": "get",
                "rule": "/new-pda/back-warehouse/binning",
                "pid": "2874",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6538",
                "title": "新返仓装箱",
                "methodType": "get",
                "rule": "/new-pda/back-warehouse/new-binning",
                "pid": "2874",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "3746",
            "title": "换箱操作",
            "methodType": "get",
            "rule": "/new-pda/change-box",
            "pid": "3296",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2682",
                "title": "换箱管理(整箱)",
                "methodType": "get",
                "rule": "/new-pda/inbound-manage/change-whole-box-manage",
                "pid": "3746",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2685",
                "title": "换箱管理(散件)",
                "methodType": "get",
                "rule": "/new-pda/inbound-manage/change-part-box-manage",
                "pid": "3746",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2936",
                "title": "大箱换上架周转箱",
                "methodType": "get",
                "rule": "/new-pda/inbound-manage/exchange-container/1",
                "pid": "3746",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2937",
                "title": "上架周转箱换大箱",
                "methodType": "get",
                "rule": "/new-pda/inbound-manage/exchange-container/2",
                "pid": "3746",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2938",
                "title": "大箱换大箱",
                "methodType": "get",
                "rule": "/new-pda/inbound-manage/exchange-container/3",
                "pid": "3746",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "3747",
            "title": "装箱",
            "methodType": "get",
            "rule": "/new-pda/put_in_box",
            "pid": "3296",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "3218",
                "title": "扫商品装箱",
                "methodType": "get",
                "rule": "/new-pda/inbound-manage/binning-by-goods",
                "pid": "3747",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3219",
                "title": "扫包裹装箱",
                "methodType": "get",
                "rule": "/new-pda/inbound-manage/binning-by-package",
                "pid": "3747",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5348",
                "title": "非特殊入库黑码装箱",
                "methodType": "post",
                "rule": "/new-pda/inbound-manage/blackcode-by-package",
                "pid": "3747",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "3748",
            "title": "商品信息采集",
            "methodType": "get",
            "rule": "/new-pda/goods_collect",
            "pid": "3296",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2672",
                "title": "商品信息采集",
                "methodType": "get",
                "rule": "/new-pda/put-shelves/goods-info-collection",
                "pid": "3748",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2719",
                "title": "执行采集任务",
                "methodType": "get",
                "rule": "/new-pda/inbound-manage/gather-task",
                "pid": "3748",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "9170",
            "title": "换箱操作",
            "methodType": "get",
            "rule": "/inbound-manage/exchange-container-test",
            "pid": "3296",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "9166",
                "title": "换箱管理",
                "methodType": "post",
                "rule": "/inbound-manage/exchange-container-new",
                "pid": "9170",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          }
        ]
      },
      {
        "id": "3297",
        "title": "商品管理系统-出库管理",
        "methodType": "get",
        "rule": "/new-pda/goods-out-manager",
        "pid": "61669",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "2560",
            "title": "拣货",
            "methodType": "post",
            "rule": "/new-pda/order-picking",
            "pid": "3297",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "5758",
                "title": "撤仓下架",
                "methodType": "get",
                "rule": "/order-picking/recall-shift-down",
                "pid": "2560",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2567",
                "title": "正常拣货",
                "methodType": "get",
                "rule": "/new-pda/order-picking/picking/get-task/1",
                "pid": "2560",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2639",
                "title": "补货下架",
                "methodType": "get",
                "rule": "/new-pda/order-picking/replenish-down",
                "pid": "2560",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2675",
                "title": "其他出库",
                "methodType": "get",
                "rule": "/new-pda/order-picking/other-outbound",
                "pid": "2560",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2729",
                "title": "移位下架",
                "methodType": "get",
                "rule": "/new-pda/order-picking/shift-down",
                "pid": "2560",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3148",
                "title": "回货下架",
                "methodType": "get",
                "rule": "/new-pda/order-picking/back-down",
                "pid": "2560",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3871",
                "title": "冻结移位",
                "methodType": "get",
                "rule": "/new-pda/order-picking/freeze",
                "pid": "2560",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4692",
                "title": "批量下架",
                "methodType": "get",
                "rule": "/order-picking/batch-down",
                "pid": "2560",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7381",
                "title": "效期冻结下架",
                "methodType": "get",
                "rule": "/new-pda/order-picking/shelf-life-frozen",
                "pid": "2560",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7846",
                "title": "多任务拣货",
                "methodType": "get",
                "rule": "/new-pda/order-picking/multi-task-picking",
                "pid": "2560",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2561",
            "title": "分播",
            "methodType": "post",
            "rule": "/new-pda/sowing",
            "pid": "3297",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "4616",
                "title": "批次二分架绑定",
                "methodType": "get",
                "rule": "/new-pda/put-shelves/batch-two-shelf-bind",
                "pid": "2561",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2581",
                "title": "一分",
                "methodType": "post",
                "rule": "/new-pda/sowing/first",
                "pid": "2561",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2593",
                "title": "二分",
                "methodType": "post",
                "rule": "/new-pda/sowing/second",
                "pid": "2561",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2611",
                "title": "一分扫描",
                "methodType": "get",
                "rule": "/new-pda/sowing/first-scan",
                "pid": "2561",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2612",
                "title": "二分扫描",
                "methodType": "get",
                "rule": "/new-pda/sowing/second-scan",
                "pid": "2561",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4300",
                "title": "特殊出库一分",
                "methodType": "get",
                "rule": "/new-pda/sowing/special-first",
                "pid": "2561",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3846",
                "title": "特殊出库二分",
                "methodType": "get",
                "rule": "/new-pda/sowing/special-second",
                "pid": "2561",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4106",
                "title": "一分二分箱空",
                "methodType": "get",
                "rule": "/new-pda/sowing/empty-container",
                "pid": "2561",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4170",
                "title": "多货扫描",
                "methodType": "post",
                "rule": "/new-pda/sowing/more-goods-scan",
                "pid": "2561",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2823",
            "title": "集货",
            "methodType": "post",
            "rule": "/new-pda/collection",
            "pid": "3297",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2824",
                "title": "子仓集货",
                "methodType": "post",
                "rule": "/new-pda/collection/sub-collection",
                "pid": "2823",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2831",
                "title": "释放集货库位",
                "methodType": "post",
                "rule": "/new-pda/collection/release-collection",
                "pid": "2823",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2834",
                "title": "集货查询",
                "methodType": "post",
                "rule": "/new-pda/collection/search-collection",
                "pid": "2823",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2892",
                "title": "特殊出库集货",
                "methodType": "get",
                "rule": "/new-pda/collection/transfer-collection",
                "pid": "2823",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3196",
                "title": "集货拉货交接",
                "methodType": "get",
                "rule": "/new-pda/collection/transfer-and-hand-over",
                "pid": "2823",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3202",
                "title": "波次查询",
                "methodType": "get",
                "rule": "/new-pda/collection/wellen-query",
                "pid": "2823",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4262",
                "title": "集货装托",
                "methodType": "post",
                "rule": "/new-pda/collection/collection-transfer",
                "pid": "2823",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "5143",
                "title": "拉货交接",
                "methodType": "get",
                "rule": "/transport/transport-hand-over",
                "pid": "2823",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6429",
                "title": "领取集货任务",
                "methodType": "get",
                "rule": "/collection/receive-collection-task",
                "pid": "2823",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "3246",
            "title": "特殊出库",
            "methodType": "get",
            "rule": "/new-pda/special-out",
            "pid": "3297",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "3247",
                "title": "整箱出库",
                "methodType": "get",
                "rule": "/new-pda/special-out/box-out",
                "pid": "3246",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3906",
                "title": "整箱暂存下架",
                "methodType": "get",
                "rule": "/new-pda/special-out/box-storage-shelf",
                "pid": "3246",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3910",
                "title": "整箱暂存",
                "methodType": "get",
                "rule": "/new-pda/special-out/box-storage",
                "pid": "3246",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3913",
                "title": "整箱暂存取消",
                "methodType": "get",
                "rule": "/new-pda/special-out/box-storage-cancel",
                "pid": "3246",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6025",
                "title": "特殊出库集货转运",
                "methodType": "get",
                "rule": "/special-out/collection-transshipment",
                "pid": "3246",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6036",
                "title": "特殊出库集货装托",
                "methodType": "get",
                "rule": "/special-out/collection-transfer",
                "pid": "3246",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6047",
                "title": "特殊出库集货接收",
                "methodType": "get",
                "rule": "/special-out/collection-transshipment-receive",
                "pid": "3246",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "6060",
                "title": "特殊出库集货换托",
                "methodType": "get",
                "rule": "/special-out/collection-transfer-changepallet",
                "pid": "3246",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "3750",
            "title": "出库异常处理",
            "methodType": "get",
            "rule": "/new-pda/exception-manage",
            "pid": "3297",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2574",
                "title": "异常拣货",
                "methodType": "post",
                "rule": "/new-pda/order-picking/picking/get-task/2",
                "pid": "3750",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8665",
                "title": "异常关箱",
                "methodType": "get",
                "rule": "/new-pda/sowing/exp-close-box",
                "pid": "3750",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2603",
                "title": "异常分播（停用）",
                "methodType": "post",
                "rule": "/new-pda/sowing/exception",
                "pid": "3750",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4562",
                "title": "异常一分",
                "methodType": "get",
                "rule": "/sowing/exp-first",
                "pid": "3750",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4563",
                "title": "异常二分",
                "methodType": "get",
                "rule": "/sowing/exp-second",
                "pid": "3750",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9587",
                "title": "强制下机(新)",
                "methodType": "get",
                "rule": "/sowing/exp-force-off",
                "pid": "3750",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2666",
                "title": "异常处理",
                "methodType": "get",
                "rule": "/new-pda/sowing/handle-error",
                "pid": "3750",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "3890",
                "title": "补拣箱集货查询",
                "methodType": "get",
                "rule": "/new-pda/sowing/pickup-container",
                "pid": "3750",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8856",
                "title": "异常下架",
                "methodType": "get",
                "rule": "/sowing/exp-off-shelf",
                "pid": "3750",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          }
        ]
      },
      {
        "id": "3298",
        "title": "商品管理系统-调拨管理",
        "methodType": "get",
        "rule": "/new-pda/goods-transer-manager",
        "pid": "61669",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "7608",
            "title": "调拨暂存管理",
            "methodType": "get",
            "rule": "/new-pda/allot-out-temporary",
            "pid": "3298",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "7609",
                "title": "待调拨整箱暂存",
                "methodType": "get",
                "rule": "/new-pda/allot-out-temporary/wait-allot-storage-fcl",
                "pid": "7608",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8945",
                "title": "调拨收货暂存",
                "methodType": "get",
                "rule": "/new-pda/allot-out-temporary/allot-receipt-temporary",
                "pid": "7608",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "7651",
                "title": "待调拨整箱下架",
                "methodType": "get",
                "rule": "/allot-out-temporary/wait-allot-storage-down",
                "pid": "7608",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "2856",
            "title": "调拨管理",
            "methodType": "get",
            "rule": "/new-pda/allot-out",
            "pid": "3298",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "2858",
                "title": "调拨出库",
                "methodType": "get",
                "rule": "/new-pda/allot-out/sales-return",
                "pid": "2856",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2863",
                "title": "取消调拨出库",
                "methodType": "get",
                "rule": "/new-pda/allot-out/cancel-out",
                "pid": "2856",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2865",
                "title": "调拨收货",
                "methodType": "get",
                "rule": "/new-pda/allot-out/receiving",
                "pid": "2856",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "2871",
                "title": "调拨暂存扫描",
                "methodType": "get",
                "rule": "/new-pda/allot-out/temp-scan",
                "pid": "2856",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "4372",
                "title": "二次调拨",
                "methodType": "get",
                "rule": "/new-pda/allot-out/second-allot",
                "pid": "2856",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8570",
                "title": "调拨分箱",
                "methodType": "get",
                "rule": "/new-pda/allot-out/binning",
                "pid": "2856",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "9466",
                "title": "非正品分箱",
                "methodType": "get",
                "rule": "/new-pda/allot-out/non-genuine",
                "pid": "2856",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          },
          {
            "id": "8961",
            "title": "调拨叫货管理",
            "methodType": "get",
            "rule": "/new-pda/allot-out-task",
            "pid": "3298",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": [
              {
                "id": "8962",
                "title": "叫货/收货",
                "methodType": "get",
                "rule": "/allot-out/call-and-receiving",
                "pid": "8961",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              },
              {
                "id": "8963",
                "title": "拉货/发货",
                "methodType": "get",
                "rule": "/allot-out/pull-and-send",
                "pid": "8961",
                "type": "1",
                "remark": "产品经理-Roy Zhou",
                "remarkPositionIdList": [],
                "children": []
              }
            ]
          }
        ]
      },
      {
        "id": "61671",
        "title": "包裹管理系统-入库管理",
        "methodType": "get",
        "rule": "/inbound",
        "pid": "61669",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "61765",
            "title": "入仓收包",
            "methodType": "get",
            "rule": "/inbound/receipt-and-storage",
            "pid": "61671",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61762",
            "title": "上架",
            "methodType": "get",
            "rule": "/inbound/put-shelves",
            "pid": "61671",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62083",
            "title": "收货扫描",
            "methodType": "post",
            "rule": "/inbound/receipt-scan",
            "pid": "61671",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "61672",
        "title": "包裹管理系统-库内管理",
        "methodType": "get",
        "rule": "/in-warehouse",
        "pid": "61669",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "61723",
            "title": "子包裹信息查询",
            "methodType": "get",
            "rule": "/in-warehouse/sub-package-query",
            "pid": "61672",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61724",
            "title": "库位库存查询",
            "methodType": "get",
            "rule": "/in-warehouse/location-stock-query",
            "pid": "61672",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61726",
            "title": "容器查询",
            "methodType": "get",
            "rule": "/in-warehouse/container-query",
            "pid": "61672",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61896",
            "title": "集货查询",
            "methodType": "post",
            "rule": "/in-warehouse/collection-search",
            "pid": "61672",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61999",
            "title": "异常库位库存查询",
            "methodType": "get",
            "rule": "/in-warehouse/exp-location-query",
            "pid": "61672",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62271",
            "title": "二分播种架查询",
            "methodType": "get",
            "rule": "/in-warehouse/query-second-shelf",
            "pid": "61672",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62294",
            "title": "出库包裹查询",
            "methodType": "get",
            "rule": "/in-warehouse/package-query",
            "pid": "61672",
            "type": "1",
            "remark": "产品经理-Alex Chen",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62302",
            "title": "包裹移位",
            "methodType": "get",
            "rule": "/in-warehouse/package-shift",
            "pid": "61672",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "61670",
        "title": "包裹管理系统-出库管理",
        "methodType": "get",
        "rule": "/outbound",
        "pid": "61669",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "61767",
            "title": "异常上架",
            "methodType": "get",
            "rule": "/outbound/abnormal-shelf-launch",
            "pid": "61670",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62397",
            "title": "发货扫描",
            "methodType": "get",
            "rule": "/outbound/shipping-scan",
            "pid": "61670",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61792",
            "title": "边拣边分",
            "methodType": "get",
            "rule": "/outbound/picking?type=pickSplitSameTime",
            "pid": "61670",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62396",
            "title": "包裹装箱",
            "methodType": "get",
            "rule": "/outbound/package-boxing",
            "pid": "61670",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61717",
            "title": "中转仓集货",
            "methodType": "get",
            "rule": "/outbound/transfer-warehouse-collect",
            "pid": "61670",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61763",
            "title": "中转仓分播",
            "methodType": "get",
            "rule": "/outbound/transfer-warehouse-sowing",
            "pid": "61670",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "61794",
            "title": "集中拣货",
            "methodType": "get",
            "rule": "/outbound/picking?type=concentratePick",
            "pid": "61670",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62207",
            "title": "集运仓一分",
            "methodType": "get",
            "rule": "/outbound/collection-warehouse-first-sowing",
            "pid": "61670",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62208",
            "title": "集运仓二分",
            "methodType": "post",
            "rule": "/outbound/collection-warehouse-second-sowing",
            "pid": "61670",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62319",
            "title": "异常包裹暂存",
            "methodType": "get",
            "rule": "/outbound/abnormal-temporary",
            "pid": "61670",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62339",
            "title": "超时异常下架",
            "methodType": "get",
            "rule": "/outbound/timeout-exception-unshelve",
            "pid": "61670",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62340",
            "title": "齐套异常下架",
            "methodType": "get",
            "rule": "/outbound/kitting-exception-unshelve",
            "pid": "61670",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "62085",
        "title": "包裹管理系统-分拣中心管理",
        "methodType": "post",
        "rule": "/sorting-center-management",
        "pid": "61669",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "62116",
            "title": "分拣中心仓收货",
            "methodType": "get",
            "rule": "/sorting-center-management/pick-center-receipt",
            "pid": "62085",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62094",
            "title": "子包裹装箱",
            "methodType": "get",
            "rule": "/sorting-center-management/sub-package-boxing",
            "pid": "62085",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62086",
            "title": "发货扫描",
            "methodType": "post",
            "rule": "/sorting-center-management/delivery-scan",
            "pid": "62085",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      }
    ]
  },
  {
    "id": "62278",
    "title": "开发者工具",
    "methodType": "get",
    "rule": "test1",
    "pid": "0",
    "type": "1",
    "remark": "产品经理-Christopher Liu",
    "remarkPositionIdList": [],
    "children": [
      {
        "id": "6081",
        "title": "商品管理预案",
        "methodType": "get",
        "rule": "/outbound-management",
        "pid": "62278",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "6082",
            "title": "预案波次查询",
            "methodType": "get",
            "rule": "/outbound-management/wave-query",
            "pid": "6081",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "6480",
            "title": "数据监控预警",
            "methodType": "get",
            "rule": "/outbound-management/data-monitoring",
            "pid": "6081",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "7170",
            "title": "打包预案",
            "methodType": "get",
            "rule": "/outbound-management/package-plan",
            "pid": "6081",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "8403",
            "title": "特殊出库预案查询",
            "methodType": "get",
            "rule": "/outbound-management/special-outbound-query",
            "pid": "6081",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "8917",
            "title": "海外本地部署数据割接工具",
            "methodType": "post",
            "rule": "/outbound-management/localized-deployment-cut",
            "pid": "6081",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "62003",
        "title": "包裹管理预案",
        "methodType": "get",
        "rule": "/developer-tools",
        "pid": "62278",
        "type": "1",
        "remark": "产品经理-Christopher Liu",
        "remarkPositionIdList": [],
        "children": [
          {
            "id": "62004",
            "title": "预案工具",
            "methodType": "get",
            "rule": "/developer-tools/wpm-tools",
            "pid": "62003",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62027",
            "title": "拣货组查询",
            "methodType": "get",
            "rule": "/developer-tools/plan-picking-group-query",
            "pid": "62003",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62044",
            "title": "开发工具",
            "methodType": "get",
            "rule": "/developer-tools/dev-tools",
            "pid": "62003",
            "type": "1",
            "remark": "产品经理-Christopher Liu",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62134",
            "title": "转运周转箱查询",
            "methodType": "get",
            "rule": "/developer-tools/transport-shelf-query",
            "pid": "62003",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          },
          {
            "id": "62357",
            "title": "ES数据补推",
            "methodType": "post",
            "rule": "/developer-tools/elasticsearch",
            "pid": "62003",
            "type": "1",
            "remark": "产品经理-Roy Zhou",
            "remarkPositionIdList": [],
            "children": []
          }
        ]
      },
      {
        "id": "62273",
        "title": "通用预案查询工具",
        "methodType": "get",
        "rule": "/developer-tools/plan-tools",
        "pid": "62278",
        "type": "1",
        "remark": "产品经理-Amigo Zheng",
        "remarkPositionIdList": [],
        "children": []
      },
      {
        "id": "62382",
        "title": "test",
        "methodType": "get",
        "rule": "/test",
        "pid": "62278",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": []
      },
      {
        "id": "62407",
        "title": "测试页base",
        "methodType": "get",
        "rule": "/homeBase",
        "pid": "62278",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": []
      },
      {
        "id": "62408",
        "title": "测试页la",
        "methodType": "get",
        "rule": "/homeLa",
        "pid": "62278",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": []
      }
    ]
  },
  {
    "id": "62383",
    "title": "揽收管理",
    "methodType": "get",
    "rule": "/Developer-management-system",
    "pid": "0",
    "type": "1",
    "remark": "产品经理-Roy Zhou",
    "remarkPositionIdList": [],
    "children": [
      {
        "id": "62384",
        "title": "揽收管理",
        "methodType": "get",
        "rule": "test1",
        "pid": "62383",
        "type": "1",
        "remark": "产品经理-Roy Zhou",
        "remarkPositionIdList": [],
        "children": []
      }
    ]
  }

];

// Example usage
const paths = [
  "/qms/defective/return-box-query",
  "/sysconfig/inbound/return-subwarehouse-config",
  "/qms/defective/flow-rack-management",
  "/inbound/park-management/park-hourly-capacity",
  "/basic/in-storage-maintenance/list",
  "/basic/in-storage-configuration",
  "/basic/inbound-configuration/warehouse-entry-rules",
  "/basic/supplier-sub-warehouse",
  "/basic/handover-and-transfer-configuration",
  "/sysconfig/inbound/sorting-port-management",
  "/basic/material-rule-config",
  "/sysconfig/outbound/material-configuration/view",
  "/qms/receipt-management/express-list-query",
  "/basic/turnover-handover-record",
  "/basic/inbound-configuration/delivery-management",
  "/management/basic-data/goods",
  "/management/basic-data/container",
  "/management/basic-data/goods-gather",
  "/basic/category-storage",
  "/basic/national-line",
  "/basic/goods-allow-list",
  "/basic/package-national-line-config",
  "/personnel-mgt/role-manage/list",
  "/system/global-cfg/permission",
  "/basic/over-stock-config",
  "/basic/overdue-threshold-configuration",
  "/management-sys/dept-role-config",
  "/management/basic-data/work-location-manage",
  "/person-manage/group-time-management/list",
  "/basic/member-time-limit",
  "/charging/tickets-rules",
  "/personnel-mgt/user-manage",
  "/personnel-mgt/user-manage",
  "/charging/tickets-management/list",
  "/owc-manage/bill-management/service-provider-bill",
  "/owc-manage/bill-management/statement-details",
  "/sysconfig/inbound/capacity-planning",
  "/management/basic-data/area",
  "/basic/valuation-operation-warehouse",
  "/package-mgt/warehouse-inspection-list",
  "/person-manage/group-time-management/list",
  "/basic/inbound-configuration/weight-discrepancy-allocation",
  "/domestic-salary/basic-info/warehouses-calculate-income",
  "/domestic-salary/basic-info/warehouses-calculate-income",
  "/promise/application-range",
  "/basic/outbound-config/channel-management",
  "/in-warehouse/replenish",
  "/domestic-salary/balance-and-rules/performance-average",
  "/domestic-salary/balance-and-rules/performance-coefficient-score",
  "/basic/use-manager/s-op-goods",
  "/basic/config-management/schedule-rule-config/volume-related-configuration",
  "/basic/config-management/schedule-rule-config/personnel-related-configuration",
  "/basic/market-national-line",
  "/in-warehouse/whitelist-manage/replenishment-and-inventory-whitelist",
  "/domestic-salary/balance-and-rules/settlement/temporary-monitor",
  "/domestic-salary/balance-and-rules/settlement/group-production-capacity",
  "/in-warehouse/capacity-planning",
];

const result = findTitlesByRules(data, paths);
console.log(result.map(x => x.title).join('\n'));

debugger


