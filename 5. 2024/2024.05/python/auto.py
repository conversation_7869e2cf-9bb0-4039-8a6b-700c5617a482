import sys  # 导入sys模块，用于访问系统相关的变量和函数

project_root = "C:\\Users\\<USER>\\sites\\Inbound-Putaway"  # 设置项目根目录路径
sys.path.insert(0, project_root)  # 将项目根目录路径添加到系统路径中，以便导入自定义模块

import pyautogui  # 导入pyautogui模块，用于自动化控制鼠标和键盘
import time  # 导入time模块，用于时间相关操作
from datetime import datetime  # 从datetime模块中导入datetime类，用于处理日期和时间
from selenium.webdriver.common.by import By  # 从selenium.webdriver.common模块中导入By类，用于定位元素
from selenium.webdriver.support import expected_conditions as EC  # 从selenium.webdriver.support模块中导入expected_conditions模块，用于等待条件
from typing import Dict  # 从typing模块中导入Dict类型，用于指定函数返回类型
import os  # 导入os模块，用于操作系统相关功能
from src.errors.error_log import ErrorLog  # 从src.errors.error_log模块中导入ErrorLog类，用于记录错误日志
from selenium.common.exceptions import TimeoutException  # 从selenium.common.exceptions模块中导入TimeoutException异常类，用于处理超时异常


class WmsReportDownload:
    def __init__(self, wait, browser, options,nave):
        self.wait = wait  # 初始化等待对象
        self.browser = browser  # 初始化浏览器对象
        self.options = options  # 初始化选项对象
        self.nave = nave  # 初始化导航对象
        # self.sector = sector  # 初始化区块对象

    def wait_for_element(self, by, value, max_retries=5):
        """
        等待页面元素加载完成
        :param by: 元素定位方法
        :param value: 元素定位值
        :param max_retries: 最大重试次数，默认为5
        :return: 加载完成的元素
        """
        retries = 0  # 初始化重试次数
        while retries < max_retries:  # 循环直到达到最大重试次数
            try:
                return self.wait.until(EC.presence_of_element_located((by, value)))  # 等待元素加载完成并返回
            except TimeoutException:  # 捕获超时异常
                print(f"Timeout waiting for element. Retry {retries + 1}/{max_retries}")  # 打印超时信息
                retries += 1  # 重试次数加1
        raise Exception("Element not found even after retries")  # 如果超过最大重试次数仍未找到元素，则抛出异常

    def download_sheet(self) -> Dict:
        """
        下载报表
        :return: 下载的报表文件名和下载时间
        """
        try:
            extract_url = "https://wms-la.biz.sheinbackend.com/#/management/import-export-mgt/download"  # 设置报表下载页面URL
            self.browser.get(extract_url)  # 打开报表下载页面
            time.sleep(1)  # 等待1秒钟，确保页面加载完成
            self.browser.switch_to.window(self.browser.window_handles[0])  # 切换到第一个窗口
            btn_extract_search = self.wait_for_element(
                By.XPATH,
                '//*[@id="app"]/div/div[1]/div/div/div[2]/section[3]/section/div[1]/div/div/div/div/div/form/div[5]/div/button',
            )  # 定位并等待抽取搜索按钮加载完成
            btn_extract_search.click()  # 点击抽取搜索按钮
            time.sleep(30)  # 等待30秒，确保报表下载完成

            time.sleep(3)  # 等待3秒
            btn_extract_search = self.wait_for_element(
                By.XPATH,
                '//*[@id="app"]/div/div[1]/div/div/div[2]/section[3]/section/div[1]/div/div/div/div/div/form/div[5]/div/button',
            )  # 再次定位抽取搜索按钮
            btn_extract_search.click()  # 再次点击抽取搜索按钮
            time.sleep(3)  # 等待3秒

            if self.nave == 'D':  # 如果导航对象为'D'
                my_file = self.wait_for_element(
                    By.XPATH,
                    '/html/body/div[2]/div/div[1]/div/div/div[2]/section[3]/section/div[1]/div/div/section/div/div[1]/div[2]/div[2]/div/table/tbody/tr[td[contains(text(),"SPglp2WH020")]][1]/td[2]',
                )  # 定位并等待我的文件加载完成
                my_file.click()  # 点击我的文件
            else:
                my_file = self.wait_for_element(
                By.XPATH,
                '/html/body/div[2]/div/div[1]/div/div/div[2]/section[3]/section/div[1]/div/div/section/div/div[1]/div[2]/div[2]/div/table/tbody/tr[td[contains(text(),"SPglp050")]][1]/td[2]',
            )  # 定位并等待我的文件加载完成
                my_file.click()  # 点击我的文件

            time.sleep(3)  # 等待3秒

            downloads_folder = "C:\\Users\\<USER>\\Downloads"  # 设置下载文件夹路径

            downloads = os.listdir(downloads_folder)  # 获取下载文件夹中的所有文件和文件夹

            # Filtra apenas arquivos (exclui pastas)
            downloads = [
                file
                for file in downloads
                if os.path.isfile(os.path.join(downloads_folder, file))
            ]  # 过滤掉文件夹，只保留文件

            # Encontra o arquivo mais recente com base na data de modificação
            if downloads:  # 如果有下载的文件
                recent_file = max(
                    downloads,
                    key=lambda x: os.path.getmtime(os.path.join(downloads_folder, x)),
                )  # 找到最近修改的文件

                # Obtém o nome completo do arquivo mais recente
                complete_path = os.path.join(downloads_folder, recent_file)  # 获取最近文件的完整路径

                # Obtém apenas o nome do arquivo (sem o caminho completo)
                file_name = os.path.basename(complete_path)  # 获取最近文件的文件名

                print("Nome do último arquivo baixado:", file_name)  # 打印最近下载的文件名
                return {"file_name": file_name, "download_time": datetime.now()}  # 返回最近下载的文件名和下载时间
            else:
                print("Nenhum arquivo encontrado na pasta de downloads.")  # 如果下载文件夹为空，打印提示信息
        except Exception as exception:  # 捕获所有异常
            self.browser.quit()  # 关闭浏览器
            raise ErrorLog(str(exception), func="download_sheet()",error_code=9) from exception  # 记录异常到错误日志，并抛出异常
