# 页面改造

## todo list
✅ outbound-mgt/sowing-management/sowing-detail	8656	包裹管理系统/出库管理/ 分播管理/分播明细管理
✅ inbound-mgt/receive-detail-management	4953	包裹管理系统/入库管理/收货明细管理
✅ inbound-mgt/shelf-detail-management	9759	包裹管理系统/入库管理/ 上架明细管理
✅ personnel-mgt/user-manage	3794	人员管理/用户管理/用户
✅ management/basic-data/goods	866	后台管理/ 基础数据/ 库位管理

✅ inbound/reject-check-manage/reject-box-query	6385	商品管理系统/入库管理/特殊入库管理/入库质检管理/入库装箱查询
✅ qms/receipt-management/receipt	1836	商品管理系统/入库管理/收货管理/收货单管理
✅ qms/receipt-management/detail/_ALL_	1158	商品管理系统/入库管理/收货管理/收货单明细查询
✅ inbound/storage-query-detail/list	1075	商品管理系统/入库管理/入仓管理/入库单明细
✅ inbound/reject-order/list	1052	商品管理系统/入库管理/特殊入库管理/入库管理/收货单

outbound/package	4654	商品管理系统/出库管理/包裹管理
outbound/wellen	3790	商品管理系统/出库管理/波次管理
outbound/tasks/pick-data	1441	商品管理系统/出库管理/拣货任务管理/拣货数据查询
basic/product-info	1418	商品管理系统/后台管理/基础数据/商品数据
outbound/tasks/task	1405	商品管理系统/出库管理/拣货任务管理/任务查询
basic-functions/barcode-print	1163	商品管理系统/后台管理/基础功能/条码打印
work-order-management/work-order-deal/work-order-platform	934	 商品管理系统/ 工单管理/ 工单处理/工单工作台
outbound/tasks/pick-detail	870	商品管理系统/出库管理/拣货任务管理/拣货明细查询
outbound/tasks/pick-detail/list	847	商品管理系统/出库管理/拣货任务管理/拣货数据查询

## prompt

帮我利用 /src/utils/business-component-helper.ts 的handleInputMoreProps, handleSelectProps, handleInputMoreProps 函数，仿造代码1，改造代码2，使得代码更加简洁，不要做多余改造。

/src/utils/business-component-helper.ts
```typescript
// import React from 'react';
import { t } from '@shein-bbl/react';
import { type TYPE } from 'shineout';
import { InputMoreProps } from '@shein-components/inputMore/lib';
import { WmsInputProps } from '@shein-components/WmsInput/lib';
import { ObjectKey, KeygenType } from 'shineout/lib/@types/common';
import type { DataDictionaryItem } from '@/apis';

/**
 * 统一设置Table一些常用属性的默认值；建议放在第一行，后边设置的属性也可以将其覆盖
 * 用途：减少代码行数，统一处理一些公共属性设置；后边需要统一配置某些属性时，可以只改一个地方
 */
export function handleTablePros<Value, TRD>(
  columns: TYPE.Table.ColumnItem<Value>[]
): Pick<
  TYPE.Table.Props<Value, TRD>,
  | 'columns'
  | 'width'
  | 'bordered'
  | 'size'
  | 'empty'
  | 'style'
  | 'rowClassName'
  | 'fixed'
> {
  return {
    columns, // TableColumn设置
    // 最小宽度，判断是否出现滚动条；50是为了给表格高级筛选和复选框用的
    width: columns.reduce((pre, curr) => pre + (curr.width || 100), 50),
    bordered: false, // 是否显示外边框
    // fixed: 'both', // 虚拟滚动条方向设置，不设置则使用原生滚动条且关闭懒加载
    size: 'small', // 表格尺寸
    empty: t('暂无数据'), // 空数据文案
    style: { height: '100%' }, // 为了让分页条固定在底部
    rowClassName: () => `borderInner`, // 设置底部边框
  };
}

/**
 * shineout/Modal.error 强制点击配置
 */
export const modalErrorOptions: Pick<
  TYPE.Modal.FunctionOptions,
  'text' | 'autoFocusButton' | 'maskCloseAble'
> = {
  text: { ok: t('确定') },
  autoFocusButton: 'ok',
  maskCloseAble: false,
};

/**
 * InputMore 通用默认值
 *
 * @param props.trim 前后端去空格, 默认为 true
 */
export const handleInputMoreProps = (
  props: InputMoreProps & {
    /** 是否前后去空格 */
    trim?: boolean;
  }
): InputMoreProps => {
  const { title = '', trim = true, max } = props;

  return {
    placeholder: t('请输入{}', title),
    title: t('添加多个{}，以回车键隔开', title),
    modalplaceholder: max
      ? t('支持输入最多{}个{}', max, title)
      : t('支持输入多个{}', title),
    innerTitle: title,
    tip: title,
    text: { cancle: t('取消'), reset: t('重置'), ok: t('确定') },
    maskCloseAble: false,
    overDisabled: true,
    clearable: true,
    trim: trim,
    max: max,
  };
};

/**
 * RuleInput 通用默认值
 *
 * @param props.trim 前后端去空格, 默认为 true
 */
export const handleRuleInputProps = (
  props: TYPE.Input.Props & {
    /** 是否前后去空格 */
    trim?: boolean;
  }
): WmsInputProps => {
  const { trim = true } = props;
  return {
    innerTitle: props.title,
    tip: props.title,
    popover: 'top',
    placeholder: t('请输入'),
    clearable: true,
    trim: trim,
  };
};

/**
 * Select 通用默认值
 */
export const handleSelectProps = <Item = DataDictionaryItem, Value = string>(
  props: Partial<TYPE.Select.Props<Item, Value>> & {
    /** 标题 */
    title: string;
    keygen?: string;
    renderItem?: string;
  }
) => {
  const {
    title,
    multiple,
    keygen = 'dictCode',
    renderItem = 'dictNameZh',
  } = props;

  return {
    innerTitle: title,
    tip: title,
    popover: 'top' as TYPE.Popover.Props['position'],
    keygen: keygen as KeygenType<Item>,
    format: keygen as ObjectKey<Item>,
    renderItem: renderItem as ObjectKey<Item>,
    onFilter: (text: string) => (d: Item) =>
      (d[renderItem as string] || '')
        .toLowerCase()
        .includes(text.toLowerCase()),
    multiple: multiple ? true : false,
    compressed: multiple ? true : false,
    clearable: true,
  };
};
```

代码1:
```typescript
import { t } from '@shein-bbl/react';
import { Form, Rule, Select } from 'shineout';
import InputMore from '@shein-components/inputMore';
import RuleInput from '@shein-components/WmsInput';
import {
  handleInputMoreProps,
  handleRuleInputProps,
  handleSelectProps,
} from '@/utils/business-component-helper';
import SearchAreaContainer from '@/components/search-queries/searchArea-container';
import DateRangePicker from '@/components/shein-components/dateRangePicker2Wrapper';
import { checkDays } from '@utils/date';
import { formatSearchData } from '@utils/tool-function';
import type { DataDictionaryType } from '@/apis';

export interface QueryFormType {
  combineSuggestCodeList: string;
  combinePackageNoList: string;
  packageNo: string;
  handlingStatusList: string[];
  pageNum: number;
  pageSize: number;
  expDateType: number;
  startTime: string;
  endTime: string;
  dateType: number;
}

type HeaderProps = {
  setFormRef: (f: any) => void;
  queryForm: QueryFormType;
  initialQueryForm: QueryFormType;
  onChangeQueryForm: (form: QueryFormType) => void;
  onClearQueryForm: () => void;
  onSearch: () => any;
  handlingStatusList: DataDictionaryType;
  timeTypeList: DataDictionaryType;
  dateTypeList: DataDictionaryType;
};

const rules = Rule({
  timeRange: {
    func: (val, formData: QueryFormType, callback) => {
      if (!formData.startTime || !formData.endTime) {
        // 若存在，则可不填时间: 合单建议编号/合单包裹号
        if (
          !formData.combinePackageNoList &&
          !formData.combineSuggestCodeList &&
          !formData.packageNo
        ) {
          callback(new Error(t('开始时间或结束时间必选')));
        }
      }
      // 限制可选时间段最大不超过30天
      if (checkDays(formData.startTime, formData.endTime, 30)) {
        callback(new Error(t('时间范围不能超过{}天', 30)));
      }
      callback(true);
    },
  },
});

export default function Header({
  setFormRef,
  queryForm,
  onChangeQueryForm,
  onClearQueryForm,
  onSearch,
  handlingStatusList,
  initialQueryForm,
  dateTypeList,
  roleList,
}: HeaderProps) {
  return (
    <div className="commonHeader">
      <SearchAreaContainer
        value={queryForm}
        formRef={setFormRef}
        onSearch={onSearch}
        onClear={onClearQueryForm}
        onChange={(val: QueryFormType) => {
          onChangeQueryForm(formatSearchData(initialQueryForm, val));
        }}
      >
        <InputMore
          {...handleInputMoreProps({ title: t('合单包裹号'), max: 1000 })}
          name="combinePackageNoList"
        />
        <RuleInput
          {...handleRuleInputProps({ title: t('包裹号') })}
          name="packageNo"
        />
        <Select
          {...handleSelectProps({
            title: t('异常处理状态'),
            multiple: true,
          })}
          name="handlingStatusList"
          data={handlingStatusList}
          rules={[rules.handlingStatusList()]}
        />
        <Select
          {...handleSelectProps({
            title: t('角色'),
            keygen: 'id',
            renderItem: 'title',
          })}
          name="positionId"
          data={[{ title: t('无'), id: '-1' }, ...roleList]}
        />
      </SearchAreaContainer>
    </div>
  );
}
```

代码2：
```javascript
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Input, Rule, Select } from 'shineout';
import moment from 'moment';
import DateRangePicker from '@shein-components/dateRangePicker2';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import InputMore from '@shein-components/inputMore';
import { quicklySelectFn } from '@src/lib/dealFunc';
import store, { defaultLimit } from '../reducers';
import Handle from './handle';

const rules = Rule({
  timeRange: {
    func: (val, formData, callback) => {
      // 限制可选时间段最大不超过1个月
      if (moment(formData.createTimeEnd)
        .diff(moment(formData.createTimeBegin), 'months', true) > 1) {
        callback(new Error(t('只能选择一个月内的数据')));
      }
      callback(true);
    },
  },
  timeRangeClose: {
    func: (val, formData, callback) => {
      // 限制可选时间段最大不超过1个月
      if (moment(formData.closeTimeEnd)
        .diff(moment(formData.closeTimeBegin), 'months', true) > 1) {
        callback(new Error(t('只能选择一个月内的数据')));
      }
      callback(true);
    },
  },
  timeRangeExchange: {
    func: (val, formData, callback) => {
      // 限制可选时间段最大不超过1个月
      if (moment(formData.changeTimeEnd)
        .diff(moment(formData.changeTimeBegin), 'months', true) > 1) {
        callback(new Error(t('只能选择一个月内的数据')));
      }
      callback(true);
    },
  },

});
class Header extends Component {
  render() {
    const {
      loading,
      limit,
      goodsQualityList,
      statusList,
      boxTypeList,
      containerCategoryDataList,
      containerLabelList,
      containerFlagsList,
      qualityList,
      allotQualityList,
      purchasePlaceTypeList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          labelStyle={{ width: 90 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('关箱时间'), t('换箱时间'), t('创建时间')]} // 需要校验的字段，需在这里配置且需要加required
        >
          <InputMore
            label={t('箱号')}
            title={t('添加多个箱号,以回车键隔开')}
            name="containerCode"
            clearable
            modalplaceholder={t('支持输入多个单号')}
            text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
            max={100}
            maskCloseAble={false}
            overDisabled
            value={limit.containerCode}
            onChange={(e) => {
              store.changeLimitData({ containerCode: e });
            }}
          />
          <Select
            label={t('商品品质')}
            name="goodsQuality"
            data={goodsQualityList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('箱号类型')}
            name="containerType"
            data={boxTypeList}
            keygen="dictCode"
            format={(d) => String(d.dictCode)}
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('箱子品类')}
            name="containerCategoryIdList"
            data={containerCategoryDataList}
            keygen="id"
            format="id"
            renderItem="containerCategory"
            onFilter={(text) => (d) => d.containerCategory.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('箱号标签')}
            name="containerFlags"
            data={containerFlagsList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('质检类型')}
            name="qualityType"
            data={qualityList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('调拨质检类型')}
            name="allotQualityType"
            data={allotQualityList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('状态')}
            name="status"
            data={statusList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Input
            label={t('提单号')}
            name="ladingBillNo"
            placeholder={t('请输入')}
          />
          <InputMore
            label={t('上架批次号')}
            title={t('添加多个批次号,以回车键隔开')}
            name="batchNo"
            clearable
            modalplaceholder={t('支持输入多个单号')}
            text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
            max={100}
            maskCloseAble={false}
            overDisabled
            value={limit.batchNo}
            onChange={(e) => {
              store.changeLimitData({ batchNo: e });
            }}
          />
          <Select
            label={t('商品标签')}
            name="containerLabels"
            data={containerLabelList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('商品采购地')}
            name="purchasePlaces"
            data={purchasePlaceTypeList}
            keygen="dictNameZh"
            format="dictNameZh"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <DateRangePicker
            style={{ textAlign: 'center' }}
            label={t('关箱时间')}
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['closeTimeBegin', 'closeTimeEnd']}
            defaultTime={['00:00:00', '23:59:59']}
            span={2}
            rules={[rules.timeRangeClose(limit)]}
            quickSelect={quicklySelectFn({
              dateArray: [
                {
                  type: 'days',
                  num: -1,
                }, {
                  type: 'weeks',
                  num: -1,
                }, {
                  type: 'months',
                  num: -1,
                }],
              beginTime: limit.closeTimeBegin || moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
              endTime: limit.closeTimeEnd || moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            })}
            onPickerChange={(v) => {
              store.changeLimitData({
                closeTimeBegin: v[0],
                closeTimeEnd: v[1],
              });
            }}
          />
          <DateRangePicker
            style={{ textAlign: 'center' }}
            label={t('换箱时间')}
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['changeTimeBegin', 'changeTimeEnd']}
            rules={[rules.timeRangeExchange(limit)]}
            defaultTime={['00:00:00', '23:59:59']}
            span={2}
            quickSelect={quicklySelectFn({
              dateArray: [
                {
                  type: 'days',
                  num: -1,
                }, {
                  type: 'weeks',
                  num: -1,
                }, {
                  type: 'months',
                  num: -1,
                }],
              beginTime: limit.changeTimeBegin || moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
              endTime: limit.changeTimeEnd || moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            })}
            onPickerChange={(v) => {
              store.changeLimitData({
                changeTimeBegin: v[0],
                changeTimeEnd: v[1],
              });
            }}
          />
          <DateRangePicker
            style={{ textAlign: 'center' }}
            label={t('创建时间')}
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['createTimeBegin', 'createTimeEnd']}
            defaultTime={['00:00:00', '23:59:59']}
            span={2}
            rules={[rules.timeRange(limit)]}
            quickSelect={quicklySelectFn({
              dateArray: [
                {
                  type: 'days',
                  num: -1,
                }, {
                  type: 'weeks',
                  num: -1,
                }, {
                  type: 'months',
                  num: -1,
                }],
              beginTime: limit.createTimeBegin,
              endTime: limit.createTimeEnd,
            })}
            onPickerChange={(v) => {
              store.changeLimitData({
                createTimeBegin: v[0],
                createTimeEnd: v[1],
              });
            }}
          />
        </SearchAreaContainer>
        <Handle {...this.props} />
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  goodsQualityList: PropTypes.arrayOf(PropTypes.shape()),
  statusList: PropTypes.arrayOf(PropTypes.shape()),
  boxTypeList: PropTypes.arrayOf(PropTypes.shape()),
  containerCategoryDataList: PropTypes.arrayOf(PropTypes.shape()),
  containerLabelList: PropTypes.arrayOf(PropTypes.shape()),
  containerFlagsList: PropTypes.arrayOf(PropTypes.shape()),
  qualityList: PropTypes.arrayOf(PropTypes.shape()),
  allotQualityList: PropTypes.arrayOf(PropTypes.shape()),
  purchasePlaceTypeList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
```