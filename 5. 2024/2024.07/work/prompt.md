帮我利用 business-component-helper.js 的handleInputMoreProps, handleSelectProps, handleInputMoreProps 函数，仿造代码1，改造代码2，使得代码更加简洁，不要做多余改造, 直接输出代码。

/src/lib/business-component-helper.ts
```javascript
import { t } from "@shein-bbl/react";

/**
 * InputMore 通用默认值
 */
export const handleInputMoreProps = (props = {}) => {
  const { title = "", trim = true } = props;

  return {
    placeholder: t("请输入{}", title),
    modalplaceholder: t("请输入{}，批量查询以回车键隔开", title),
    label: title,
    text: { cancle: t("取消"), reset: t("重置"), ok: t("确定") },
    maskCloseAble: false,
    overDisabled: true,
    clearable: true,
    trim: trim,
  };
};

/**
 * RuleInput 通用默认值
 */
export const handleRuleInputProps = (props = {}) => {
  const { title, trim = true } = props;
  return {
    label: title,
    placeholder: t("请输入"),
    clearable: true,
    trim: trim,
  };
};

/**
 * Select 通用默认值
 */
export const handleSelectProps = (props = {}) => {
  const {
    title,
    multiple,
    keygen = "dictCode",
    renderItem = "dictNameZh",
    placeholder,
  } = props;

  return {
    label: title,
    placeholder: placeholder || t("请选择"),
    keygen: keygen,
    format: keygen,
    renderItem: renderItem,
    onFilter: (text) => (d) =>
      d[renderItem].toLowerCase().includes(text.toLowerCase()),
    multiple: multiple ? true : false,
    compressed: multiple ? true : false,
    clearable: true,
  };
};
```

代码1 改造前:
```javascript
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Input, Rule, Select } from 'shineout';
import moment from 'moment';
import DateRangePicker from '@shein-components/dateRangePicker2';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import InputMore from '@shein-components/inputMore';
import { quicklySelectFn } from '@src/lib/dealFunc';
import store, { defaultLimit } from '../reducers';
import Handle from './handle';

const rules = Rule({
  timeRange: {
    func: (val, formData, callback) => {
      // 限制可选时间段最大不超过1个月
      if (moment(formData.createTimeEnd)
        .diff(moment(formData.createTimeBegin), 'months', true) > 1) {
        callback(new Error(t('只能选择一个月内的数据')));
      }
      callback(true);
    },
  },
});
class Header extends Component {
  render() {
    const {
      loading,
      limit,
      goodsQualityList,
      statusList,
      boxTypeList,
      containerCategoryDataList,
      containerLabelList,
      containerFlagsList,
      qualityList,
      allotQualityList,
      purchasePlaceTypeList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          labelStyle={{ width: 90 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('关箱时间'), t('换箱时间'), t('创建时间')]} // 需要校验的字段，需在这里配置且需要加required
        >
          <InputMore
            label={t('箱号')}
            title={t('添加多个箱号,以回车键隔开')}
            name="containerCode"
            clearable
            modalplaceholder={t('支持输入多个单号')}
            text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
            max={100}
            maskCloseAble={false}
            overDisabled
            value={limit.containerCode}
            onChange={(e) => {
              store.changeLimitData({ containerCode: e });
            }}
          />
          <Select
            label={t('箱号类型')}
            name="containerType"
            data={boxTypeList}
            keygen="dictCode"
            format={(d) => String(d.dictCode)}
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('箱子品类')}
            name="containerCategoryIdList"
            data={containerCategoryDataList}
            keygen="id"
            format="id"
            renderItem="containerCategory"
            onFilter={(text) => (d) => d.containerCategory.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('箱号标签')}
            name="containerFlags"
            data={containerFlagsList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Input
            label={t('提单号')}
            name="ladingBillNo"
            placeholder={t('请输入')}
          />
          <InputMore
            label={t('上架批次号')}
            title={t('添加多个批次号,以回车键隔开')}
            name="batchNo"
            clearable
            modalplaceholder={t('支持输入多个单号')}
            text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
            max={100}
            maskCloseAble={false}
            overDisabled
            value={limit.batchNo}
            onChange={(e) => {
              store.changeLimitData({ batchNo: e });
            }}
          />
          <DateRangePicker
            style={{ textAlign: 'center' }}
            label={t('创建时间')}
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['createTimeBegin', 'createTimeEnd']}
            defaultTime={['00:00:00', '23:59:59']}
            span={2}
            rules={[rules.timeRange(limit)]}
            quickSelect={quicklySelectFn({
              dateArray: [
                {
                  type: 'days',
                  num: -1,
                }, {
                  type: 'weeks',
                  num: -1,
                }, {
                  type: 'months',
                  num: -1,
                }],
              beginTime: limit.createTimeBegin,
              endTime: limit.createTimeEnd,
            })}
            onPickerChange={(v) => {
              store.changeLimitData({
                createTimeBegin: v[0],
                createTimeEnd: v[1],
              });
            }}
          />
        </SearchAreaContainer>
        <Handle {...this.props} />
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  goodsQualityList: PropTypes.arrayOf(PropTypes.shape()),
  statusList: PropTypes.arrayOf(PropTypes.shape()),
  boxTypeList: PropTypes.arrayOf(PropTypes.shape()),
  containerCategoryDataList: PropTypes.arrayOf(PropTypes.shape()),
  containerLabelList: PropTypes.arrayOf(PropTypes.shape()),
  containerFlagsList: PropTypes.arrayOf(PropTypes.shape()),
  qualityList: PropTypes.arrayOf(PropTypes.shape()),
  allotQualityList: PropTypes.arrayOf(PropTypes.shape()),
  purchasePlaceTypeList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
```

代码1 改造后:
```javascript
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Rule, Select } from 'shineout';
import moment from 'moment';
import DateRangePicker from '@shein-components/dateRangePicker2';
import RuleInput from '@shein-components/wmsInput';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import InputMore from '@shein-components/inputMore';
import {
  handleInputMoreProps,
  handleRuleInputProps,
  handleSelectProps,
} from '@src/lib/business-component-helper';
import { quicklySelectFn } from '@src/lib/dealFunc';
import store, { defaultLimit } from '../reducers';
import Handle from './handle';

const rules = Rule({
  timeRange: {
    func: (val, formData, callback) => {
      // 限制可选时间段最大不超过1个月
      if (moment(formData.createTimeEnd)
        .diff(moment(formData.createTimeBegin), 'months', true) > 1) {
        callback(new Error(t('只能选择一个月内的数据')));
      }
      callback(true);
    },
  },
});
class Header extends Component {
  render() {
    const {
      loading,
      limit,
      goodsQualityList,
      statusList,
      boxTypeList,
      containerCategoryDataList,
      containerLabelList,
      containerFlagsList,
      qualityList,
      allotQualityList,
      purchasePlaceTypeList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          labelStyle={{ width: 90 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('关箱时间'), t('换箱时间'), t('创建时间')]} // 需要校验的字段，需在这里配置且需要加required
        >
          <InputMore
            {...handleInputMoreProps({ title: t('箱号'), max: 100 })}
            name="containerCode"
            value={limit.containerCode}
            onChange={(e) => store.changeLimitData({ containerCode: e })}
          />
          <Select
            {...handleSelectProps({
              title: t('箱号类型'),
              multiple: true,
            })}
            name="containerType"
            data={boxTypeList}
          />
          <Select
            {...handleSelectProps({
              title: t('箱子品类'),
              multiple: true,
              keygen: 'id',
              renderItem: 'containerCategory',
              placeholder: t('全部'),
            })}
            name="containerCategoryIdList"
            data={containerCategoryDataList}
          />
          <Select
            {...handleSelectProps({
              title: t('箱号标签'),
              placeholder: t('全部'),
            })}
            name="containerFlags"
            data={containerFlagsList}
          />
          <RuleInput
            {...handleRuleInputProps({title: t('提单号')})}
            name="ladingBillNo"
          />
          <InputMore
            {...handleInputMoreProps({ title: t('上架批次号'), max: 100 })}
            name="batchNo"
            value={limit.batchNo}
            onChange={(e) => store.changeLimitData({ batchNo: e })}
          />
          <DateRangePicker
            style={{ textAlign: 'center' }}
            label={t('创建时间')}
            required
            placeholder={[t('开始时间'), t('结束时间')]}
            type="datetime"
            inputable
            format="yyyy-MM-dd HH:mm:ss"
            name={['createTimeBegin', 'createTimeEnd']}
            defaultTime={['00:00:00', '23:59:59']}
            span={2}
            rules={[rules.timeRange(limit)]}
            quickSelect={quicklySelectFn({
              dateArray: [
                {
                  type: 'days',
                  num: -1,
                }, {
                  type: 'weeks',
                  num: -1,
                }, {
                  type: 'months',
                  num: -1,
                }],
              beginTime: limit.createTimeBegin,
              endTime: limit.createTimeEnd,
            })}
            onPickerChange={(v) => {
              store.changeLimitData({
                createTimeBegin: v[0],
                createTimeEnd: v[1],
              });
            }}
          />
        </SearchAreaContainer>
        <Handle {...this.props} />
      </section>
    );
  }
}

Header.propTypes = {
  loading: PropTypes.number,
  limit: PropTypes.shape(),
  goodsQualityList: PropTypes.arrayOf(PropTypes.shape()),
  statusList: PropTypes.arrayOf(PropTypes.shape()),
  boxTypeList: PropTypes.arrayOf(PropTypes.shape()),
  containerCategoryDataList: PropTypes.arrayOf(PropTypes.shape()),
  containerLabelList: PropTypes.arrayOf(PropTypes.shape()),
  containerFlagsList: PropTypes.arrayOf(PropTypes.shape()),
  qualityList: PropTypes.arrayOf(PropTypes.shape()),
  allotQualityList: PropTypes.arrayOf(PropTypes.shape()),
  purchasePlaceTypeList: PropTypes.arrayOf(PropTypes.shape()),
};
export default Header;
```


代码2：
```javascript
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { t } from '@shein-bbl/react';
import {
  Input, Select, Rule, Checkbox, Form,
} from 'shineout';
import DateRangePicker from '@shein-components/dateRangePicker2';
import SearchAreaContainer from '@public-component/search-queries/searchArea-container';
import { formatSearchData } from '@public-component/search-queries/utils';
import InputMore from '@shein-components/inputMore';
import FilterSearchSelect from '@src/component/public-component/select/filter-search-select';
import store, { defaultLimit } from '../reducers';
import Handle from './handle';
import styles from '../style.less';

const rule = Rule({
  timeRange: {
    func: (val, formData, callback) => {
      if ([formData.noticeCode, formData.packageNo, formData.billno, formData.trackNo, formData.rebackCode, formData.shippingNo].every((v) => (v?.trim ? v.trim() === '' : v === ''))) {
        if (!formData.startTime || !formData.endTime) {
          callback(new Error(t('开始时间或结束时间必选')));
        }
        // 时间范围不能超过一个月
        if (moment(formData.endTime)
          .customDiff(moment(formData.startTime), 'months', true) > 1) {
          callback(new Error(t('时间范围不能超过{}个月', 1)));
        }
      }
      callback(true);
    },
  },
});
class Header extends Component {
  render() {
    const {
      limit,
      typeList,
      statusList,
      loading,
      isRepeatList,
      timeTypeList,
      prcInvoiceStatusList,
      isPrcList,
    } = this.props;
    return (
      <section>
        <SearchAreaContainer
          clearUndefined={false}
          labelStyle={{ width: 100 }} // 统一修改label宽度，默认60
          searching={!loading}
          value={limit}
          // 保留用户所选项，并补全搜索条件
          onChange={(val) => store.changeLimitData(formatSearchData(defaultLimit, val))}
          onClear={() => store.clearLimitData()}
          onSearch={() => {
            // 点搜索按钮，将页码重置为1
            store.handlePaginationChange({ pageNum: 1 });
          }}
          formRef={(f) => store.changeData({ formRef: f })}
          alwaysVisible={[t('联动时间')]} // 需要校验的字段，需在这里配置且需要加required
        >
          <InputMore
            label={t('收货单号')}
            title={t('添加多个单号,以回车键隔开')}
            name="noticeCode"
            clearable
            modalplaceholder={t('支持输入多个单号')}
            text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
            max={100}
            maskCloseAble={false}
            overDisabled
            value={limit.noticeCode}
            onChange={(e) => {
              store.changeLimitData({ noticeCode: e });
            }}
          />
          <Select
            label={t('单据类型')}
            name="noticeType"
            data={typeList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <Select
            label={t('状态')}
            name="status"
            data={statusList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            multiple
            compressed
            clearable
            placeholder={t('全部')}
          />
          <InputMore
            label={t('包裹号')}
            title={t('添加多个包裹号,以回车键隔开')}
            name="packageNo"
            clearable
            modalplaceholder={t('支持输入多个单号')}
            text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
            max={100}
            maskCloseAble={false}
            overDisabled
            value={limit.packageNo}
            onChange={(e) => {
              store.changeLimitData({ packageNo: e });
            }}
          />
          <InputMore
            label={t('订单号')}
            title={t('添加多个单号,以回车键隔开')}
            name="billno"
            clearable
            modalplaceholder={t('支持输入多个单号')}
            text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
            max={100}
            maskCloseAble={false}
            overDisabled
            value={limit.billno}
            onChange={(e) => {
              store.changeLimitData({ billno: e });
            }}
          />
          <InputMore
            label={t('退货运单号')}
            title={t('添加多个单号,以回车键隔开')}
            name="trackNo"
            clearable
            modalplaceholder={t('支持输入多个单号')}
            text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
            max={100}
            maskCloseAble={false}
            overDisabled
            value={limit.trackNo}
            onChange={(e) => {
              store.changeLimitData({ trackNo: e });
            }}
          />
          <InputMore
            label={t('退货单号')}
            title={t('添加多个单号,以回车键隔开')}
            name="rebackCode"
            clearable
            modalplaceholder={t('支持输入多个单号')}
            text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
            max={100}
            maskCloseAble={false}
            overDisabled
            value={limit.rebackCode}
            onChange={(e) => {
              store.changeLimitData({ rebackCode: e });
            }}
          />
          <InputMore
            label={t('发货运单号')}
            title={t('添加多个单号,以回车键隔开')}
            name="shippingNo"
            clearable
            modalplaceholder={t('支持输入多个单号')}
            text={{ cancle: t('取消'), reset: t('重置'), ok: t('确定') }}
            max={100}
            maskCloseAble={false}
            overDisabled
            value={limit.shippingNo}
            onChange={(e) => {
              store.changeLimitData({ shippingNo: e });
            }}
          />
          <FilterSearchSelect
            label={t('收货员')}
            name="receiveUser"
            clearable
            placeholder={t('请输入')}
          />
          <FilterSearchSelect
            label={t('联系人')}
            name="customer"
            clearable
            placeholder={t('请输入')}
          />
          <Select
            label={t('重复收包')}
            name="isRepeat"
            data={isRepeatList}
            keygen="value"
            format="value"
            placeholder={t('全部')}
            renderItem="name"
          />
          <Select
            label={t('作废包裹')}
            name="isInvalid"
            data={[
              {
                name: t('全部'),
                value: '',
              },
              {
                name: t('是'),
                value: 1,
              },
              {
                name: t('否'),
                value: 0,
              },
            ]}
            keygen="value"
            format="value"
            placeholder={t('全部')}
            renderItem="name"
          />
          <Select
            label={t('跨仓质检')}
            name="isCross"
            data={[{ dictCode: 1, dictNameZh: t('是') }, { dictCode: 0, dictNameZh: t('否') }]}
            keygen="dictCode"
            format="dictCode"
            placeholder={t('全部')}
            renderItem="dictNameZh"
            clearable
          />
          <Select
            label={t('是否PRC包裹')}
            name="isPrc"
            data={isPrcList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            placeholder={t('全部')}
            renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            clearable
          />
          <Select
            label={t('开票状态')}
            name="invoiceStatus"
            data={prcInvoiceStatusList}
            keygen="dictCode"
            format="dictCode"
            renderItem="dictNameZh"
            placeholder={t('全部')}
            renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('全部')}</span>}
            onFilter={(text) => (d) => d.dictNameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
            compressed
            multiple
            clearable
          />
          <Form.Field
            required
            name={['timeType', 'startTime', 'endTime']}
            rules={[rule.timeRange()]}
            label={t('联动时间')} hideLabel span={2}
          >
            {({ value, onChange }) => (
              <div className={styles.headerSearchTimeLine}>
                <Select
                  className={styles.headerTimeSelect}
                  keygen="dictCode"
                  format="dictCode"
                  renderItem="dictNameZh"
                  renderUnmatched={(r) => r.dictNameZh || <span className={styles.headerUnmatchedText}>{t('选择时间类型')}</span>}
                  placeholder={t('选择时间类型')}
                  data={timeTypeList}
                  value={value[0]}
                  onChange={(val) => onChange([val, value[1], value[2]])}
                />
                <DateRangePicker
                  placeholder={[t('开始时间'), t('结束时间')]}
                  type="datetime"
                  inputable
                  format="yyyy-MM-dd HH:mm:ss"
                  defaultTime={['00:00:00', '23:59:59']}
                  value={[value[1], value[2]]}
                  onChange={(dates) => onChange([value[0], dates[0], dates[1]])}
                />
              </div>
            )}
          </Form.Field>
        </SearchAreaContainer>
        <Handle {...this.props} />
      </section>
    );
  }
}

Header.propTypes = {
  limit: PropTypes.shape(),
  warehouseList: PropTypes.arrayOf(PropTypes.object),
  typeList: PropTypes.arrayOf(PropTypes.object),
  statusList: PropTypes.arrayOf(PropTypes.object),
  reasonList: PropTypes.arrayOf(PropTypes.object),
  sourceList: PropTypes.arrayOf(PropTypes.object),
  isRepeatList: PropTypes.arrayOf(PropTypes.object),
  loading: PropTypes.number,
  topSearch: PropTypes.bool,
  timeTypeList: PropTypes.arrayOf(PropTypes.object),
};
export default Header;

```