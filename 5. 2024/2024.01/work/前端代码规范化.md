# 前端代码规范化

## 背景

原本的前端项目是有对应的前端代码规范化配置的，但wms-la和mot-la 项目改为一仓多包后，原先的相关的配置不适用。现在需要重新让设配起来。

## 前端代码规范化工具
- ESLint
- Prettiter
- lint-staged
- husky
- commitlint

## 挑战与开发

### 一仓多包的情况

wms-la 和mot-la 下的各个包的规范化配置大体相同，但存在“部分包为ts，部分包为js”的情况，需要兼容处理。

处理方式是将commitlint、lint-staged、husky 部分的配置放在根目录下，将eslint、prettier 部分的配置放在各个包下。

### husky 自定义hook

各个包的husky 自定义hook 不完全相同，需要将对应的功能整理出来，进行统一配置。

其中的pre-commit hook下的这3个功能去除：storageVerify、onFilterAndRenderItemMatch、judgeByCnVerify，因为这3个功能是基于babel AST来实现的，执行的耗时太长了，没必要在提交代码时检测，改为webhook触发代码检测和对应告警，提升下开发体验。

### jq 命令行工具的去除

另外去除的jq（命令行工具）的依赖，改用node来实现对json文件的读取配置。因为公司内网的原因，jq的安装会出现问题，索性就不用了。

## 改造计划

0. husky 配置梳理，预计01.02
1. mot-la项目安排改造，预计01.03-01.04
2. wms-la项目安排改造，预计01.04
3. 持续一周的测试环境观察验证，预计01.08-01.12
4. 上线，预计01.18

## 后续TODO

- [ ] 企微机器人 代码检测