# jira 定时推送

## 工作流程

1. push-robot: 定时执行指令
2. jira: 调用wms-mpp的api, 获取jql数据
3. push-robot: 进行数据处理
4. dad: 调用dad的api，进行企微推送

## push-robot 

push-robot，集成了两个主要功能，一个是gitlab的代码检测，一个是jira的定时推送

目前给国内/欧美/北美/拉美提供定制服务，但因为组织架构原因（国内/欧美/北美的上级是尚儒哥），拉美加不到人员名单中，导致人员映射功能无法使用

而“没有处理中的任务”提醒，也是因为这个原因，无法使用（这个功能应该是mpp的功能，具体情况还要了解）

代码仓库：https://gitlab.sheincorp.cn/div/group_biz/wms-extensions/push-robot

paas应用：https://paas.sheincorp.cn/app-space/1526

## wms-mpp

wms-mpp: http://wms-mpp-cneast-test-test:9091/jira/search

江雨的服务，实际上是对jira的中转封装，用了碧珊的token

## jira

jira本身是有api, 但需要认证 token, 而jira对token没有进行权限划分，意味着token可以访问所有的数据，这不安全

jira api: https://jira.dotfashion.cn/rest/api/2

## dad 与 企微推送

架构提供的企微推送服务，http://ad.sheincorp.cn/dad/api

可以通过api，推送消息@到指定人，但要在告警中心申请群聊机器人

如果只要推送到个人，不需要申请机器人，只需要知道工号即可

如果要推送到群聊，只需要群聊id，在告警中心中直接创建通知群即可

## 目前近况

1. 推进jira 团队提供安全合规的api，替换掉wms-mpp
2. push-robot 增加拉美自己的“没有处理中的任务”提醒: 获取当日的jql数据，反向筛选出没有处理中的任务的人员，推送到个人






