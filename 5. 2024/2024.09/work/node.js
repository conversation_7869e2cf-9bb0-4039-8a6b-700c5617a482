// 输入的多行字符串数据
const inputData = `main-menu
inbound/put-shelves
in-warehouse/sub-package-query
outbound/picking
login
sub-menu
outbound/shipping-scan-by-package
select-warehouse
in-warehouse/container-query
order-picking/picking
outbound/abnormal-shelf-launch
put-shelves/quality-shelves
outbound/transfer-warehouse-sowing
in-warehouse/collection-search
in-warehouse/location-stock-query
outbound/transfer-warehouse-collect
put-shelves/put-away
take-account/take
outbound/shipping-scan
take-account/take/1
sorting-center-management/sub-package-boxing
sorting-center-management/delivery-scan
order-picking/picking/picking-page/1
query/container-query
outbound/kitting-exception-unshelve
inbound/receipt-and-storage
collection/sub-collection
order-picking/picking/get-task/1
standard-receive-goods/new-return-package-scan
outbound/timeout-exception-unshelve
sowing/second
inbound/receipt-scan
put-shelves/put-away/put-page
query/goods-stock
in-warehouse/package-query
in-warehouse/exp-location-query
query/location-stock
sorting-center-management/pick-center-receipt
in-warehouse/pre-broadcast-query
put-shelves/shift-up
take-account/take/2
homework-subwarehouse
order-picking/other-outbound
refund-scan/goods-receipt
put-shelves/put-error
inbound/fm-manage
in-warehouse/package-shift
collection/search-collection
sub-menu/list
order-picking/freeze
collection/receive-collection-task
order-picking/shift-down
outbound/package-boxing
outbound/abnormal-temporary
in-warehouse/query-second-shelf
collection/release-collection
back-warehouse/new-binning
take-account/take/3
sowing/exp-second
sowing/first
order-picking/picking/get-task/2
take-account/take/12
special-out/box-storage
sowing/special-second
order-picking/picking/picking-page/2
take-account/take/4
special-out/box-storage-shelf
standard-receive-goods/return-package-scan-new
standard-receive-goods/standard-receive-detail
query/location-info-manage
order-picking/shift-down/picking-page
query/car-search
return/return-receive
query/inventory-flow-status
query/container-query-new
order-picking/recall-shift-down
query
order-picking
return/purchase-return-down
sowing/more-goods-scan
collection/wellen-query
outbound/shippingCBG24081300118991package
sowing/handle-error
put-shelves
return/return-delivery
special-out/box-out
login%20do%20o
sowing/exp-first
collection/main-collection
standard-receive-goods/return-package-scan
order-picking/shelf-life-frozen
back-warehouse/binning
sowing/pickup-container
inbound-manage/binning-by-goods
return/return-up
inbound-manage/batch
goods_collect/gather-task
refund-scan/return-binning
return/return-down
return/return-store-query
outbound/exception-unshelve
sorting-cem
inbound-manage/binning-by-package
allot-out/sales-return
return/logistics-order
sowing/special-first
goods_collect/goods-info-collection
return/return-purchase-down
inbound-manage/points-for-goods
exception/package

in-warehouseGC2405134663539717-package-query
inbound-manage/exchange-container-new
inbound-manage/blackcode-by-package
special-out/collection-transshipment-receive
******************
exception-manage
return/return-store
take-account
login0
home
sowing/exp-close-box
query/short-pick
outbound-mgt/package/parcel-package
receive-goods/special-receive-goods
sowing/exp-off-shelf
refund-scan/scan-putaway
refund-scan/inferior-receipt
collection/transfer-collection
sowing/second-scan
sowing/empty-container
collection/close-collection-task
collection/collection-transshipment
loginhttps://mot-la-hd.biz.sheinbackend.com/
standard-receive-goods/new-return-package-sc
standard-receive-goods/new-return-package-scanv
MAIN-MENU
inbound/reject-check-manage/reject-check-scan
outbound-mgt/delivery-box
loginNaveD@2023
outbound/collection-warehouse-second-sowing
log
homework`;

// 将多行字符串分割为数组，并移除前后的空白字符
const dataArray = inputData.split('\n').map(item => item.trim());

// 生成JavaScript代码
const result = `const arr = ${JSON.stringify(dataArray, null, 2)};`;

// 输出结果
console.log(result);
