# 3 月份 第 4 周
## 本周 todo
- 新人培训指南

## 2024.03.25 周一
todo
- 发版
- 新人文档
  - 
- 整理下la的图标 ✅
- soapi 生成server.ts

- soapi的点子是不是集成在chrome插件更好一些
- 第二步骤：jira单 默认值

## 2024.03.26 周二
todo
- 需求评审 ✅
- OFC-158404 WMS-LA入库稽查新增‘体积重稽查’稽查类型【GZSCC-20894-1】
  - 改布局
- OFC-159916 【前端需求】ioa 登录wms-la 加载慢问题调研 ✅
  - seed.js 加载问题：业务反馈的优化问题 ✅
- LCPS优化

21*8=168

7.37+9.6


1 management/import-export-mgt/download	6257	35.34
  - 看了灰度，还行
2	package-mgt/child-package-list	1932	10.91
  - 
3	package-mgt/package-list	1549	8.75
4	outbound-mgt/picking-management/picking-task-detail	1471	8.31
5	inbound-mgt/shelf-detail-management	1370

## 2024.03.27 周三
todo
- OFC-160182 【产品需求】拣货组弹窗 ✅
  - wms-la ✅
- OFC-160199 【WPM】自动化拣货组生成策略支持异形件
  - wms-la ✅
  - mot-la ✅
- OFC-160143 【WMS-LA-MOT】拣货与上架环节增加工作量展示
  - mot-la ✅
- todo gitlab 脚本检测
- todo 新人培训

## 2024.03.28 周四
todo
- 发版 ✅
- 新人培训 文档整理 ✅
- gitlab 脚本检测
- OFC-159818 【前端需求】LCP巡检：优化页面LCPS 



## 2024.03.29 周五
todo
- gitlab 脚本检测 ✅
- OFC-158404 WMS-LA入库稽查新增‘体积重稽查’稽查类型【GZSCC-20894-1】✅
  - 音效优化 
- 新人培训 文档整理
- OFC-159818 【前端需求】LCP巡检：优化页面LCPS 


1	management/import-export-mgt/download	7772	34.46 ✅
2	package-mgt/child-package-list	2550	11.31 （页面本身问题不大
3	package-mgt/package-list	2381	10.56（页面本身问题不大
4	outbound-mgt/picking-management/picking-task-detail	1748	7.75
5	inbound-mgt/shelf-detail-management	1535	6.81
6	outbound-mgt/package/package-record	1505	6.67
7	home	968	4.29

## 2024.03.30 周六
## 2024.03.31 周日
