## 前端侧 的ai 探索

### 介绍
利用ai赋能前端开发，终极目标是自动化生成需求代码。想法很大，但太大，需要拆分成小目标，逐步实现，先从生成server 部分开始，再到view 部分，再到hook/state 部分，最终实现生成一个完整的页面。

### 背景
server 部分主要是前后端接口对接，前端需要根据后端提供的接口文档生成对应的ts类型声明以及对应的请求代码。这部分工作是费时又重复，自动生成能带来可观的提效。

### 工作开展思路
1. 拉通：拉通公司内部的官方soapi API 和GPT-chat API，实现agent 功能: 输入接口文档url，输出文件；
2. 日志/优化：建立日志存储，方便分析对话，优化prompt；
3. 耐心：公司内部api 变动/模型更新相对频繁，需要耐心调试；

### 落地情况
1. 生成server的agent 试水成功，提升了开发效率；
2. 生成view的agent因为token的原因，生成内容的准确性不高，开发进度较慢，但下半年已经有了下一步的探索方向：1. 通过对view部分代码的进一步抽象，降低prompt的token数，提升输出内容的准确性；2. 对比不同模型的输出，找到最优模型。