const fs = require('fs');
const readline = require('readline');

const rl = readline.createInterface({
  input: fs.createReadStream('data.txt'),
  crlfDelay: Infinity
});

const regex = /[a-zA-Z]/;
const filteredData = [];

rl.on('line', (line) => {
  try {
    const item = JSON.parse(line);
    if (!regex.test(item.input)) {
      filteredData.push(item);
    }
  } catch (err) {
    console.error('解析JSON时发生错误：', err);
  }
});

rl.on('close', () => {
  console.log('不含英文字母的记录有：', filteredData.length, '条');
  fs.writeFileSync('filteredData.json', JSON.stringify(filteredData, null, 2), 'utf-8');
  console.log('过滤后的数据已保存至filteredData.json');
});
