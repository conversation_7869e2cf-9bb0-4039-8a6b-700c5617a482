# 12 月份 第 1 周
## 本周 todo
- wpm 标准化

## 2024.12.02 周一
todo
忙活独立子应用的事情

- wpm 标准化
  - wpm 与 micro-la 的分离 ✅
    - std-wpm 与 micro-la 路由区分 ✅
      - [todo]std-wpm 中存在部分非wpm 标准化的页面
        - 例子
    - 运行 ✅
  - alita/lego 升级 ✅
  - 接口前缀处理 ✅
    - /wms/stat -> /wkb
    - /wms/internal -> /osm
  - 同步北美wpm 改动
    - 拣货进度看板
      - packages/wpm/src/pages/board-mgt/picking-sorting-board/hooks/use-dict.ts
  - [todo]路由切换&回滚方案
  - 标准化:独立配置
    - [todo]声效
    - [todo]20 ws通信

先忙活wms的，再忙活mot部分


## 2024.12.03 周二
todo
- 跟sam开会 ✅
- 升级uem-sdk

wpm 

- 标准化
  - wms: std-wpm
    - 暂时做到这
  - mot: std-wpm
    - std-wpm 与mot-la 分离
    - alita/lego/h2 升级
      - std-wpm ✅
      - mot-la
      - mot
    - 接口前缀处理

正常拣货

## 2024.12.04 周三
todo
- 需求评审 ✅
- 标准化
  - 接入入库中台 ✅
  - 申请wpm应用，搞配置 
  - alita
  


## 2024.12.05 周四
todo
- 需求评审 ✅
- 拉美2024数据对齐 ✅
- 标准化
  - mot代码：alita升级 ✅
  - [todo]验证：装车
  - wms-wpm-std-front 的paas 配置
    - ✅
  - mot-wpm-std-front 的paas 配置

wms-na 的 useEffect 应用场景
/inbound/reject-check-manage/receive-check-scan


## 2024.12.06 周五
todo
- 填绩效表(今天搞定)
- 发灰度
  - pre-master-12-10
  
- 标准化
  - mot代码：alita升级 ✅
  - [todo]验证：装车
  - wms-wpm-std-front
    - pass ✅
    - mesh ✅ 
  - mot-wpm-std-front
    - pass ✅
    - mesh ✅
    - 确认样式问题 ✅





## 2024.12.07 周六
## 2024.12.08 周日
- 收集资料，写ppt
