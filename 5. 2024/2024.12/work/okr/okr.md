# okr 数据

## 称重机
1年节省1.5w人民币
每月节省6.48w人民币
B仓和D仓10月份节省7.2w人民币
每月节省0.72w人民币

## 虚拟键盘不弹起的
1483209+2111773+2102990+641430+98079+2446840+900125+126557+109285

382453/(8*60*60)=13个人日

10089536/(8*60*60)=

13*2500=32500
32500*4=13w人民币

减少mot用户查询耗时2s/人次，子包裹查询周均查询接口3.8w。38000 * 2* 365/60/60，减少7704h。每小时工时14元（2500/(22*8)），全年大概节省10w。

2500/(22*8)

7704*14=107856

这个需求一周节省900125秒

## 具体一周的请求数据

/wpm/front/sowing/mot/scan_sub_package_no (1483209)

inbound/put-shelves
/wpm/front/upper/scan/package_no 2111773
/wpm/front/upper/scan/location 2102990
/wpm/front/upper/mot/empty_container 6712

outbound/shipping-scan-by-package
/wpm/front/delivery/mot/scan_package 641430

outbound/picking
/wpm/front/picking/mot/scan_picking_container 98079
/wpm/front/picking/mot/close_picking_container 32008
/wpm/front/picking/mot/scan_sub_package_no 2446840
/wpm/front/picking/mot/skip_current_grid 16605
/wpm/front/picking/mot/mark_not_picked 4211
/wpm/front/picking/mot/skip_picking_task 9712

in-warehouse/sub-package-query
/wpm/front/in_location/mot/query_in_store_package 900125

outbound/abnormal-shelf-launch
/wpm/front/exception/mot/shift_up/scan_sub_package_no 126557
/wpm/front/exception/mot/shift_up/scan_location 109285

