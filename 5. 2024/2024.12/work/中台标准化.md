# 中台标准化

  - wpm 独立子应用
    - 目的：要作为一个独立中台的存在
    - mot-la
    - wms-la
  - alita 升级
    - 目的：接入其他中台
    - mot-la
    - wms-la

## alita 升级
wms-la 

- alita 升级 ✅
- logo组件 升级 ✅
- h2 优化 ✅
- 缓存 优化 ✅

# wpm 独立子应用
- https://gitlab.sheincorp.cn/wms/front/standard/mot-wpm-std-front
- https://gitlab.sheincorp.cn/wms/front/standard/wpm-std-front

wms-la 
- fork 仓库
- 可运行
- 同步北美改动点
- 同步alita升级
- 准备需求合并工具
  - 试试cherry-pick难不难
- 前端调整
- 申请应用

mot-la
- fork 仓库
- 可运行
- 同步北美改动点
