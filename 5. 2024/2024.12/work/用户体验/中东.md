# 中东-用户体验
中东-用户体验
	页面展示优化
		界面布局
			全局配置
				1. 所有界面统一展示 【时区模块】，替换apollo参数-前端改造已完成
				2. mot-me的modal弹窗补充error、info、success等类型颜色和icon，减少用户误解
			表单栏
				1. 修改高级搜索，固定必填搜索项。查询和清空按钮居右上布局
				2. 操作按钮固定在右侧, 按钮使用 text类型，className为 globalStyles.tableTextButton
				3. 若必填存在校验，在数据修改时自动触发校验。 - 避免用户在点击查询时才发现条件不合规，减少用户操作
			操作栏
				1. 操作按钮都需要添加并统一icon图标，避免用户对翻译英文有误解产生误操作
			列表栏
				1. 表头英文 整个单词不进行换行，提高可读性
				2. 合理规划列宽，避免一列数据展示多行。减少用户滚动和查找时间，提高可读性
		视觉展示
			1. 统一色调：200种颜色统一整理为20多种主题色，保证一致性的视觉统一，更容易维护。提高了可读性与可访问性
			2. 差异化测试环境主题色：清晰区分环境，有效避免研发误操作，提升操作安全性。ceva新仓UAT过程中成功避免了用户误操作
		信息数据展示
			1.  BBL词条优化：清除1.4w无用词条，新增 5k 缺失词条。提升界面加载速度，避免界面出现无翻译信息，给予用户更完整的信息展示
	页面交互优化
		1. 云配置升级推广： 升级云配置组件并进行推广。支持用户自定义固定表单查询、表格列，提升用户操作便利性
		2. Tab组件优化升级： 详情界面支持自动打开tab，并缓存界面数据。避免每次进入时的重复请求，减少用户等待时长
	页面性能优化
		资源加载优化
			1. 接口请求优化:  核心页面将串行接口并行处理,移除无用接口，减少请求接口耗时
			2. 资源缓存优化：使用servive worker缓存云配置等静态资源,支持界面秒开, lcps下降54%，大幅度提升了用户体验
			3. 无用CSS移除： 移除index.ejs中无用的css，减少首屏资源大小，降低界面请求耗时
		网络优化
			 1. assets静态资源已升级到HTTP2，提高下载并发数量
			2. 外部依赖使用CDN加速
				pdf-service-me服务优化：推动运维走CDN，提升了现场打印功能体验，提高用户满意度
	页面稳定性
		sonar错误治理
			1. 不规范数控制在700内：wms-me 不合规数 2055 降至 980；mot-me 不合规数  1450 降至 560
			2. bug数清0： wms-me剩余4个，mot-me已清0
		uem错误治理
			1. wms-me、mot-me JS错误影响用户率需要降到10%以下， 目前为wms-me为 55%， mot-me为77%