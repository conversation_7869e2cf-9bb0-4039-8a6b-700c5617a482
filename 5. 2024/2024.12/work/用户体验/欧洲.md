# 欧洲-用户体验
欧洲-用户体验
	用户体验问题搜集
		向产品或实施搜集；
			1、已发送用户体验诉求搜集表格，请求产品协助填写搜集；
		向交互设计师搜集；
			2、已拉通会议寻求设计师帮忙指导；
	交互设计优化
		界面布局与导航
			1、优化标签导航栏;
			2、修改高级搜索，查询和清空按钮居右上布局，方便在表单查询条件较多时进行操作；
			3、操作按钮分主次，按钮大于5个要进行分组（需要产品配合）；
			4、数量和金额列字段右对齐方便对比位数；
		交互控件与反馈
			1、下拉框都需要增加前端筛选，支持模糊匹配，为用户提供更流畅和高效的交互体验；
			2、下拉框、文本框都需要增加清空按钮，方便再次选择和输入，提高操作效率；
			3、功能按钮需要增加loading进行节流处理，避免可能因重复提交相同请求而导致的数据错误或不一致；
			4、操作成功或失败等需要有提示信息进行反馈，加强用户对应用行为的预期和理解；
			6、按钮置灰逻增加提示，让用户明确知晓在什么条件下可以进行探作
	视觉设计提升
		色彩与字体
			1、按钮颜色、文字颜色统一为使用shineout组件库提供的颜色
			2、mot-eu的modal弹窗补充error、info、success等类型颜色和icon，进一步丰富其表现力和用户交互体验；
		图形与图标
			1、操作按钮都需要添加并统一icon图标
				下载按钮 download 上传按钮 upload
				新增按钮 plus 删除按钮 delete 编辑按钮 edit 导出按钮 export
				查询按钮 search 打印按钮 print 刷新按钮 refresh1
	页面性能优化
		资源加载优化
			1、升级uem-sdk 2.x 方便数据搜集
			2、压缩CSS、JavaScript文件和合并小文件减少HTTP请求次数
			3、资源或数据预加载，提前加载用户将要需要的资源
			4、通过串形请求改并行请求，异步请求接口数据
			5、利用Service Worker、sessionStorage、localStorage、indexDB拦截或缓存网络请求、缓存数据和静态资源
			6、sso-sdk接入，减少登录时的ulp跳转
		渲染优化
			1、优化JavaScript执行和页面样式、减少重绘和回流
		网络优化
			1、将websocket应用于看板、数据导出页面等场景
			2、升级HTTP2协议
				1、assets静态资源已升级到HTTP2
			3、外部依赖使用CDN加速
			4、接口超时拦截，监控
	系统可维护性提升
		移除废弃代码、配置、移除废弃依赖库和更新依赖库
			1、移除废弃页面&菜单
			2、移除废弃的Apollo配置
			3、移除mot-eu的jquery或其他废弃依赖库
			4、cloud-message的messageContainer升级，减少非必要的indexDB使用
		优化CSS样式
			1、类选择器命名，属性必须单行
		优化JS代码
			1、代码行不超500，超过500行需要拆分；
			2、变量驼峰命名，常量大写下划线连接，文件中横线连接命名，未使用变量移除，函数必须有注释
			3、异步函数避免使用Generator函数，使用promise，async await
			4、将通用的方法和函数封装至组件库中进行复用
			5、localStorage、sessionStorage 使用公共方法进行创建
			6、promise.all请求的报错处理统一使用handleListMsg方法接收进行处理
		bbl不规范词条治理
			1、不能包含非中文、变量、数字、枚举值、react组件、空格等可能变动的字符，须使用占位符
	系统稳定性提升
		uem报错治理
			1、wms-eu、mot-eu、wsk-eu JS错误影响用户率需要降到10%以下
		sonar错误治理
			1、wms-eu检测出的38个bug需要解决
			2、mot-eu检测出的12个bug需要解决
		安全漏洞修复
			Xss漏洞规避
		蓝绿优化
			1、增加版本更新组件自定义延迟更新机制；